package digitization.digitization.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class EmployeeListDto {

    private Long id;
    private String ecpfNumber;
    private String panNumber;
    private String employeeName;
    private String email;
    private String mobileNumber;
    private String registerNumber;
    private String section;
    private String currentDesignation;
    private LocalDate dateOfEntry;
    private String district;

    // Employee status fields - complete status table data
    private String status;
    private String approvedBy;
    private String rejectedBy;
    private String remarks;
    private String createdBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public EmployeeListDto() {
    }

    public EmployeeListDto(Long id, String ecpfNumber, String panNumber, String employeeName, String email, String registerNumber, String section, String currentDesignation, LocalDate dateOfEntry, String district) {
        this.id = id;
        this.ecpfNumber = ecpfNumber;
        this.panNumber = panNumber;
        this.employeeName = employeeName;
        this.email = email;
        this.registerNumber = registerNumber;
        this.section = section;
        this.currentDesignation = currentDesignation;
        this.dateOfEntry = dateOfEntry;
        this.district = district;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEcpfNumber() {
        return ecpfNumber;
    }

    public void setEcpfNumber(String ecpfNumber) {
        this.ecpfNumber = ecpfNumber;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getRegisterNumber() {
        return registerNumber;
    }

    public void setRegisterNumber(String registerNumber) {
        this.registerNumber = registerNumber;
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getCurrentDesignation() {
        return currentDesignation;
    }

    public void setCurrentDesignation(String currentDesignation) {
        this.currentDesignation = currentDesignation;
    }

    public LocalDate getDateOfEntry() {
        return dateOfEntry;
    }

    public void setDateOfEntry(LocalDate dateOfEntry) {
        this.dateOfEntry = dateOfEntry;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public String getRejectedBy() {
        return rejectedBy;
    }

    public void setRejectedBy(String rejectedBy) {
        this.rejectedBy = rejectedBy;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
