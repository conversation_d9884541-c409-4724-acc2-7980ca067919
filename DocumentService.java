package digitization.digitization.services;

import digitization.digitization.dto.DocumentDto;
import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.Document;
import digitization.digitization.module.Employee;
import digitization.digitization.repository.DocumentRepository;
import digitization.digitization.repository.EmployeeRepo;
import digitization.digitization.repository.ProfileRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DocumentService {

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private EmployeeRepo employeeRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private FileStorageService fileStorageService;

    // New method for file system storage with DTO response
    public DocumentDto uploadDocument(MultipartFile file, Long employeeId) {
        // Validate employee exists
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + employeeId, null, null));

        // Store file on server
        digitization.digitization.dto.FileStorageResult storageResult = fileStorageService.storeFile(file, employeeId);

        // Create document entity
        Document document = new Document();
        document.setEmployee(employee);
        document.setFileName(file.getOriginalFilename());
        document.setFileType(file.getContentType());
        document.setFileSize(file.getSize());
        document.setFilePath(storageResult.getRelativePath());
        document.setFileUrl(storageResult.getFileUrl());

        // Save to database
        Document savedDocument = documentRepository.save(document);

        return convertToDto(savedDocument);
    }

    public DocumentDto createDocumentFromBase64(String base64Content, String fileName, String fileType, Long employeeId, String documentName, String description) {
        // Validate employee exists
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + employeeId, null, null));

        // Store file on server from Base64
        digitization.digitization.dto.FileStorageResult storageResult = fileStorageService.storeFileFromBase64(base64Content, fileName, fileType, employeeId);

        // Create document entity
        Document document = new Document();
        document.setEmployee(employee);
        document.setFileName(fileName);
        document.setFileType(fileType);
        document.setFileSize((long) java.util.Base64.getDecoder().decode(base64Content).length);
        document.setFilePath(storageResult.getRelativePath());
        document.setFileUrl(storageResult.getFileUrl());
//        document.setDocumentName(documentName != null ? documentName : fileName);
//        document.setDescription(description);

        // Save to database
        Document savedDocument = documentRepository.save(document);

        return convertToDto(savedDocument);
    }

    public DocumentDto createDocumentPlaceholder(Long employeeId, String fileName, String fileType) {
        // Validate employee exists
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + employeeId, null, null));

        // Create employee-specific directory
        String employeeDir = "employee_" + employeeId;
        java.nio.file.Path dirPath = java.nio.file.Paths.get("uploads", employeeDir);
        try {
            java.nio.file.Files.createDirectories(dirPath);
        } catch (java.io.IOException e) {
            throw new RuntimeException("Could not create employee directory", e);
        }

        // Create document entity without actual file
        Document document = new Document();
        document.setEmployee(employee);
        document.setFileName(fileName);
        document.setFileType(fileType != null ? fileType : "application/pdf");
        document.setFileSize(0L); // Will be updated when file is uploaded
        document.setFilePath(null); // Will be set when file is uploaded
        document.setFileUrl(null); // Will be set when file is uploaded

        // Save to database
        Document savedDocument = documentRepository.save(document);

        return convertToDto(savedDocument);
    }

    // Legacy method for backward compatibility (stores in database as byte array)
    public Document storeDocument(Long employeeId, MultipartFile file) throws java.io.IOException {
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + employeeId, null, null));

        Document document = new Document();
        document.setFileName(file.getOriginalFilename());
        document.setFileType(file.getContentType());
        document.setFileSize(file.getSize());
        document.setEmployee(employee);

        // For legacy compatibility, we'll store the file path but also provide the data
        // This is a temporary solution - ideally the old controller should be updated
        digitization.digitization.dto.FileStorageResult storageResult = fileStorageService.storeFile(file, employeeId);
        document.setFilePath(storageResult.getRelativePath());
        document.setFileUrl(storageResult.getFileUrl());

        return documentRepository.save(document);
    }

    public List<DocumentDto> getDocumentsByEmployeeId(Long employeeId) {
        List<Document> documents = documentRepository.findByEmployee_Id(employeeId);
        return documents.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    public List<DocumentDto> getPdfDocumentsByEmployeeId(Long employeeId) {
        List<Document> documents = documentRepository.findPdfDocumentsByEmployeeId(employeeId);
        return documents.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    public DocumentDto getDocumentById(Long documentId) {
        Document document = documentRepository.findById(documentId)
                .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + documentId, null, null));
        return convertToDto(document);
    }

    // Legacy method for backward compatibility
    public Document getDocument(Long id) {
        return documentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + id, null, null));
    }

    public Resource downloadDocument(Long documentId) {
        Document document = documentRepository.findById(documentId)
                .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + documentId, null, null));

        return fileStorageService.loadFileAsResource(document.getFilePath());
    }

    public Resource downloadDocumentByEmployeeIdAndFileName(Long employeeId, String fileName) {
        Document document = documentRepository.findByEmployeeIdAndFileName(employeeId, fileName)
                .orElseThrow(() -> new ResourceNotFoundException("Document not found for employee: " + employeeId + " with filename: " + fileName, null, null));

        return fileStorageService.loadFileAsResource(document.getFilePath());
    }

    public boolean deleteDocument(Long documentId) {
        Optional<Document> documentOpt = documentRepository.findById(documentId);
        if (documentOpt.isPresent()) {
            Document document = documentOpt.get();

            // Delete file from server
            boolean fileDeleted = fileStorageService.deleteFile(document.getFilePath());

            // Delete from database
            documentRepository.deleteById(documentId);

            return fileDeleted;
        }
        return false;
    }

    public List<DocumentDto> getAllDocuments() {
        List<Document> documents = documentRepository.findAll();
        return documents.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    public List<DocumentDto> searchDocuments(String keyword) {
        List<Document> documents = documentRepository.searchDocuments(keyword);
        return documents.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }



    public DocumentDto updateDocumentWithFile(Long documentId, MultipartFile file) {
        Document document = documentRepository.findById(documentId)
                .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + documentId, null, null));

        // Store file on server
        digitization.digitization.dto.FileStorageResult storageResult = fileStorageService.storeFile(file, document.getEmployee().getId());

        // Update document with file information
        document.setFileName(file.getOriginalFilename());
        document.setFileType(file.getContentType());
        document.setFileSize(file.getSize());
        document.setFilePath(storageResult.getRelativePath());
        document.setFileUrl(storageResult.getFileUrl());

        Document updatedDocument = documentRepository.save(document);
        return convertToDto(updatedDocument);
    }

    public String uploadPhotoAndUpdateEmployee(Long employeeId, MultipartFile file) {
        // Validate employee exists
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + employeeId, null, null));

        // Store file on server
        digitization.digitization.dto.FileStorageResult storageResult = fileStorageService.storeFile(file, employeeId);

        // Check if photo document already exists for this employee
        java.util.List<Document> existingPhotos = documentRepository.findByEmployee_IdAndFileNameContaining(employeeId, "photo");
        Document document;

        if (!existingPhotos.isEmpty()) {
            // Update existing photo document
            document = existingPhotos.get(0);
            document.setFileName(file.getOriginalFilename());
            document.setFileType(file.getContentType());
            document.setFileSize(file.getSize());
            document.setFilePath(storageResult.getRelativePath());
            document.setFileUrl(storageResult.getFileUrl());
        } else {
            // Create new photo document
            document = new Document();
            document.setEmployee(employee);
            document.setFileName("photo_" + file.getOriginalFilename());
            document.setFileType(file.getContentType());
            document.setFileSize(file.getSize());
            document.setFilePath(storageResult.getRelativePath());
            document.setFileUrl(storageResult.getFileUrl());
        }

        // Save document to database
        documentRepository.save(document);

        // Update employee's profile photo URL
        employee.setProfilePhoto(storageResult.getFileUrl());
        employeeRepository.save(employee);

        // Also update profile table's profile photo URL
        try {
            java.util.Optional<digitization.digitization.module.Profile> profileOpt = profileRepository.findByEmployeeId(employeeId);
            if (profileOpt.isPresent()) {
                digitization.digitization.module.Profile profile = profileOpt.get();
                profile.setProfilephoto(storageResult.getFileUrl());
                profileRepository.save(profile);
            }
        } catch (Exception e) {
            // Log error but don't fail the photo upload
            System.out.println("Warning: Could not update profile photo in profile table: " + e.getMessage());
        }

        return storageResult.getFileUrl();
    }

    public String uploadSignatureAndUpdateEmployee(Long employeeId, MultipartFile file) {
        // Validate employee exists
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + employeeId, null, null));

        // Store file on server
        digitization.digitization.dto.FileStorageResult storageResult = fileStorageService.storeFile(file, employeeId);

        // Check if signature document already exists for this employee
        java.util.List<Document> existingSignatures = documentRepository.findByEmployee_IdAndFileNameContaining(employeeId, "signature");
        Document document;

        if (!existingSignatures.isEmpty()) {
            // Update existing signature document
            document = existingSignatures.get(0);
            document.setFileName(file.getOriginalFilename());
            document.setFileType(file.getContentType());
            document.setFileSize(file.getSize());
            document.setFilePath(storageResult.getRelativePath());
            document.setFileUrl(storageResult.getFileUrl());
        } else {
            // Create new signature document
            document = new Document();
            document.setEmployee(employee);
            document.setFileName("signature_" + file.getOriginalFilename());
            document.setFileType(file.getContentType());
            document.setFileSize(file.getSize());
            document.setFilePath(storageResult.getRelativePath());
            document.setFileUrl(storageResult.getFileUrl());
        }

        // Save document to database
        documentRepository.save(document);

        // Note: Signature field removed from Employee entity
        // Signature is now stored only in documents table

        return storageResult.getFileUrl();
    }

    public List<DocumentDto> getDocumentPlaceholdersByEmployeeId(Long employeeId) {
        List<Document> documents = documentRepository.findByEmployee_Id(employeeId);
        return documents.stream()
                .filter(doc -> doc.getFilePath() == null) // Only placeholders without actual files
                .map(this::convertToDto)
                .collect(java.util.stream.Collectors.toList());
    }



    private DocumentDto convertToDto(Document document) {
        DocumentDto dto = new DocumentDto();
        dto.setId(document.getId());
        dto.setEmployeeId(document.getEmployee().getId());
        dto.setEmployeeName(document.getEmployee().getEmployeeName());
        dto.setFileName(document.getFileName());
        dto.setFileType(document.getFileType());
        dto.setFileSize(document.getFileSize());
        dto.setFilePath(document.getFilePath());
        dto.setFileUrl(document.getFileUrl());
        dto.setUploadDate(document.getUploadDate());
        return dto;
    }
}
