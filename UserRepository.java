package digitization.digitization.repository;

import digitization.digitization.enums.DeleteStatus;
import digitization.digitization.module.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
    <PERSON>olean existsByusername(String username);
    Boolean existsBymobile(String mobile);
    Boolean existsByemail(String email);



    Page<User> findByStatus(DeleteStatus deleteStatus, Pageable pageable);

    Long countByStatus(DeleteStatus active);
//    Page<User> findByUserId(Pageable pageable, String userId);

//    <T> Optional<T> findByusername(String username);

    @Query("SELECT COUNT(u) FROM User u JOIN u.role r WHERE r.role = digitization.digitization.enums.Roles.ADMIN")
    long countAdmins();

    // Method to count users with the OPERATOR role
    @Query("SELECT COUNT(u) FROM User u JOIN u.role r WHERE r.role = digitization.digitization.enums.Roles.OPERATOR")
    long countOperators();
}
