package digitization.digitization.converter.educationqualification;

import digitization.digitization.dto.EducationQualificationDto;
import digitization.digitization.module.EducationQualification;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class EducationQualificationToDto implements Converter<EducationQualification, EducationQualificationDto> {
    @Override
    public EducationQualificationDto convert(EducationQualification educationQualification) {
        EducationQualificationDto educationQualificationDto = new EducationQualificationDto();
        educationQualificationDto.setId(educationQualification.getId());
        educationQualificationDto.setQualification(educationQualification.getQualification());
        educationQualificationDto.setCoursename(educationQualification.getCoursename());
        educationQualificationDto.setSchoolname(educationQualification.getSchoolname());
        educationQualificationDto.setCollegename(educationQualification.getCollegename());
        educationQualificationDto.setUniversityname(educationQualification.getUniversityname());
        educationQualificationDto.setSpecialization(educationQualification.getSpecialization());
        return educationQualificationDto;
    }
}
