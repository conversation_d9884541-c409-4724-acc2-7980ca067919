package digitization.digitization.common;

import digitization.digitization.module.User;
import digitization.digitization.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class SuccessHandler {

    @Autowired
    private UserRepository userRepo;


    public void updateUserLogin(String username, String clientIpAddress, LocalDateTime lastLogin){
        User user = userRepo.findByUsername(username).orElseThrow(() ->
                new UsernameNotFoundException("UserName or Password wrong  : ")
        );
//        user.setLastLogin(lastLogin);
//        user.setClientIpAddress(clientIpAddress);

        User loginUser =  userRepo.save(user);
        if(loginUser != null){
            System.out.println("loginUser"+loginUser);
        }else{
            System.out.println("faild");
        }
    }
}
