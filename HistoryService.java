package digitization.digitization.services;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.ServiceHistory;
import digitization.digitization.repository.ServiceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import digitization.digitization.repository.EmployeeRepo;
import digitization.digitization.module.Employee;

import java.util.List;

@Service
public class HistoryService {
    @Autowired
    private ServiceRepository serviceRepository;

    @Autowired
    private EmployeeRepo employeeRepository;

    public ServiceHistory addServiceHistory(Long employeeId, ServiceHistory serviceHistory) {
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + employeeId, null, null));

        serviceHistory.setEmployee(employee);
        return serviceRepository.save(serviceHistory);
    }

    public List<ServiceHistory> getServiceHistoryByEmployeeId(Long employeeId) {
        return serviceRepository.findByEmployeeIdOrderByDateDesc(employeeId);
    }

    public ServiceHistory updateServiceHistory(Long id, ServiceHistory serviceHistoryDetails) {
        ServiceHistory serviceHistory = serviceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Service history not found with id: " + id, null, null));

        serviceHistory.setDate(serviceHistoryDetails.getDate());
        serviceHistory.setType(serviceHistoryDetails.getType());
        serviceHistory.setStatus(serviceHistoryDetails.getStatus());
        serviceHistory.setAppointmenttype(serviceHistoryDetails.getAppointmenttype());
        serviceHistory.setModeofappointment(serviceHistoryDetails.getModeofappointment());
        serviceHistory.setDateofappointment(serviceHistoryDetails.getDateofappointment());
        serviceHistory.setProceedingorderdate(serviceHistoryDetails.getProceedingorderdate());
        serviceHistory.setProceedingorderno(serviceHistoryDetails.getProceedingorderno());
        serviceHistory.setJoiningdate(serviceHistoryDetails.getJoiningdate());
        serviceHistory.setPromoteddate(serviceHistoryDetails.getPromoteddate());
        serviceHistory.setFromdesignation(serviceHistoryDetails.getFromdesignation());
        serviceHistory.setTopromoted(serviceHistoryDetails.getTopromoted());
        serviceHistory.setFromdate(serviceHistoryDetails.getFromdate());
        serviceHistory.setTodate(serviceHistoryDetails.getTodate());
        serviceHistory.setTypeofincrement(serviceHistoryDetails.getTypeofincrement());
        serviceHistory.setFromplace(serviceHistoryDetails.getFromplace());
        serviceHistory.setToplace(serviceHistoryDetails.getToplace());
        serviceHistory.setDesignation(serviceHistoryDetails.getDesignation());
        serviceHistory.setOriginaldesignation(serviceHistoryDetails.getOriginaldesignation());
        serviceHistory.setParentdepartment(serviceHistoryDetails.getParentdepartment());
        serviceHistory.setPunishmenttype(serviceHistoryDetails.getPunishmenttype());
        serviceHistory.setCasedetails(serviceHistoryDetails.getCasedetails());
        serviceHistory.setPunishmentdate(serviceHistoryDetails.getPunishmentdate());

        return serviceRepository.save(serviceHistory);
    }

    public void deleteServiceHistory(Long id) {
        serviceRepository.deleteById(id);
    }
}
