package digitization.digitization.module;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.time.LocalDate;

@Entity
@Table(name = "punishment_details")
public class PunishmentDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Punishment type is required")
    private String punishmenttype;

    @NotNull(message = "Date is required")
    private LocalDate date;

//    @NotBlank(message = "Case details are required")
    @Column(columnDefinition = "TEXT")
    private String casedetails;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "employee_id")
    private Employee employee;

    public PunishmentDetails() {
    }

    public PunishmentDetails(Long id, String punishmenttype, LocalDate date, String casedetails) {
        this.id = id;
        this.punishmenttype = punishmenttype;
        this.date = date;
        this.casedetails = casedetails;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPunishmenttype() {
        return punishmenttype;
    }

    public void setPunishmenttype(String punishmenttype) {
        this.punishmenttype = punishmenttype;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getCasedetails() {
        return casedetails;
    }

    public void setCasedetails(String casedetails) {
        this.casedetails = casedetails;
    }

    public Employee getEmployee() {
        return employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }
}
