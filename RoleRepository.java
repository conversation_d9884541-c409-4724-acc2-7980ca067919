package digitization.digitization.repository;

import digitization.digitization.enums.RoleStatus;
import digitization.digitization.enums.Roles;
import digitization.digitization.module.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {

    Role findByRole(Roles roles);

//    Optional<Role> findByRole(Role role);


//    List<Role> findByStatus(RoleStatus roleStatus);

    List<Role> findByRoleNotAndStatus(Roles sales_manager, RoleStatus active);
}
