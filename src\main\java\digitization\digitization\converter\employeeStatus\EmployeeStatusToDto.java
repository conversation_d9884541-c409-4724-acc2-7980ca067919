package digitization.digitization.converter.employeeStatus;

import digitization.digitization.dto.EmployeeStatusDto;
import digitization.digitization.module.EmployeeStatus;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class EmployeeStatusToDto implements Converter<EmployeeStatus, EmployeeStatusDto> {

    @Override
    public EmployeeStatusDto convert(EmployeeStatus employeeStatus) {
        EmployeeStatusDto dto = new EmployeeStatusDto();
        dto.setId(employeeStatus.getId());
        dto.setEmpId(employeeStatus.getEmpId());
        dto.setStatus(employeeStatus.getStatus());
        dto.setApprovedBy(employeeStatus.getApprovedBy());
        dto.setRejectedBy(employeeStatus.getRejectedBy());
        dto.setRemarks(employeeStatus.getRemarks());
        dto.setCreatedAt(employeeStatus.getCreatedAt());
        dto.setUpdatedAt(employeeStatus.getUpdatedAt());
        return dto;
    }
}

