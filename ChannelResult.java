package digitization.digitization.response.responce;

import com.fasterxml.jackson.annotation.JsonProperty;

public class   ChannelResult {

    @JsonProperty("channelid")
    public int channelid;
    @JsonProperty("channelname")
    public String channelname;

    public int status;
    @JsonProperty("channelip")
    public String channelip;
    @JsonProperty("channeltype")
    public int channeltype;
    @JsonProperty("snapurl")
    public String snapurl;
    @JsonProperty("majorurl")
    public String majorurl;
    @JsonProperty("minorurl")
    public String minorurl;
    @JsonProperty("analyticurl")
    public String analyticurl;
    @JsonProperty("username")
    public String username;
    @JsonProperty("password")
    public String password;
    @JsonProperty("latitude")
    public double latitude;
    @JsonProperty("longitude")
    public double longitude;
    @JsonProperty("description")
    public String description;
    @JsonProperty("recordingserverid")
    public String recordingserverid;
    @JsonProperty("recordingservername")
    public String recordingservername;

    public boolean checked = false;

    public int getChannelid() {
        return channelid;
    }

    public void setChannelid(int channelid) {
        this.channelid = channelid;
    }

    public String getChannelname() {
        return channelname;
    }

    public void setChannelname(String channelname) {
        this.channelname = channelname;
    }

    public String getChannelip() {
        return channelip;
    }

    public void setChannelip(String channelip) {
        this.channelip = channelip;
    }

    public int getChanneltype() {
        return channeltype;
    }

    public void setChanneltype(int channeltype) {
        this.channeltype = channeltype;
    }

    public String getSnapurl() {
        return snapurl;
    }

    public void setSnapurl(String snapurl) {
        this.snapurl = snapurl;
    }

    public String getMajorurl() {
        return majorurl;
    }

    public void setMajorurl(String majorurl) {
        this.majorurl = majorurl;
    }

    public String getMinorurl() {
        return minorurl;
    }

    public void setMinorurl(String minorurl) {
        this.minorurl = minorurl;
    }

    public String getAnalyticurl() {
        return analyticurl;
    }

    public void setAnalyticurl(String analyticurl) {
        this.analyticurl = analyticurl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRecordingserverid() {
        return recordingserverid;
    }

    public void setRecordingserverid(String recordingserverid) {
        this.recordingserverid = recordingserverid;
    }

    public String getRecordingservername() {
        return recordingservername;
    }

    public void setRecordingservername(String recordingservername) {
        this.recordingservername = recordingservername;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }


    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}



