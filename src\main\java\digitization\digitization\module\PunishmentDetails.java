package digitization.digitization.module;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;

@Entity
@Table(name = "punishment_details")
public class PunishmentDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Punishment type is required")
    private String punishmenttype;

    @NotNull(message = "Date is required")
    private LocalDate date;

//    @NotBlank(message = "Case details are required")
    @Column(columnDefinition = "TEXT")
    private String casedetails;
    private Date caseDate;
    private String caseNumber;
    private String description;

    @ElementCollection
    @CollectionTable(name = "punishment_persons_involved", joinColumns = @JoinColumn(name = "punishment_id"))
    @Column(name = "person_name")
    private String[] personsInvolved;

    private String presentStatus;

    private LocalDate withHoldingFromDate;

    private LocalDate withHoldingToDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "employee_id")
    private Employee employee;

    public PunishmentDetails() {
    }

    public PunishmentDetails(Long id, String punishmenttype, LocalDate date, String casedetails, Date caseDate, String caseNumber, String description, String[] personsInvolved, String presentStatus, LocalDate withHoldingFromDate, LocalDate withHoldingToDate, Employee employee) {
        this.id = id;
        this.punishmenttype = punishmenttype;
        this.date = date;
        this.casedetails = casedetails;
        this.caseDate = caseDate;
        this.caseNumber = caseNumber;
        this.description = description;
        this.personsInvolved = personsInvolved;
        this.presentStatus = presentStatus;
        this.withHoldingFromDate = withHoldingFromDate;
        this.withHoldingToDate = withHoldingToDate;
        this.employee = employee;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPunishmenttype() {
        return punishmenttype;
    }

    public void setPunishmenttype(String punishmenttype) {
        this.punishmenttype = punishmenttype;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getCasedetails() {
        return casedetails;
    }

    public void setCasedetails(String casedetails) {
        this.casedetails = casedetails;
    }

    public Employee getEmployee() {
        return employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }

    public Date getCaseDate() {
        return caseDate;
    }

    public void setCaseDate(Date caseDate) {
        this.caseDate = caseDate;
    }

    public String getCaseNumber() {
        return caseNumber;
    }

    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String[] getPersonsInvolved() {
        return personsInvolved;
    }

    public void setPersonsInvolved(String[] personsInvolved) {
        this.personsInvolved = personsInvolved;
    }

    public String getPresentStatus() {
        return presentStatus;
    }

    public void setPresentStatus(String presentStatus) {
        this.presentStatus = presentStatus;
    }

    public LocalDate getWithHoldingFromDate() {
        return withHoldingFromDate;
    }

    public void setWithHoldingFromDate(LocalDate withHoldingFromDate) {
        this.withHoldingFromDate = withHoldingFromDate;
    }

    public LocalDate getWithHoldingToDate() {
        return withHoldingToDate;
    }

    public void setWithHoldingToDate(LocalDate withHoldingToDate) {
        this.withHoldingToDate = withHoldingToDate;
    }

    @Override
    public String toString() {
        return "PunishmentDetails{" +
                "id=" + id +
                ", punishmenttype='" + punishmenttype + '\'' +
                ", date=" + date +
                ", casedetails='" + casedetails + '\'' +
                ", caseDate=" + caseDate +
                ", caseNumber='" + caseNumber + '\'' +
                ", description='" + description + '\'' +
                ", personsInvolved=" + Arrays.toString(personsInvolved) +
                ", presentStatus='" + presentStatus + '\'' +
                ", withHoldingFromDate=" + withHoldingFromDate +
                ", withHoldingToDate=" + withHoldingToDate +
                '}';
    }
}
