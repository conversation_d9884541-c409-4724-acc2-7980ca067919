package digitization.digitization.services;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.Employee;
import digitization.digitization.module.Leave;
import digitization.digitization.repository.EmployeeRepo;
import digitization.digitization.repository.LeaveRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LeaveService {
    @Autowired
    private LeaveRepository leaveBalanceRepository;

    @Autowired
    private EmployeeRepo employeeRepository;

    public Leave addLeaveBalance(Long employeeId, Leave leaveBalance) {
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + employeeId, null, null));

        leaveBalance.setEmployee(employee);
        return leaveBalanceRepository.save(leaveBalance);
    }

    public List<Leave> getLeaveBalancesByEmployeeId(Long employeeId) {
        return leaveBalanceRepository.findByEmployeeId(employeeId);
    }

    public List<Leave> getAllLeaveBalances() {
        return leaveBalanceRepository.findAll();
    }

    public Leave getLeaveBalanceById(Long id) {
        return leaveBalanceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Leave balance not found with id: " + id, null, null));
    }

    public Leave updateLeaveBalance(Long id, Leave leaveBalanceDetails) {
        Leave leaveBalance = leaveBalanceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Leave balance not found with id: " + id, null, null));

        leaveBalance.setLeaveType(leaveBalanceDetails.getLeaveType());
        leaveBalance.setOpeningBalance(leaveBalanceDetails.getOpeningBalance());
        leaveBalance.setClosingBalance(leaveBalanceDetails.getClosingBalance());
        leaveBalance.setEntryDate(leaveBalanceDetails.getEntryDate());

        return leaveBalanceRepository.save(leaveBalance);
    }

    public void deleteLeaveBalance(Long id) {
        leaveBalanceRepository.deleteById(id);
    }
}
