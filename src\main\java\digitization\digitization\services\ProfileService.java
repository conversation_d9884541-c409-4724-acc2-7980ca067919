package digitization.digitization.services;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.Profile;
import digitization.digitization.repository.ProfileRepository;
import digitization.digitization.services.implementation.ProfileServ;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ProfileService implements ProfileServ {

    @Autowired
    ProfileRepository profileRepository;

    @Override
    public List<Profile> getProfileList() {
        List<Profile> list = profileRepository.findAll();
        return list;
    }

    @Override
    public Profile getProfileById(Long profileId) {
        Optional<Profile> optionalProfile = this.profileRepository.findById(profileId);
        if (!optionalProfile.isPresent()) {
            throw new ResourceNotFoundException("Profile not found with id: " + profileId, null, null);
        }
        return optionalProfile.get();
    }

    @Override
    public Profile createProfile(Profile profile) {
        Profile prof = new Profile();
        prof.setId(profile.getId());
        prof.setFatherName(profile.getFatherName());
        prof.setMotherName(profile.getMotherName());
        prof.setDateOfBirth(profile.getDateOfBirth());
        prof.setCommunity(profile.getCommunity());
        prof.setCaste(profile.getCaste());
        prof.setDistrict(profile.getDistrict());
        prof.setNativeplaceandtaluk(profile.getNativeplaceandtaluk());
        prof.setGender(profile.getGender());

        // Present address fields
        prof.setPresentDoorNo(profile.getPresentDoorNo());
        prof.setPresentBuildingName(profile.getPresentBuildingName());
        prof.setPresentStreetAddress(profile.getPresentStreetAddress());
        prof.setPresentCity(profile.getPresentCity());
        prof.setPresentPincode(profile.getPresentPincode());

        // Permanent address fields
        prof.setPermanentDoorNo(profile.getPermanentDoorNo());
        prof.setPermanentBuildingName(profile.getPermanentBuildingName());
        prof.setPermanentStreetAddress(profile.getPermanentStreetAddress());
        prof.setPermanentCity(profile.getPermanentCity());
        prof.setPermanentPincode(profile.getPermanentPincode());

        prof.setProfilephoto(profile.getProfilephoto());
        prof.setEmployeeType(profile.getEmployeeType());
        prof.setEmail(profile.getEmail());
        prof.setEmployee(profile.getEmployee());

        profileRepository.save(prof);
        return prof;
    }

    @Override
    public Profile updateProfile(Long id, Profile profileDetails) {
        Profile profile = profileRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Profile not found with id: " + id, null, null));

        profile.setFatherName(profileDetails.getFatherName());
        profile.setMotherName(profileDetails.getMotherName());
        profile.setDateOfBirth(profileDetails.getDateOfBirth());
        profile.setCommunity(profileDetails.getCommunity());
        profile.setCaste(profileDetails.getCaste());
        profile.setDistrict(profileDetails.getDistrict());
        profile.setNativeplaceandtaluk(profileDetails.getNativeplaceandtaluk());
        profile.setGender(profileDetails.getGender());

        // Present address fields
        profile.setPresentDoorNo(profileDetails.getPresentDoorNo());
        profile.setPresentBuildingName(profileDetails.getPresentBuildingName());
        profile.setPresentStreetAddress(profileDetails.getPresentStreetAddress());
        profile.setPresentCity(profileDetails.getPresentCity());
        profile.setPresentPincode(profileDetails.getPresentPincode());

        // Permanent address fields
        profile.setPermanentDoorNo(profileDetails.getPermanentDoorNo());
        profile.setPermanentBuildingName(profileDetails.getPermanentBuildingName());
        profile.setPermanentStreetAddress(profileDetails.getPermanentStreetAddress());
        profile.setPermanentCity(profileDetails.getPermanentCity());
        profile.setPermanentPincode(profileDetails.getPermanentPincode());

        profile.setProfilephoto(profileDetails.getProfilephoto());
        profile.setEmployeeType(profileDetails.getEmployeeType());
        profile.setEmail(profileDetails.getEmail());
        // Note: Employee relationship should not be updated in profile update
        // profile.setEmployee(profileDetails.getEmployee());


        return profileRepository.save(profile);
    }

    @Override
    public boolean deleteProfile(Long profileId) {
        try {
            Optional<Profile> profile = profileRepository.findById(profileId);
            if (profile.isPresent()) {
                profileRepository.deleteById(profileId);
                return true;
            } else {
                System.out.println("Profile not found with ID: " + profileId);
                return false;
            }
        } catch (Exception e) {
            System.out.println("Error deleting profile: " + e.getMessage());
            return false;
        }
    }

    @Override
    public Profile getProfileByEmail(String email) {
        Optional<Profile> optionalProfile = this.profileRepository.findByEmail(email);
        if (!optionalProfile.isPresent()) {
            throw new ResourceNotFoundException("Profile not found with email: " + email, null, null);
        }
        return optionalProfile.get();
    }
}
