package digitization.digitization.dto.userDto;

import digitization.digitization.common.UniqueIdGenerator;
import digitization.digitization.enums.DeleteStatus;
import digitization.digitization.module.Role;
import org.springframework.data.annotation.CreatedDate;

import java.time.LocalDateTime;

public class UserDto {


    private Long id;
    private String username;
    private String name;
    private String email;
    private String mobile;
    private String password;
    private String region;

    private boolean isActive;
    private boolean isAccountLocked;
    private int failedCount;
    private boolean isLoggedin;

    private String forgottenPasswordCode;
    private LocalDateTime forgottenPasswordTime;
    private String confirmPassword;
    private String userId = UniqueIdGenerator.getUserId();

    private Role role;
    private Long roleId;
    private String roleType;
    private DeleteStatus status;


    @CreatedDate
    private LocalDateTime createdAt;

    public UserDto() {
    }

    public UserDto(Long id, String username, String name, String email, String mobile, String password, String region, boolean isActive, boolean isAccountLocked, int failedCount, boolean isLoggedin, String forgottenPasswordCode, LocalDateTime forgottenPasswordTime, String confirmPassword, String userId, Role role, Long roleId, String roleType, DeleteStatus status, LocalDateTime createdAt) {
        this.id = id;
        this.username = username;
        this.name = name;
        this.email = email;
        this.mobile = mobile;
        this.password = password;
        this.region = region;
        this.isActive = isActive;
        this.isAccountLocked = isAccountLocked;
        this.failedCount = failedCount;
        this.isLoggedin = isLoggedin;
        this.forgottenPasswordCode = forgottenPasswordCode;
        this.forgottenPasswordTime = forgottenPasswordTime;
        this.confirmPassword = confirmPassword;
        this.userId = userId;
        this.role = role;
        this.roleId = roleId;
        this.roleType = roleType;
        this.status = status;
        this.createdAt = createdAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Role getRole() {
        return role;
    }

    public void setRole(Role role) {
        this.role = role;
    }

    public int getFailedCount() {
        return failedCount;
    }

    public void setFailedCount(int failedCount) {
        this.failedCount = failedCount;
    }

    public boolean isLoggedin() {
        return isLoggedin;
    }

    public void setLoggedin(boolean loggedin) {
        isLoggedin = loggedin;
    }

    public DeleteStatus getStatus() {
        return status;
    }

    public void setStatus(DeleteStatus status) {
        this.status = status;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public String getRoleType() {
        return roleType;
    }

    public void setRoleType(String roleType) {
        this.roleType = roleType;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public boolean isAccountLocked() {
        return isAccountLocked;
    }

    public void setAccountLocked(boolean accountLocked) {
        isAccountLocked = accountLocked;
    }

    public String getForgottenPasswordCode() {
        return forgottenPasswordCode;
    }

    public void setForgottenPasswordCode(String forgottenPasswordCode) {
        this.forgottenPasswordCode = forgottenPasswordCode;
    }

    public LocalDateTime getForgottenPasswordTime() {
        return forgottenPasswordTime;
    }

    public void setForgottenPasswordTime(LocalDateTime forgottenPasswordTime) {
        this.forgottenPasswordTime = forgottenPasswordTime;
    }

    public String getConfirmPassword() {
        return confirmPassword;
    }

    public void setConfirmPassword(String confirmPassword) {
        this.confirmPassword = confirmPassword;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}
