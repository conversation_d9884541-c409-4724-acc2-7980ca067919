package digitization.digitization.exception;

public class UserNotFoundException extends RuntimeException{

    private boolean status;
    private String message;
    private String info;

    public UserNotFoundException(boolean status,String message){
        super();
        this.status = status;
        this.message = message;
    }


    public UserNotFoundException(boolean status,String message,String info){
        super();
        this.status = status;
        this.message = message;
        this.info = info;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }


    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
