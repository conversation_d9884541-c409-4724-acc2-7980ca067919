package digitization.digitization.controller;

import digitization.digitization.module.ServiceHistory;
import digitization.digitization.services.HistoryService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("service")
public class ServiceContro {
    @Autowired
    private HistoryService serviceHistoryService;

    @PostMapping("/addServ/{employeeId}")
    public ServiceHistory addServiceHistory(@PathVariable Long employeeId, @Valid @RequestBody ServiceHistory serviceHistory) {
        return serviceHistoryService.addServiceHistory(employeeId, serviceHistory);
    }

    @GetMapping("/employee/{employeeId}")
    public List<ServiceHistory> getServiceHistoryByEmployee(@PathVariable Long employeeId) {
        return serviceHistoryService.getServiceHistoryByEmployeeId(employeeId);
    }

    @PutMapping("/updateService/{id}")
    public ServiceHistory updateServiceHistory(@PathVariable Long id, @Valid @RequestBody ServiceHistory serviceHistoryDetails) {
        return serviceHistoryService.updateServiceHistory(id, serviceHistoryDetails);
    }

    @DeleteMapping("/deleteServ/{id}")
    public ResponseEntity<?> deleteServiceHistory(@PathVariable Long id) {
        serviceHistoryService.deleteServiceHistory(id);
        return ResponseEntity.ok().build();
    }
}
