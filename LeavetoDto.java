package digitization.digitization.converter.leave;

import digitization.digitization.dto.LeaveDto;
import digitization.digitization.module.Leave;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class LeavetoDto implements Converter<Leave, LeaveDto> {
    @Override
    public LeaveDto convert(Leave leave) {
        LeaveDto leaveDto=new LeaveDto();
        leaveDto.setId(leave.getId());
        leaveDto.setLeaveType(leave.getLeaveType());
        leaveDto.setOpeningBalance(leave.getOpeningBalance());
        leaveDto.setClosingBalance(leave.getClosingBalance());
        leaveDto.setEntryDate(leave.getEntryDate());
        leaveDto.setCreatedAt(leave.getCreatedAt());
        return leaveDto;
    }
}
