package digitization.digitization.converter.employee;

import digitization.digitization.dto.EmployeeDto;
import digitization.digitization.module.Employee;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class DtotoEmployee implements Converter<EmployeeDto, Employee> {
    @Override
    public Employee convert(EmployeeDto employeeDto) {
        Employee employee=new Employee();
        employee.setId(employeeDto.getId());
        employee.setEcpfNumber(employeeDto.getEcpfNumber());
        employee.setPanNumber(employeeDto.getPanNumber());
        employee.setEmployeeName(employeeDto.getEmployeeName());
        employee.setFatherName(employeeDto.getFatherName());
        employee.setMotherName(employeeDto.getMotherName());
        employee.setReligion(employeeDto.getReligion());
        employee.setDistrict(employeeDto.getDistrict());
        employee.setNativePlaceAndTaluk(employeeDto.getNativePlaceAndTaluk());
        employee.setPersonalIdentificationmark1(employeeDto.getPersonalIdentificationmark1());
        employee.setPersonalIdentificationmark2(employeeDto.getPersonalIdentificationmark2());
        employee.setCurrentDesignation(employeeDto.getCurrentDesignation());
        employee.setDateOfEntry(employeeDto.getDateOfEntry());
        employee.setDateOfBirth(employeeDto.getDateOfBirth());
        employee.setCommunity(employeeDto.getCommunity());
        employee.setCaste(employeeDto.getCaste());
        employee.setMainEmployeeType(employeeDto.getMainEmployeeType());

        return employee;
    }
}
