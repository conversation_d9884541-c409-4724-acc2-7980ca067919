package digitization.digitization.dto;

public class AccountDetailsDto {

    private Long id;
    private String bankaccountnumber;
    private String ifsccode;
    private String bankname;
    private String uannumber;
    private String aadharnumber;

    public AccountDetailsDto() {
    }

    public AccountDetailsDto(Long id, String bankaccountnumber, String ifsccode, String bankname, String uannumber, String aadharnumber) {
        this.id = id;
        this.bankaccountnumber = bankaccountnumber;
        this.ifsccode = ifsccode;
        this.bankname = bankname;
        this.uannumber = uannumber;
        this.aadharnumber = aadharnumber;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBankaccountnumber() {
        return bankaccountnumber;
    }

    public void setBankaccountnumber(String bankaccountnumber) {
        this.bankaccountnumber = bankaccountnumber;
    }

    public String getIfsccode() {
        return ifsccode;
    }

    public void setIfsccode(String ifsccode) {
        this.ifsccode = ifsccode;
    }

    public String getBankname() {
        return bankname;
    }

    public void setBankname(String bankname) {
        this.bankname = bankname;
    }

    public String getUannumber() {
        return uannumber;
    }

    public void setUannumber(String uannumber) {
        this.uannumber = uannumber;
    }

    public String getAadharnumber() {
        return aadharnumber;
    }

    public void setAadharnumber(String aadharnumber) {
        this.aadharnumber = aadharnumber;
    }
}
