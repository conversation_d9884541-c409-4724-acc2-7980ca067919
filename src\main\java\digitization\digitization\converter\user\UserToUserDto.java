package digitization.digitization.converter.user;

import digitization.digitization.dto.userDto.UserDto;
import digitization.digitization.enums.Roles;
import digitization.digitization.module.Role;
import digitization.digitization.module.User;
import digitization.digitization.repository.RoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
public class UserToUserDto implements Converter<User, UserDto> {
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RoleRepository roleRepository;

    @Override
    public UserDto convert(User user) {

        UserDto userDto = new UserDto();
        userDto.setId(user.getId());
        userDto.setPassword(passwordEncoder.encode(user.getPassword()));
        userDto.setConfirmPassword(passwordEncoder.encode(user.getConfirmPassword()));
        userDto.setRegion(user.getRegion());
        userDto.setName(user.getName());
        userDto.setUsername(user.getUsername());
        userDto.setEmail(user.getEmail().trim());
        userDto.setMobile(user.getMobile());
        userDto.setActive(user.isActive());
        userDto.setUserId(user.getUserId());
        userDto.setRoleType(user.getRoleType());

        if(user.getRoleType()!=null){

            Role role =  roleRepository.findByRole(Roles.valueOf(user.getRoleType()));
            userDto.setRole(role);
        }
        return userDto;
    }
}
