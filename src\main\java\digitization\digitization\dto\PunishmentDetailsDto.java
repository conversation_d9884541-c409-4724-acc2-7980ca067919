package digitization.digitization.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDate;
import java.util.Date;

public class PunishmentDetailsDto {

    private Long id;

    @JsonProperty("punishmenttype")
    private String punishmenttype;

    @JsonProperty("date")
    private LocalDate date;

    @JsonProperty("casedetails")
    private String casedetails;
    private Date caseDate;
    private String caseNumber;
    private String description;

    @JsonProperty("personsInvolved")
    private String[] personsInvolved;

    private String presentStatus;

    private LocalDate withHoldingFromDate;

    private LocalDate withHoldingToDate;

    public PunishmentDetailsDto() {
    }

    public PunishmentDetailsDto(Long id, String punishmenttype, LocalDate date, String casedetails) {
        this.id = id;
        this.punishmenttype = punishmenttype;
        this.date = date;
        this.casedetails = casedetails;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPunishmenttype() {
        return punishmenttype;
    }

    public void setPunishmenttype(String punishmenttype) {
        this.punishmenttype = punishmenttype;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getCasedetails() {
        return casedetails;
    }

    public void setCasedetails(String casedetails) {
        this.casedetails = casedetails;
    }

    public Date getCaseDate() {
        return caseDate;
    }

    public void setCaseDate(Date caseDate) {
        this.caseDate = caseDate;
    }

    public String getCaseNumber() {
        return caseNumber;
    }

    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String[] getPersonsInvolved() {
        return personsInvolved;
    }

    public void setPersonsInvolved(String[] personsInvolved) {
        this.personsInvolved = personsInvolved;
    }

    public String getPresentStatus() {
        return presentStatus;
    }

    public void setPresentStatus(String presentStatus) {
        this.presentStatus = presentStatus;
    }

    public LocalDate getWithHoldingFromDate() {
        return withHoldingFromDate;
    }

    public void setWithHoldingFromDate(LocalDate withHoldingFromDate) {
        this.withHoldingFromDate = withHoldingFromDate;
    }

    public LocalDate getWithHoldingToDate() {
        return withHoldingToDate;
    }

    public void setWithHoldingToDate(LocalDate withHoldingToDate) {
        this.withHoldingToDate = withHoldingToDate;
    }

    // Backward compatibility methods for single person involved
    @JsonIgnore
    public String getPersonInvolved() {
        if (personsInvolved != null && personsInvolved.length > 0) {
            return personsInvolved[0];
        }
        return null;
    }

    @JsonIgnore
    public void setPersonInvolved(String personInvolved) {
        if (personInvolved != null) {
            this.personsInvolved = new String[]{personInvolved};
        } else {
            this.personsInvolved = null;
        }
    }

    @Override
    public String toString() {
        return "PunishmentDetailsDto{" +
                "id=" + id +
                ", punishmenttype='" + punishmenttype + '\'' +
                ", date=" + date +
                ", casedetails='" + casedetails + '\'' +
                ", caseDate=" + caseDate +
                ", caseNumber='" + caseNumber + '\'' +
                ", description='" + description + '\'' +
                ", personsInvolved=" + java.util.Arrays.toString(personsInvolved) +
                ", presentStatus='" + presentStatus + '\'' +
                ", withHoldingFromDate=" + withHoldingFromDate +
                ", withHoldingToDate=" + withHoldingToDate +
                '}';
    }
}
