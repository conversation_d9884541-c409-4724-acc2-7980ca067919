package digitization.digitization.services.implementation;

import digitization.digitization.module.TrainingDetails;

import java.time.LocalDate;
import java.util.List;

public interface TrainingDetailsServ {
    List<TrainingDetails> getTrainingDetailsList();
    TrainingDetails getTrainingDetailsById(Long id);
    TrainingDetails createTrainingDetails(TrainingDetails trainingDetails);
    TrainingDetails updateTrainingDetails(Long id, TrainingDetails trainingDetailsDetails);
    boolean deleteTrainingDetails(Long trainingDetailsId);
    List<TrainingDetails> getTrainingDetailsByType(String trainingtype);
    List<TrainingDetails> getTrainingDetailsByDate(LocalDate date);
    List<TrainingDetails> getTrainingDetailsByDateRange(LocalDate startDate, LocalDate endDate);
    List<TrainingDetails> getTrainingDetailsAfterDate(LocalDate date);
    List<TrainingDetails> getTrainingDetailsBeforeDate(LocalDate date);
    List<TrainingDetails> searchTrainingDetailsByType(String keyword);
    List<TrainingDetails> getAllTrainingDetailsOrderByDateDesc();
    List<TrainingDetails> getAllTrainingDetailsOrderByDateAsc();
    List<TrainingDetails> getTrainingDetailsByTypeOrderByDateDesc(String trainingtype);
    List<TrainingDetails> getTrainingDetailsByTypeOrderByDateAsc(String trainingtype);
    List<String> getDistinctTrainingTypes();
}
