package digitization.digitization.controller;


import digitization.digitization.module.Document;
import digitization.digitization.services.DocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("document")
@CrossOrigin(value = "*")
public class DocumentContro {

    @Autowired
    private DocumentService documentService;

    @PostMapping("/upload/{employeeId}")
    public Document uploadDocument(@PathVariable Long employeeId, @RequestParam("file") MultipartFile file) throws IOException {
        return documentService.storeDocument(employeeId, file);
    }

    @PostMapping("/upload/photo/{employeeId}")
    public ResponseEntity<String> uploadPhoto(@PathVariable Long employeeId, @RequestParam("file") MultipartFile file) throws IOException {
        String fileUrl = documentService.uploadPhotoAndUpdateEmployee(employeeId, file);
        return ResponseEntity.ok("Photo uploaded successfully. URL: " + fileUrl);
    }

    @PostMapping("/upload/signature/{employeeId}")
    public ResponseEntity<String> uploadSignature(@PathVariable Long employeeId, @RequestParam("file") MultipartFile file) throws IOException {
        String fileUrl = documentService.uploadSignatureAndUpdateEmployee(employeeId, file);
        return ResponseEntity.ok("Signature uploaded successfully. URL: " + fileUrl);
    }

    @GetMapping("/getDoc/{id}")
    public ResponseEntity<byte[]> getDocument(@PathVariable Long id) throws IOException {
        Document document = documentService.getDocument(id);

        // Read file from file system
        org.springframework.core.io.Resource resource = documentService.downloadDocument(id);
        byte[] data = resource.getInputStream().readAllBytes();

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(document.getFileType()))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + document.getFileName() + "\"")
                .body(data);
    }

    @GetMapping("/employee/{employeeId}")
    public List<Document> getDocumentsByEmployee(@PathVariable Long employeeId) {
        // Convert DTOs back to Documents for backward compatibility
        List<digitization.digitization.dto.DocumentDto> dtos = documentService.getDocumentsByEmployeeId(employeeId);
        return dtos.stream()
                .map(this::convertDtoToDocument)
                .collect(java.util.stream.Collectors.toList());
    }

    private Document convertDtoToDocument(digitization.digitization.dto.DocumentDto dto) {
        Document document = new Document();
        document.setId(dto.getId());
        document.setFileName(dto.getFileName());
        document.setFileType(dto.getFileType());
        document.setFileSize(dto.getFileSize());
        document.setFilePath(dto.getFilePath());
        document.setUploadDate(dto.getUploadDate());
        // Employee will be loaded lazily if needed
        return document;
    }

    @DeleteMapping("/delDoc/{id}")
    public ResponseEntity<?> deleteDocument(@PathVariable Long id) {
        documentService.deleteDocument(id);
        return ResponseEntity.ok().build();
    }

}
