package digitization.digitization.services;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.Nominee;
import digitization.digitization.repository.NomineeRepository;
import digitization.digitization.services.implementation.NomineeServ;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class NomineeService implements NomineeServ {

    @Autowired
    NomineeRepository nomineeRepository;

    @Override
    public List<Nominee> getNomineeList() {
        List<Nominee> list = nomineeRepository.findAll();
        return list;
    }

    @Override
    public Nominee getNomineeById(Long nomineeId) {
        Optional<Nominee> optionalNominee = this.nomineeRepository.findById(nomineeId);
        if (!optionalNominee.isPresent()) {
            throw new ResourceNotFoundException("Nominee not found with id: " + nomineeId, null, null);
        }
        return optionalNominee.get();
    }

    @Override
    public Nominee createNominee(Nominee nominee) {
        Nominee nom = new Nominee();
        nom.setId(nominee.getId());
        nom.setNomineename(nominee.getNomineename());
        nom.setAddress(nominee.getAddress());
        nom.setRelationship(nominee.getRelationship());
        nom.setAge(nominee.getAge());
        nom.setPercentageofshare(nominee.getPercentageofshare());
        nom.setGender(nominee.getGender());
        nom.setNomineePhoto(nominee.getNomineePhoto());
        nom.setEmployee(nominee.getEmployee());

        nomineeRepository.save(nom);
        return nom;
    }

    @Override
    public Nominee updateNominee(Long id, Nominee nomineeDetails) {
        Nominee nominee = nomineeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Nominee not found with id: " + id, null, null));

        nominee.setNomineename(nomineeDetails.getNomineename());
        nominee.setAddress(nomineeDetails.getAddress());
        nominee.setRelationship(nomineeDetails.getRelationship());
        nominee.setAge(nomineeDetails.getAge());
        nominee.setPercentageofshare(nomineeDetails.getPercentageofshare());
        nominee.setGender(nomineeDetails.getGender());
        nominee.setNomineePhoto(nomineeDetails.getNomineePhoto());

        return nomineeRepository.save(nominee);
    }

    @Override
    public boolean deleteNominee(Long nomineeId) {
        try {
            Optional<Nominee> nominee = nomineeRepository.findById(nomineeId);
            if (nominee.isPresent()) {
                nomineeRepository.deleteById(nomineeId);
                return true;
            } else {
                System.out.println("Nominee not found with ID: " + nomineeId);
                return false;
            }
        } catch (Exception e) {
            System.out.println("Error deleting nominee: " + e.getMessage());
            return false;
        }
    }

    @Override
    public List<Nominee> getNomineesByName(String nomineename) {
        return nomineeRepository.findByNomineename(nomineename);
    }

    @Override
    public List<Nominee> getNomineesByRelationship(String relationship) {
        return nomineeRepository.findByRelationship(relationship);
    }

    @Override
    public List<Nominee> getNomineesByGender(String gender) {
        return nomineeRepository.findByGender(gender);
    }

    @Override
    public List<Nominee> getNomineesByAge(Integer age) {
        return nomineeRepository.findByAge(age);
    }

    @Override
    public List<Nominee> getNomineesByAgeRange(Integer minAge, Integer maxAge) {
        return nomineeRepository.findByAgeBetween(minAge, maxAge);
    }

    @Override
    public List<Nominee> getNomineesByPercentageShare(Double percentageofshare) {
        return nomineeRepository.findByPercentageofshare(percentageofshare);
    }

    @Override
    public List<Nominee> getNomineesWithShareGreaterThan(Double percentage) {
        return nomineeRepository.findByPercentageofshareGreaterThan(percentage);
    }

    @Override
    public List<Nominee> searchNomineesByName(String name) {
        return nomineeRepository.findByNomineeNameContaining(name);
    }

    @Override
    public List<Nominee> searchNomineesByAddress(String keyword) {
        return nomineeRepository.findByAddressContaining(keyword);
    }

    @Override
    public List<Nominee> getNomineesByRelationshipAndGender(String relationship, String gender) {
        return nomineeRepository.findByRelationshipAndGender(relationship, gender);
    }

    @Override
    public List<Nominee> getAllNomineesOrderByShare() {
        return nomineeRepository.findByOrderByPercentageofshareDesc();
    }

    @Override
    public List<Nominee> getAllNomineesOrderByName() {
        return nomineeRepository.findByOrderByNomineenameAsc();
    }
}
