package digitization.digitization.repository;

import digitization.digitization.module.Employee;
import digitization.digitization.module.EmployeeStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EmployeeStatusRepository extends JpaRepository<EmployeeStatus, Long> {

    Optional<EmployeeStatus> findByEmpId(Long empId);

    Optional<EmployeeStatus> findById(Long id);

    List<EmployeeStatus> findByStatus(String status);

    List<EmployeeStatus> findByStatusOrderByUpdatedAtDesc(String status);

    @Query("SELECT es FROM EmployeeStatus es WHERE es.status = :status ORDER BY es.updatedAt DESC")
    List<EmployeeStatus> findEmployeesByStatus(@Param("status") String status);

    boolean existsByEmpId(Long empId);

    @Query(value = "SELECT status, COUNT(*) as count FROM employee_status GROUP BY status", nativeQuery = true)
    List<Object[]> getStatusCounts();

    List<EmployeeStatus> findByCreatedBy(String createdBy);

    List<EmployeeStatus> findByCreatedByOrderByUpdatedAtDesc(String createdBy);

    @Query(value = "SELECT es.* FROM employee_status es " +
            "JOIN employee e ON es.emp_id = e.id " +
            "WHERE es.status = :status AND e.district = :district", nativeQuery = true)
    List<EmployeeStatus> findByStatusAndEmployeeDistrict(@Param("status") String status, @Param("district") String district);

}
