package digitization.digitization.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import digitization.digitization.module.Employee;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public class ServiceHistoryDto {
    private Long id;
    private LocalDate date;
    private String type;
    private String status;
    private String appointmenttype;
    private String modeofappointment;
    private LocalDate dateofappointment;
    private LocalDate proceedingorderdate;
    private String proceedingorderno;
    private LocalDate joiningdate;
    private LocalDate promoteddate;
    private String fromdesignation;
    private String topromoted;
    private LocalDate fromdate;
    private LocalDate todate;
    private String typeofincrement;
    private String incrementtype;  // Alternative field name for backward compatibility
    private String fromplace;
    private String toplace;
    private String designation;
    private String originaldesignation;
    private String parentdepartment;
    private String punishmenttype;
    private String casedetails;
    private Date caseDate;
    private String caseNumber;
    private String description;

    @JsonIgnore
    private String involvedPersons;

    // Direct field for JSON deserialization
    @JsonProperty("personsInvolved")
    private Long[] personsInvolvedArray;

    @JsonProperty("involvedPersonsWithNames")
    private List<PersonDto> involvedPersonsWithNames;

    private String presentStatus;
    private LocalDate punishmentdate;
    private LocalDate withholdingFromDate;
    private LocalDate withholdingToDate;
    private String hasCaseDetails;

    private Double basicPay;

    private Double da;
    private Double daField;  // Alternative field name for backward compatibility

    private Double basicPlusDA;

    // New fields added
    private LocalDate probationDateOfJoining;
    private LocalDate probationEndDate;
    private String fromOrganization;
    private String fromDesignationForeign;
    private String toDesignationForeign;
    private String fromDepartment;
    private String toDepartment;

    public ServiceHistoryDto() {
    }

    public ServiceHistoryDto(Long id, LocalDate date, String type, String status, String appointmenttype, String modeofappointment, LocalDate dateofappointment, LocalDate proceedingorderdate, String proceedingorderno, LocalDate joiningdate, LocalDate promoteddate, String fromdesignation, String topromoted, LocalDate fromdate, LocalDate todate, String typeofincrement, String fromplace, String toplace, String designation, String originaldesignation, String parentdepartment, String punishmenttype, String casedetails, Date caseDate, String caseNumber, String description, String involvedPersons, String presentStatus, LocalDate punishmentdate, Double basicPay, Double da, Double basicPlusDA, LocalDate probationDateOfJoining, LocalDate probationEndDate, String fromOrganization, String fromDesignationForeign, String toDesignationForeign, String fromDepartment, String toDepartment) {
        this.id = id;
        this.date = date;
        this.type = type;
        this.status = status;
        this.appointmenttype = appointmenttype;
        this.modeofappointment = modeofappointment;
        this.dateofappointment = dateofappointment;
        this.proceedingorderdate = proceedingorderdate;
        this.proceedingorderno = proceedingorderno;
        this.joiningdate = joiningdate;
        this.promoteddate = promoteddate;
        this.fromdesignation = fromdesignation;
        this.topromoted = topromoted;
        this.fromdate = fromdate;
        this.todate = todate;
        this.typeofincrement = typeofincrement;
        this.fromplace = fromplace;
        this.toplace = toplace;
        this.designation = designation;
        this.originaldesignation = originaldesignation;
        this.parentdepartment = parentdepartment;
        this.punishmenttype = punishmenttype;
        this.casedetails = casedetails;
        this.caseDate = caseDate;
        this.caseNumber = caseNumber;
        this.description = description;
        this.involvedPersons = involvedPersons;
        this.presentStatus = presentStatus;

        // Sync the array field with the string field
        this.personsInvolvedArray = convertStringToLongArray(involvedPersons);
        this.punishmentdate = punishmentdate;
        this.basicPay = basicPay;
        this.da = da;
        this.basicPlusDA = basicPlusDA;
        this.probationDateOfJoining = probationDateOfJoining;
        this.probationEndDate = probationEndDate;
        this.fromOrganization = fromOrganization;
        this.fromDesignationForeign = fromDesignationForeign;
        this.toDesignationForeign = toDesignationForeign;
        this.fromDepartment = fromDepartment;
        this.toDepartment = toDepartment;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getAppointmenttype() {
        return appointmenttype;
    }

    public void setAppointmenttype(String appointmenttype) {
        this.appointmenttype = appointmenttype;
    }

    public String getModeofappointment() {
        return modeofappointment;
    }

    public void setModeofappointment(String modeofappointment) {
        this.modeofappointment = modeofappointment;
    }

    public LocalDate getDateofappointment() {
        return dateofappointment;
    }

    public void setDateofappointment(LocalDate dateofappointment) {
        this.dateofappointment = dateofappointment;
    }

    public LocalDate getProceedingorderdate() {
        return proceedingorderdate;
    }

    public void setProceedingorderdate(LocalDate proceedingorderdate) {
        this.proceedingorderdate = proceedingorderdate;
    }

    public LocalDate getJoiningdate() {
        return joiningdate;
    }

    public void setJoiningdate(LocalDate joiningdate) {
        this.joiningdate = joiningdate;
    }

    public LocalDate getPromoteddate() {
        return promoteddate;
    }

    public void setPromoteddate(LocalDate promoteddate) {
        this.promoteddate = promoteddate;
    }

    public String getFromdesignation() {
        return fromdesignation;
    }

    public void setFromdesignation(String fromdesignation) {
        this.fromdesignation = fromdesignation;
    }

    public String getTopromoted() {
        return topromoted;
    }

    public void setTopromoted(String topromoted) {
        this.topromoted = topromoted;
    }

    public LocalDate getFromdate() {
        return fromdate;
    }

    public void setFromdate(LocalDate fromdate) {
        this.fromdate = fromdate;
    }

    public LocalDate getTodate() {
        return todate;
    }

    public void setTodate(LocalDate todate) {
        this.todate = todate;
    }

    public String getTypeofincrement() {
        return typeofincrement;
    }

    public void setTypeofincrement(String typeofincrement) {
        this.typeofincrement = typeofincrement;
    }

    public String getIncrementtype() {
        return incrementtype;
    }

    public void setIncrementtype(String incrementtype) {
        this.incrementtype = incrementtype;
    }

    public String getFromplace() {
        return fromplace;
    }

    public void setFromplace(String fromplace) {
        this.fromplace = fromplace;
    }

    public String getToplace() {
        return toplace;
    }

    public void setToplace(String toplace) {
        this.toplace = toplace;
    }
    public String getDesignation() {
        return designation;
    }

    public void setDesignation(String designation) {
        this.designation = designation;
    }

    public String getOriginaldesignation() {
        return originaldesignation;
    }

    public void setOriginaldesignation(String originaldesignation) {
        this.originaldesignation = originaldesignation;
    }

    public String getParentdepartment() {
        return parentdepartment;
    }

    public void setParentdepartment(String parentdepartment) {
        this.parentdepartment = parentdepartment;
    }


    public String getProceedingorderno() {
        return proceedingorderno;
    }

    public void setProceedingorderno(String proceedingorderno) {
        this.proceedingorderno = proceedingorderno;
    }

    public String getPunishmenttype() {
        return punishmenttype;
    }

    public void setPunishmenttype(String punishmenttype) {
        this.punishmenttype = punishmenttype;
    }

    public String getCasedetails() {
        return casedetails;
    }

    public void setCasedetails(String casedetails) {
        this.casedetails = casedetails;
    }

    public Date getCaseDate() {
        return caseDate;
    }

    public void setCaseDate(Date caseDate) {
        this.caseDate = caseDate;
    }

    public String getCaseNumber() {
        return caseNumber;
    }

    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("personsInvolved")
    public Long[] getPersonsInvolved() {
        // If the array field is set (from JSON), use it; otherwise convert from string
        if (personsInvolvedArray != null) {
            return personsInvolvedArray;
        }
        return convertStringToLongArray(involvedPersons);
    }

    @JsonProperty("personsInvolved")
    public void setPersonsInvolved(Long[] personsInvolved) {
        System.out.println("DEBUG: ServiceHistoryDto.setPersonsInvolved() called with: " + java.util.Arrays.toString(personsInvolved));
        this.personsInvolvedArray = personsInvolved;
        this.involvedPersons = convertLongArrayToString(personsInvolved);
        System.out.println("DEBUG: ServiceHistoryDto.setPersonsInvolved() stored as: '" + this.involvedPersons + "'");
    }

    public String getInvolvedPersons() {
        return involvedPersons;
    }

    public void setInvolvedPersons(String involvedPersons) {
        this.involvedPersons = involvedPersons;
    }



    public List<PersonDto> getInvolvedPersonsWithNames() {
        return involvedPersonsWithNames;
    }

    public void setInvolvedPersonsWithNames(List<PersonDto> involvedPersonsWithNames) {
        this.involvedPersonsWithNames = involvedPersonsWithNames;
    }

    // Direct getter/setter for the array field (for Jackson) - marked as JsonIgnore to avoid conflicts
    @JsonIgnore
    public Long[] getPersonsInvolvedArray() {
        return personsInvolvedArray;
    }

    @JsonIgnore
    public void setPersonsInvolvedArray(Long[] personsInvolvedArray) {
        System.out.println("DEBUG: ServiceHistoryDto.setPersonsInvolvedArray() called with: " + java.util.Arrays.toString(personsInvolvedArray));
        this.personsInvolvedArray = personsInvolvedArray;
        this.involvedPersons = convertLongArrayToString(personsInvolvedArray);
        System.out.println("DEBUG: ServiceHistoryDto.setPersonsInvolvedArray() stored as: '" + this.involvedPersons + "'");
    }

    // Helper methods to convert between Long[] and String
    private Long[] convertStringToLongArray(String str) {
        if (str == null || str.trim().isEmpty()) {
            return new Long[0];
        }
        try {
            String[] parts = str.split(",");
            Long[] result = new Long[parts.length];
            for (int i = 0; i < parts.length; i++) {
                result[i] = Long.parseLong(parts[i].trim());
            }
            return result;
        } catch (NumberFormatException e) {
            System.out.println("Error converting string to Long array: " + str + ", error: " + e.getMessage());
            return new Long[0];
        }
    }

    private String convertLongArrayToString(Long[] array) {
        if (array == null || array.length == 0) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < array.length; i++) {
            if (array[i] != null) {
                sb.append(array[i]);
                if (i < array.length - 1) {
                    sb.append(",");
                }
            }
        }
        return sb.toString();
    }

    public String getPresentStatus() {
        return presentStatus;
    }

    public void setPresentStatus(String presentStatus) {
        this.presentStatus = presentStatus;
    }

    public LocalDate getPunishmentdate() {
        return punishmentdate;
    }

    public void setPunishmentdate(LocalDate punishmentdate) {
        this.punishmentdate = punishmentdate;
    }

    public LocalDate getWithholdingFromDate() {
        return withholdingFromDate;
    }

    public void setWithholdingFromDate(LocalDate withholdingFromDate) {
        this.withholdingFromDate = withholdingFromDate;
    }

    public LocalDate getWithholdingToDate() {
        return withholdingToDate;
    }

    public void setWithholdingToDate(LocalDate withholdingToDate) {
        this.withholdingToDate = withholdingToDate;
    }

    public String getHasCaseDetails() {
        return hasCaseDetails;
    }

    public void setHasCaseDetails(String hasCaseDetails) {
        this.hasCaseDetails = hasCaseDetails;
    }

    public Double getBasicPay() {
        return basicPay;
    }

    public void setBasicPay(Double basicPay) {
        this.basicPay = basicPay;
    }

    public Double getDa() {
        return da;
    }

    public void setDa(Double da) {
        this.da = da;
    }

    public Double getDaField() {
        return daField;
    }

    public void setDaField(Double daField) {
        this.daField = daField;
    }

    public Double getBasicPlusDA() {
        return basicPlusDA;
    }

    public void setBasicPlusDA(Double basicPlusDA) {
        this.basicPlusDA = basicPlusDA;
    }

    // Backward compatibility methods for single person involved
    @JsonIgnore
    public String getPersonInvolved() {
        Long[] persons = getPersonsInvolved();
        if (persons != null && persons.length > 0) {
            return persons[0].toString();
        }
        return null;
    }

    @JsonIgnore
    public void setPersonInvolved(String personInvolved) {
        if (personInvolved != null) {
            try {
                setPersonsInvolved(new Long[]{Long.parseLong(personInvolved)});
            } catch (NumberFormatException e) {
                // If it's not a valid number, set to null
                setPersonsInvolved(null);
            }
        } else {
            setPersonsInvolved(null);
        }
    }

    @Override
    public String toString() {
        return "ServiceHistoryDto{" +
                "id=" + id +
                ", date=" + date +
                ", type='" + type + '\'' +
                ", status='" + status + '\'' +
                ", appointmenttype='" + appointmenttype + '\'' +
                ", modeofappointment='" + modeofappointment + '\'' +
                ", dateofappointment=" + dateofappointment +
                ", proceedingorderdate=" + proceedingorderdate +
                ", proceedingorderno='" + proceedingorderno + '\'' +
                ", joiningdate=" + joiningdate +
                ", promoteddate=" + promoteddate +
                ", fromdesignation='" + fromdesignation + '\'' +
                ", topromoted='" + topromoted + '\'' +
                ", fromdate=" + fromdate +
                ", todate=" + todate +
                ", typeofincrement='" + typeofincrement + '\'' +
                ", incrementtype='" + incrementtype + '\'' +
                ", fromplace='" + fromplace + '\'' +
                ", toplace='" + toplace + '\'' +
                ", designation='" + designation + '\'' +
                ", originaldesignation='" + originaldesignation + '\'' +
                ", parentdepartment='" + parentdepartment + '\'' +
                ", punishmenttype='" + punishmenttype + '\'' +
                ", casedetails='" + casedetails + '\'' +
                ", caseDate=" + caseDate +
                ", caseNumber='" + caseNumber + '\'' +
                ", description='" + description + '\'' +
                ", personsInvolved=" + java.util.Arrays.toString(getPersonsInvolved()) +
                ", presentStatus='" + presentStatus + '\'' +
                ", punishmentdate=" + punishmentdate +
                ", withholdingFromDate=" + withholdingFromDate +
                ", withholdingToDate=" + withholdingToDate +
                ", hasCaseDetails='" + hasCaseDetails + '\'' +
                ", basicPay=" + basicPay +
                ", da=" + da +
                ", daField=" + daField +
                ", basicPlusDA=" + basicPlusDA +
                '}';
    }

    public LocalDate getProbationDateOfJoining() {
        return probationDateOfJoining;
    }

    public void setProbationDateOfJoining(LocalDate probationDateOfJoining) {
        this.probationDateOfJoining = probationDateOfJoining;
    }

    public LocalDate getProbationEndDate() {
        return probationEndDate;
    }

    public void setProbationEndDate(LocalDate probationEndDate) {
        this.probationEndDate = probationEndDate;
    }

    public String getFromOrganization() {
        return fromOrganization;
    }

    public void setFromOrganization(String fromOrganization) {
        this.fromOrganization = fromOrganization;
    }

    public String getFromDesignationForeign() {
        return fromDesignationForeign;
    }

    public void setFromDesignationForeign(String fromDesignationForeign) {
        this.fromDesignationForeign = fromDesignationForeign;
    }

    public String getToDesignationForeign() {
        return toDesignationForeign;
    }

    public void setToDesignationForeign(String toDesignationForeign) {
        this.toDesignationForeign = toDesignationForeign;
    }

    public String getFromDepartment() {
        return fromDepartment;
    }

    public void setFromDepartment(String fromDepartment) {
        this.fromDepartment = fromDepartment;
    }

    public String getToDepartment() {
        return toDepartment;
    }

    public void setToDepartment(String toDepartment) {
        this.toDepartment = toDepartment;
    }

    // Test method to verify conversion works
    public static void testConversion() {
        ServiceHistoryDto dto = new ServiceHistoryDto();

        // Test setting array
        Long[] testArray = {25L, 10L};
        dto.setPersonsInvolved(testArray);
        System.out.println("After setting [25, 10], involvedPersons = '" + dto.getInvolvedPersons() + "'");

        // Test getting array back
        Long[] retrieved = dto.getPersonsInvolved();
        System.out.println("Retrieved array: " + java.util.Arrays.toString(retrieved));

        // Test setting string directly
        dto.setInvolvedPersons("123,456,789");
        Long[] fromString = dto.getPersonsInvolved();
        System.out.println("From string '123,456,789', got array: " + java.util.Arrays.toString(fromString));
    }
}
