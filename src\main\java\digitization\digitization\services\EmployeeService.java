package digitization.digitization.services;

import digitization.digitization.dto.*;
import digitization.digitization.dto.userDto.UserDto;
import digitization.digitization.enums.DeleteStatus;
import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.*;
import digitization.digitization.repository.*;
import digitization.digitization.services.implementation.EmployeeServ;
import digitization.digitization.services.userService.UsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class EmployeeService implements EmployeeServ {
    @Autowired
    EmployeeRepo employeeRepo;

    @Autowired
    ProfileRepository profileRepository;

    @Autowired
    AccountDetailsRepository accountDetailsRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    LeaveRepository leaveRepository;

    @Autowired
    EducationQualificationRepository educationQualificationRepository;

    @Autowired
    PunishmentDetailsRepository punishmentDetailsRepository;

    @Autowired
    TrainingDetailsRepository trainingDetailsRepository;

    @Autowired
    NomineeRepository nomineeRepository;

    @Autowired
    DocumentService documentService;

    @Autowired
    DocumentRepository documentRepository;

    @Autowired
    FileStorageService fileStorageService;

    @Autowired
    EmployeeStatusRepository employeeStatusRepository;

    @Autowired
    EmployeeStatusService employeeStatusService;

    @Autowired
    SalaryDetailsService salaryDetailsService;

    @Autowired
    SalaryDetailsRepository salaryDetailsRepository;

    @Autowired
    UserRepository userRepository;

    @Autowired
    RoleRepository roleRepository;

    @Autowired
    UsersService usersService;

//    @Autowired
//    EmployeeStatusService employeeStatusService;

    @Override
    public List<Employee> getEmployeeList() {
        List<Employee> list = employeeRepo.findAllByOrderByIdDesc();
        return list;
    }

    @Override
    public List<EmployeeListDto> getEmployeeListDto() {
        List<Employee> employees = employeeRepo.findAllByOrderByIdDesc();
//        List<Employee> employees = employeeRepo.findByCreatedByOrderByIdDesc();


        return employees.stream()
                .map(this::convertToListDto)
                .collect(java.util.stream.Collectors.toList());
    }

    @Override
    public List<EmployeeListDto> getEmployeeListDtoCreated(String createdBy) {
//        List<Employee> employees = employeeRepo.findAllByOrderByIdDesc();
        List<Employee> employees = employeeRepo.findByCreatedByOrderByIdDesc(createdBy);


        return employees.stream()
                .map(this::convertToListDto)
                .collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Employee getEmployeeById(Long empId){
        Optional<Employee> optionalEmployee = this.employeeRepo.findById(empId);
        if (!optionalEmployee.isPresent()) {
            throw new ResourceNotFoundException("Employee not found with id: " + empId, null, null);
        }
        return optionalEmployee.get();

    }
//    public List<Employee> getEmployeesByStatus(String status) {
//        return employeeRepo.findByStatusOrderByUpdatedAtDesc(status);
//    }

    public List<Employee> getEmployeesByStatus(String status) {
        try {
            return employeeRepo.findEmployeesByStatus(status);
        } catch (Exception e) {
            // Fallback approach if the join query fails
            System.out.println("Join query failed, using fallback approach: " + e.getMessage());
            return getEmployeesByStatusFallback(status);
        }
    }
    private List<Employee> getEmployeesByStatusFallback(String status) {
        // Get all employee statuses with the given status
        List<EmployeeStatus> employeeStatuses = employeeStatusService.getEmployeesByStatus(status);

        // Get employee IDs (now they are already Long)
        List<Long> employeeIds = employeeStatuses.stream()
                .map(EmployeeStatus::getEmpId)
                .filter(id -> id != null)
                .collect(Collectors.toList());

        // Get employees by IDs
        return employeeIds.stream()
                .map(id -> {
                    try {
                        return getEmployeeById(id);
                    } catch (Exception e) {
                        System.out.println("Employee not found for ID: " + id);
                        return null;
                    }
                })
                .filter(emp -> emp != null)
                .collect(Collectors.toList());
    }

    /**
     * Get approved employee names - optimized method to return only names
     * Handles both 'approved' and 'APPROVED' status values
     */
    public List<EmployeeNameDto> getApprovedEmployeeNames() {
        try {
            // Use the specific method that handles both 'approved' and 'APPROVED'
            List<Object[]> results = employeeRepo.findApprovedEmployeeNames();
            return results.stream()
                    .map(result -> new EmployeeNameDto(
                            ((Number) result[0]).longValue(), // id
                            (String) result[1]                // employee_name
                    ))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            System.out.println("Error fetching approved employee names: " + e.getMessage());
            e.printStackTrace();
            // Fallback: try both cases separately
            return getApprovedEmployeeNamesFallback();
        }
    }

    /**
     * Fallback method to get approved employee names by trying different status values
     */
    private List<EmployeeNameDto> getApprovedEmployeeNamesFallback() {
        List<EmployeeNameDto> result = new ArrayList<>();
        try {
            // Try lowercase 'approved'
            List<Object[]> approvedResults = employeeRepo.findEmployeeNamesByStatus("approved");
            result.addAll(approvedResults.stream()
                    .map(r -> new EmployeeNameDto(((Number) r[0]).longValue(), (String) r[1]))
                    .collect(Collectors.toList()));

            // Try uppercase 'APPROVED'
            List<Object[]> approvedUpperResults = employeeRepo.findEmployeeNamesByStatus("APPROVED");
            result.addAll(approvedUpperResults.stream()
                    .map(r -> new EmployeeNameDto(((Number) r[0]).longValue(), (String) r[1]))
                    .collect(Collectors.toList()));

            // Remove duplicates based on employee ID
            return result.stream()
                    .collect(Collectors.toMap(
                            EmployeeNameDto::getId,
                            dto -> dto,
                            (existing, replacement) -> existing))
                    .values()
                    .stream()
                    .collect(Collectors.toList());

        } catch (Exception e) {
            System.out.println("Error in fallback method: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Get employee names by status - generic method with case-insensitive matching
     */
    public List<EmployeeNameDto> getEmployeeNamesByStatus(String status) {
        try {
            List<Object[]> results = employeeRepo.findEmployeeNamesByStatus(status);
            return results.stream()
                    .map(result -> new EmployeeNameDto(
                            ((Number) result[0]).longValue(), // id
                            (String) result[1]                // employee_name
                    ))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            System.out.println("Error fetching employee names by status '" + status + "': " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Get employee names by list of IDs
     */
    public List<PersonDto> getEmployeeNamesByIds(List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return new ArrayList<>();
            }

            List<Object[]> results = employeeRepo.findEmployeeNamesByIds(ids);
            return results.stream()
                    .map(result -> new PersonDto(
                            ((Number) result[0]).longValue(), // id
                            (String) result[1]                // employee_name
                    ))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            System.out.println("Error fetching employee names by IDs: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    public digitization.digitization.dto.EmployeeDetailDto getEmployeeWithAllDetails(Long empId) {
        // Get employee basic details
        Employee employee = getEmployeeById(empId);

        // Create detailed DTO
        digitization.digitization.dto.EmployeeDetailDto detailDto = new digitization.digitization.dto.EmployeeDetailDto();

        // Map employee basic details
        detailDto.setId(employee.getId());
        detailDto.setEcpfNumber(employee.getEcpfNumber());
        detailDto.setPanNumber(employee.getPanNumber());
        detailDto.setEmployeeName(employee.getEmployeeName());
        detailDto.setEmail(employee.getEmail());
        detailDto.setMobileNumber(employee.getMobileNumber());
        detailDto.setRegisterNumber(employee.getRegisterNumber());
        detailDto.setSection(employee.getSection());
        detailDto.setFatherName(employee.getFatherName());
        detailDto.setMotherName(employee.getMotherName());
        detailDto.setDateOfBirth(employee.getDateOfBirth());
        detailDto.setReligion(employee.getReligion());
        detailDto.setCommunity(employee.getCommunity());
        detailDto.setCaste(employee.getCaste());
        detailDto.setPersonalIdentificationmark1(employee.getPersonalIdentificationmark1());
        detailDto.setPersonalIdentificationmark2(employee.getPersonalIdentificationmark2());
        detailDto.setCurrentDesignation(employee.getCurrentDesignation());
        detailDto.setDateOfEntry(employee.getDateOfEntry());
        detailDto.setDistrict(employee.getDistrict());
        detailDto.setNativePlaceAndTaluk(employee.getNativePlaceAndTaluk());
        detailDto.setMainEmployeeType(employee.getMainEmployeeType());
        detailDto.setSeasonalCategory(employee.getSeasonalCategory());
        detailDto.setLoadManCategory(employee.getLoadManCategory());
        detailDto.setProfilePhoto(employee.getProfilePhoto());
        detailDto.setCreatedBy(employee.getCreatedBy());
        detailDto.setCreatedAt(employee.getCreatedAt());
        detailDto.setUpdatedAt(employee.getUpdatedAt());

        // Get employee status information
        try {
            Optional<EmployeeStatus> employeeStatusOpt = employeeStatusRepository.findByEmpId(empId);
            if (employeeStatusOpt.isPresent()) {
                EmployeeStatus employeeStatus = employeeStatusOpt.get();
                detailDto.setStatus(employeeStatus.getStatus());
                detailDto.setStatusCreatedAt(employeeStatus.getCreatedAt());
                detailDto.setStatusUpdatedAt(employeeStatus.getUpdatedAt());
                detailDto.setStatusApprovedBy(employeeStatus.getApprovedBy());
                detailDto.setStatusRejectedBy(employeeStatus.getRejectedBy());
                detailDto.setStatusRemarks(employeeStatus.getRemarks());
            } else {
                // Set default values if no status record exists
                detailDto.setStatus("unknown");
                detailDto.setStatusCreatedAt(null);
                detailDto.setStatusUpdatedAt(null);
                detailDto.setStatusApprovedBy(null);
                detailDto.setStatusRejectedBy(null);
                detailDto.setStatusRemarks(null);
            }
        } catch (Exception e) {
            // Set default values if status lookup fails
            System.out.println("Warning: Could not fetch employee status for ID " + empId + ": " + e.getMessage());
            detailDto.setStatus("unknown");
            detailDto.setStatusCreatedAt(null);
            detailDto.setStatusUpdatedAt(null);
            detailDto.setStatusApprovedBy(null);
            detailDto.setStatusRejectedBy(null);
            detailDto.setStatusRemarks(null);
        }

        // Get related data
        detailDto.setProfile(getProfileByEmployeeId(empId));
        detailDto.setAccountDetails(getAccountDetailsByEmployeeId(empId));
        detailDto.setServiceHistory(getServiceHistoryByEmployeeId(empId));
        detailDto.setLeaveBalances(getLeaveBalancesByEmployeeId(empId));
        detailDto.setEducationQualifications(getEducationQualificationsByEmployeeId(empId));
        detailDto.setPunishmentDetails(getPunishmentDetailsByEmployeeId(empId));
        detailDto.setTrainingDetails(getTrainingDetailsByEmployeeId(empId));
        detailDto.setNominees(getNomineesByEmployeeId(empId));
        detailDto.setDocuments(documentService.getDocumentsByEmployeeId(empId));
        detailDto.setSalaryDetails(getSalaryDetailsByEmployeeId(empId));
        System.out.println("get value" + detailDto.getServiceHistory());
        return detailDto;
    }

    @Override
    public Employee createEmployee(Employee employee) {
        Employee emp=new Employee();
        emp.setId(employee.getId());
        emp.setEcpfNumber(employee.getEcpfNumber());
        emp.setPanNumber(employee.getPanNumber());
        emp.setEmployeeName(employee.getEmployeeName());
        emp.setEmail(employee.getEmail());
        emp.setMobileNumber(employee.getMobileNumber());
        emp.setFatherName(employee.getFatherName());
        emp.setMotherName(employee.getMotherName());
        emp.setCommunity(employee.getCommunity());
        emp.setCaste(employee.getCaste());
        emp.setCurrentDesignation(employee.getCurrentDesignation());
        emp.setDateOfBirth(employee.getDateOfBirth());
        emp.setDateOfEntry(employee.getDateOfEntry());
        emp.setPersonalIdentificationmark1(employee.getPersonalIdentificationmark1());
        emp.setPersonalIdentificationmark2(employee.getPersonalIdentificationmark2());
        emp.setNativePlaceAndTaluk(employee.getNativePlaceAndTaluk());
        emp.setReligion(employee.getReligion());
        emp.setDistrict(employee.getDistrict());
        emp.setMainEmployeeType(employee.getMainEmployeeType());
        emp.setGender(employee.getGender());
        emp.setCreatedBy(employee.getCreatedBy());

        // Save employee first
        Employee savedEmployee = employeeRepo.save(emp);

        // Create employee status record
        try {
            employeeStatusService.createEmployeeStatus(savedEmployee.getId(), "pending");
        } catch (Exception e) {
            // Log error but don't fail the employee creation
            System.out.println("Warning: Could not create employee status record: " + e.getMessage());
        }

        return savedEmployee;
    }

    @Override
    @Transactional
    public Employee createEmployeeWithRelatedRecords(EmployeeCreationDto employeeCreationDto) {
        // 1. Create Employee
        Employee employee = new Employee();
        employee.setEcpfNumber(employeeCreationDto.getEcpfNumber());
        employee.setEmpId(employeeCreationDto.getEmpId());
        employee.setPanNumber(employeeCreationDto.getPanNumber());
        employee.setEmployeeName(employeeCreationDto.getEmployeeName());
        employee.setEmail(employeeCreationDto.getEmail());
        employee.setMobileNumber(employeeCreationDto.getMobileNumber());
        employee.setRegisterNumber(employeeCreationDto.getRegisterNumber());
        employee.setSection(employeeCreationDto.getSection());
        employee.setFatherName(employeeCreationDto.getFatherName());
        employee.setMotherName(employeeCreationDto.getMotherName());
        employee.setDateOfBirth(employeeCreationDto.getDateOfBirth());
        employee.setReligion(employeeCreationDto.getReligion());
        employee.setCommunity(employeeCreationDto.getCommunity());
        employee.setCaste(employeeCreationDto.getCaste());
        employee.setPersonalIdentificationmark1(employeeCreationDto.getPersonalIdentificationmark1());
        employee.setPersonalIdentificationmark2(employeeCreationDto.getPersonalIdentificationmark2());
        employee.setCurrentDesignation(employeeCreationDto.getCurrentDesignation());
        employee.setDateOfEntry(employeeCreationDto.getDateOfEntry());
        employee.setDistrict(employeeCreationDto.getDistrict());
        employee.setNativePlaceAndTaluk(employeeCreationDto.getNativePlaceAndTaluk());
        employee.setMainEmployeeType(employeeCreationDto.getMainEmployeeType());
        employee.setGender(employeeCreationDto.getGender());
        employee.setCreatedBy(employeeCreationDto.getCreatedBy());

        // Save employee first to get the ID
        Employee savedEmployee = employeeRepo.save(employee);
        Long employeeId = savedEmployee.getId();

        // 2. Create Employee Status record with default "pending" status
        try {
            String createdBy = employeeCreationDto.getCreatedBy();
            if (createdBy != null && !createdBy.trim().isEmpty()) {
                employeeStatusService.createEmployeeStatus(employeeId, "pending", createdBy);
            } else {
                employeeStatusService.createEmployeeStatus(employeeId, "pending");
            }
        } catch (Exception e) {
            // Log error but don't fail the employee creation
            System.out.println("Warning: Could not create employee status record: " + e.getMessage());
        }

        // 3. Create Profile (if provided or use employee data)
        createProfile(employeeCreationDto, employeeId);

        // 4. Create Account Details (if provided)
        if (employeeCreationDto.getAccountDetails() != null) {
            createAccountDetails(employeeCreationDto.getAccountDetails(), employeeId);
        }

        // 5. Create Service History records (if provided)
        if (employeeCreationDto.getServiceHistory() != null && !employeeCreationDto.getServiceHistory().isEmpty()) {
            createServiceHistory(employeeCreationDto.getServiceHistory(), savedEmployee);
        }

        // 6. Create Leave balances (if provided)
        if (employeeCreationDto.getLeaveBalances() != null && !employeeCreationDto.getLeaveBalances().isEmpty()) {
            createLeaveBalances(employeeCreationDto.getLeaveBalances(), savedEmployee);
        }

        // 7. Create Education Qualifications (if provided)
        if (employeeCreationDto.getEducationQualifications() != null && !employeeCreationDto.getEducationQualifications().isEmpty()) {
            createEducationQualifications(employeeCreationDto.getEducationQualifications(), employeeId);
        }

        // 8. Create Punishment Details (if provided)
        if (employeeCreationDto.getPunishmentDetails() != null && !employeeCreationDto.getPunishmentDetails().isEmpty()) {
            createPunishmentDetails(employeeCreationDto.getPunishmentDetails(), employeeId);
        }

        // 9. Create Training Details (if provided)
        if (employeeCreationDto.getTrainingDetails() != null && !employeeCreationDto.getTrainingDetails().isEmpty()) {
            createTrainingDetails(employeeCreationDto.getTrainingDetails(), employeeId);
        }

        // 10. Create Nominees (if provided)
        if (employeeCreationDto.getNominees() != null && !employeeCreationDto.getNominees().isEmpty()) {
            createNominees(employeeCreationDto.getNominees(), employeeId);
        }

        // 11. Create Documents (if provided)
        if (employeeCreationDto.getDocuments() != null && !employeeCreationDto.getDocuments().isEmpty()) {
            createDocuments(employeeCreationDto.getDocuments(), employeeId);
        }

        // 12. Create Salary Details (if provided)
        SalaryDetailsDto salaryDetails = employeeCreationDto.getSalaryDetails();

        // Handle backward compatibility: if salary fields are at root level, create/update salaryDetails
        if (employeeCreationDto.getGroup() != null || employeeCreationDto.getPayband() != null || employeeCreationDto.getGradepay() != null) {
            if (salaryDetails == null) {
                salaryDetails = new SalaryDetailsDto();
            }

            // Map root level salary fields to salaryDetails object
            if (employeeCreationDto.getGroup() != null) {
                salaryDetails.setGroup(employeeCreationDto.getGroup());
            }
            if (employeeCreationDto.getPayband() != null) {
                salaryDetails.setPayband(employeeCreationDto.getPayband());
            }
            if (employeeCreationDto.getGradepay() != null) {
                salaryDetails.setGradepay(employeeCreationDto.getGradepay());
            }

            System.out.println("Mapped root level salary fields to salaryDetails: group=" + salaryDetails.getGroup() +
                             ", payband=" + salaryDetails.getPayband() + ", gradepay=" + salaryDetails.getGradepay());
        }

        if (salaryDetails != null) {
            createSalaryDetails(salaryDetails, employeeId);
        }
        if(employeeCreationDto.getEmployeeStatusDto() != null) {
            createStatusDetails(employeeCreationDto.getEmployeeStatusDto(), employeeId);
        }
        UserDto user = new UserDto();
        user.setCreatedAt(LocalDateTime.now());
        user.setEmail(employeeCreationDto.getEmail());
        user.setUsername(employeeCreationDto.getEcpfNumber());
        user.setPassword(employeeCreationDto.getPanNumber());
        user.setActive(true);
        user.setConfirmPassword(employeeCreationDto.getPanNumber());
        user.setFailedCount(0);
        user.setLoggedin(false);
        user.setMobile("9878451245");
        if(user.getRoleId()!=null){
            List<Role> roles = new ArrayList<>();
            Optional<Role>   optionalRole = roleRepository.findById(user.getRoleId());
            user.setRoleType(optionalRole.get().getRole().name());

            Optional<Role> optionalRoles = roleRepository.findById(user.getRoleId());
            if (!optionalRoles.isPresent()) {
                throw new ResourceNotFoundException("Error", "Role", "NOT FOUND");
            }
            roles.add(optionalRoles.get());

            user.setRole(roles.get(2));
        }

//        user.setAccountLocked(accountLocked);
//        user.setActive(accountActive);
        user.setCreatedAt(LocalDateTime.now());
        user.setStatus(DeleteStatus.ACTIVE);


//        usersService.save(user);

        return savedEmployee;
    }

    @Transactional
    public Employee createEmployeeWithFiles(EmployeeCreationDto employeeCreationDto,
                                          MultipartFile profilePhoto,
                                          MultipartFile[] nomineePhotos,
                                          MultipartFile[] documents) {

        System.out.println("Creating employee with files...");

        // 1. Create Employee first (same as existing method)
        Employee employee = new Employee();
        employee.setEcpfNumber(employeeCreationDto.getEcpfNumber());
        employee.setEmpId(employeeCreationDto.getEmpId());
        employee.setPanNumber(employeeCreationDto.getPanNumber());
        employee.setEmployeeName(employeeCreationDto.getEmployeeName());
        employee.setEmail(employeeCreationDto.getEmail());
        employee.setMobileNumber(employeeCreationDto.getMobileNumber());
        employee.setRegisterNumber(employeeCreationDto.getRegisterNumber());
        employee.setSection(employeeCreationDto.getSection());
        employee.setFatherName(employeeCreationDto.getFatherName());
        employee.setMotherName(employeeCreationDto.getMotherName());
        employee.setDateOfBirth(employeeCreationDto.getDateOfBirth());
        employee.setReligion(employeeCreationDto.getReligion());
        employee.setCommunity(employeeCreationDto.getCommunity());
        employee.setCaste(employeeCreationDto.getCaste());
        employee.setPersonalIdentificationmark1(employeeCreationDto.getPersonalIdentificationmark1());
        employee.setPersonalIdentificationmark2(employeeCreationDto.getPersonalIdentificationmark2());
        employee.setCurrentDesignation(employeeCreationDto.getCurrentDesignation());
        employee.setDateOfEntry(employeeCreationDto.getDateOfEntry());
        employee.setDistrict(employeeCreationDto.getDistrict());
        employee.setNativePlaceAndTaluk(employeeCreationDto.getNativePlaceAndTaluk());
        employee.setMainEmployeeType(employeeCreationDto.getMainEmployeeType());
        employee.setGender(employeeCreationDto.getGender());
        employee.setCreatedBy(employeeCreationDto.getCreatedBy());

        // Save employee first to get the ID
        Employee savedEmployee = employeeRepo.save(employee);
        Long employeeId = savedEmployee.getId();

        System.out.println("Employee created with ID: " + employeeId);

        // 2. Upload profile photo if provided
        if (profilePhoto != null && !profilePhoto.isEmpty()) {
            try {
                System.out.println("Uploading profile photo: " + profilePhoto.getOriginalFilename());
                String profilePhotoUrl = documentService.uploadPhotoAndUpdateEmployee(employeeId, profilePhoto);
                System.out.println("Profile photo uploaded: " + profilePhotoUrl);
            } catch (Exception e) {
                System.out.println("Error uploading profile photo: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 3. Upload nominee photos and update nominee records
        if (nomineePhotos != null && nomineePhotos.length > 0 &&
            employeeCreationDto.getNominees() != null && !employeeCreationDto.getNominees().isEmpty()) {

            System.out.println("Creating nominees WITH photos - " + nomineePhotos.length + " photos provided for " + employeeCreationDto.getNominees().size() + " nominees");
            createNomineesWithPhotos(employeeCreationDto.getNominees(), employeeId, nomineePhotos);
        } else if (employeeCreationDto.getNominees() != null && !employeeCreationDto.getNominees().isEmpty()) {
            // Create nominees without photos
            System.out.println("Creating nominees WITHOUT photos - " + employeeCreationDto.getNominees().size() + " nominees");
            createNominees(employeeCreationDto.getNominees(), employeeId);
        } else {
            System.out.println("No nominees to create");
        }

        // 4. Upload documents if provided
        if (documents != null && documents.length > 0) {
            for (MultipartFile document : documents) {
                if (document != null && !document.isEmpty()) {
                    try {
                        System.out.println("Uploading document: " + document.getOriginalFilename());
                        digitization.digitization.module.Document savedDoc = documentService.storeDocument(employeeId, document);
                        System.out.println("Document uploaded: " + savedDoc.getFileUrl());
                    } catch (Exception e) {
                        System.out.println("Error uploading document " + document.getOriginalFilename() + ": " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            }
        }

        // 5. Create other related records (same as existing method)
        try {
            String createdBy = employeeCreationDto.getCreatedBy();
            if (createdBy != null && !createdBy.trim().isEmpty()) {
                employeeStatusService.createEmployeeStatus(employeeId, "pending", createdBy);
            } else {
                employeeStatusService.createEmployeeStatus(employeeId, "pending");
            }
        } catch (Exception e) {
            System.out.println("Warning: Could not create employee status record: " + e.getMessage());
        }

        createProfile(employeeCreationDto, employeeId);

        if (employeeCreationDto.getAccountDetails() != null) {
            createAccountDetails(employeeCreationDto.getAccountDetails(), employeeId);
        }

        if (employeeCreationDto.getServiceHistory() != null && !employeeCreationDto.getServiceHistory().isEmpty()) {
            createServiceHistory(employeeCreationDto.getServiceHistory(), savedEmployee);
        }

        if (employeeCreationDto.getLeaveBalances() != null && !employeeCreationDto.getLeaveBalances().isEmpty()) {
            createLeaveBalances(employeeCreationDto.getLeaveBalances(), savedEmployee);
        }

        if (employeeCreationDto.getEducationQualifications() != null && !employeeCreationDto.getEducationQualifications().isEmpty()) {
            createEducationQualifications(employeeCreationDto.getEducationQualifications(), employeeId);
        }

        if (employeeCreationDto.getPunishmentDetails() != null && !employeeCreationDto.getPunishmentDetails().isEmpty()) {
            createPunishmentDetails(employeeCreationDto.getPunishmentDetails(), employeeId);
        }

        if (employeeCreationDto.getTrainingDetails() != null && !employeeCreationDto.getTrainingDetails().isEmpty()) {
            createTrainingDetails(employeeCreationDto.getTrainingDetails(), employeeId);
        }

        // Handle salary details with backward compatibility
        SalaryDetailsDto salaryDetails = employeeCreationDto.getSalaryDetails();

        // Handle backward compatibility: if salary fields are at root level, create/update salaryDetails
        if (employeeCreationDto.getGroup() != null || employeeCreationDto.getPayband() != null || employeeCreationDto.getGradepay() != null) {
            if (salaryDetails == null) {
                salaryDetails = new SalaryDetailsDto();
            }

            // Map root level salary fields to salaryDetails object
            if (employeeCreationDto.getGroup() != null) {
                salaryDetails.setGroup(employeeCreationDto.getGroup());
            }
            if (employeeCreationDto.getPayband() != null) {
                salaryDetails.setPayband(employeeCreationDto.getPayband());
            }
            if (employeeCreationDto.getGradepay() != null) {
                salaryDetails.setGradepay(employeeCreationDto.getGradepay());
            }

            System.out.println("Mapped root level salary fields to salaryDetails: group=" + salaryDetails.getGroup() +
                             ", payband=" + salaryDetails.getPayband() + ", gradepay=" + salaryDetails.getGradepay());
        }

        if (salaryDetails != null) {
            createSalaryDetails(salaryDetails, employeeId);
        }

        // Create Documents (if provided) - same as working method
        if (employeeCreationDto.getDocuments() != null && !employeeCreationDto.getDocuments().isEmpty()) {
            createDocuments(employeeCreationDto.getDocuments(), employeeId);
        }

        // Create Employee Status Details (if provided) - same as working method
        if(employeeCreationDto.getEmployeeStatusDto() != null) {
            createStatusDetails(employeeCreationDto.getEmployeeStatusDto(), employeeId);
        }

        // Create User record - same as working method
        UserDto user = new UserDto();
        user.setCreatedAt(LocalDateTime.now());
        user.setEmail(employeeCreationDto.getEmail());
        user.setUsername(employeeCreationDto.getEcpfNumber());
        user.setPassword(employeeCreationDto.getPanNumber());
        user.setActive(true);
        user.setConfirmPassword(employeeCreationDto.getPanNumber());
        user.setFailedCount(0);
        user.setLoggedin(false);
        user.setMobile("9878451245");
        if(user.getRoleId()!=null){
            List<Role> roles = new ArrayList<>();
            Optional<Role>   optionalRole = roleRepository.findById(user.getRoleId());
            user.setRoleType(optionalRole.get().getRole().name());

            Optional<Role> optionalRoles = roleRepository.findById(user.getRoleId());
            if (!optionalRoles.isPresent()) {
                throw new ResourceNotFoundException("Error", "Role", "NOT FOUND");
            }
            roles.add(optionalRoles.get());

            user.setRole(roles.get(2));
        }

        user.setCreatedAt(LocalDateTime.now());
        user.setStatus(DeleteStatus.ACTIVE);

        // Note: User service call is commented out in working method too
        // usersService.save(user);

        System.out.println("Employee with files created successfully: " + employeeId);
        return savedEmployee;
    }

    @Override
    public Employee updateEmployee(Long id, Employee employeeDetails) {
        Employee employee = employeeRepo.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + id, null, null));

        employee.setEmployeeName(employeeDetails.getEmployeeName());
        employee.setEcpfNumber(employeeDetails.getEcpfNumber());
        employee.setPanNumber(employeeDetails.getPanNumber());
        employee.setEmail(employeeDetails.getEmail());
        employee.setMobileNumber(employeeDetails.getMobileNumber());
        employee.setFatherName(employeeDetails.getFatherName());
        employee.setMotherName(employeeDetails.getMotherName());
        employee.setCommunity(employeeDetails.getCommunity());
        employee.setDateOfBirth(employeeDetails.getDateOfBirth());
        employee.setDateOfEntry(employeeDetails.getDateOfEntry());
        employee.setCurrentDesignation(employeeDetails.getCurrentDesignation());
        employee.setCaste(employeeDetails.getCaste());
        employee.setPersonalIdentificationmark1(employeeDetails.getPersonalIdentificationmark1());
        employee.setPersonalIdentificationmark2(employeeDetails.getPersonalIdentificationmark2());
        employee.setNativePlaceAndTaluk(employeeDetails.getNativePlaceAndTaluk());
        employee.setDistrict(employeeDetails.getDistrict());
        employee.setReligion(employeeDetails.getReligion());
        employee.setMainEmployeeType(employeeDetails.getMainEmployeeType());
        employee.setGender(employeeDetails.getGender());
        // Note: createdBy should not be updated, only set during creation
        // employee.setCreatedBy(employeeDetails.getCreatedBy());

        return employeeRepo.save(employee);
    }

    @Override
    @Transactional
    public Employee updateEmployeeWithRelatedRecords(Long id, EmployeeCreationDto employeeUpdateDto) {
        // 1. Get existing employee
        Employee employee = employeeRepo.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + id, null, null));

        // 2. Update employee basic details
        employee.setEcpfNumber(employeeUpdateDto.getEcpfNumber());
        employee.setEmpId(employeeUpdateDto.getEmpId());
        employee.setPanNumber(employeeUpdateDto.getPanNumber());
        employee.setEmployeeName(employeeUpdateDto.getEmployeeName());
        employee.setEmail(employeeUpdateDto.getEmail());
        employee.setMobileNumber(employeeUpdateDto.getMobileNumber());
        employee.setRegisterNumber(employeeUpdateDto.getRegisterNumber());
        employee.setSection(employeeUpdateDto.getSection());
        employee.setFatherName(employeeUpdateDto.getFatherName());
        employee.setMotherName(employeeUpdateDto.getMotherName());
        employee.setDateOfBirth(employeeUpdateDto.getDateOfBirth());
        employee.setReligion(employeeUpdateDto.getReligion());
        employee.setCommunity(employeeUpdateDto.getCommunity());
        employee.setCaste(employeeUpdateDto.getCaste());
        employee.setPersonalIdentificationmark1(employeeUpdateDto.getPersonalIdentificationmark1());
        employee.setPersonalIdentificationmark2(employeeUpdateDto.getPersonalIdentificationmark2());
        employee.setCurrentDesignation(employeeUpdateDto.getCurrentDesignation());
        employee.setDateOfEntry(employeeUpdateDto.getDateOfEntry());
        employee.setDistrict(employeeUpdateDto.getDistrict());
        employee.setNativePlaceAndTaluk(employeeUpdateDto.getNativePlaceAndTaluk());
        employee.setMainEmployeeType(employeeUpdateDto.getMainEmployeeType());
        employee.setGender(employeeUpdateDto.getGender());

        // Note: createdBy should not be updated, only set during creation
        // employee.setCreatedBy(employeeUpdateDto.getCreatedBy());

        // Save updated employee
        Employee updatedEmployee = employeeRepo.save(employee);

        // 3. Update Profile
        updateProfile(employeeUpdateDto, id);

        // 4. Update Account Details
        if (employeeUpdateDto.getAccountDetails() != null) {
            updateAccountDetails(employeeUpdateDto.getAccountDetails(), id);
        }

        // 5. Update Service History records
        if (employeeUpdateDto.getServiceHistory() != null && !employeeUpdateDto.getServiceHistory().isEmpty()) {
            updateServiceHistory(employeeUpdateDto.getServiceHistory(), updatedEmployee);
        }

        // 6. Update Leave balances
        if (employeeUpdateDto.getLeaveBalances() != null && !employeeUpdateDto.getLeaveBalances().isEmpty()) {
            updateLeaveBalances(employeeUpdateDto.getLeaveBalances(), updatedEmployee);
        }

        // 7. Update Education Qualifications
        if (employeeUpdateDto.getEducationQualifications() != null && !employeeUpdateDto.getEducationQualifications().isEmpty()) {
            updateEducationQualifications(employeeUpdateDto.getEducationQualifications(), id);
        }

        // 8. Update Punishment Details
        if (employeeUpdateDto.getPunishmentDetails() != null && !employeeUpdateDto.getPunishmentDetails().isEmpty()) {
            updatePunishmentDetails(employeeUpdateDto.getPunishmentDetails(), id);
        }

        // 9. Update Training Details
        if (employeeUpdateDto.getTrainingDetails() != null && !employeeUpdateDto.getTrainingDetails().isEmpty()) {
            updateTrainingDetails(employeeUpdateDto.getTrainingDetails(), id);
        }

        // 10. Update Nominees
        if (employeeUpdateDto.getNominees() != null && !employeeUpdateDto.getNominees().isEmpty()) {
            updateNominees(employeeUpdateDto.getNominees(), id);
        }

        // 11. Update Documents (create new placeholders if provided)
        if (employeeUpdateDto.getDocuments() != null && !employeeUpdateDto.getDocuments().isEmpty()) {
            createDocuments(employeeUpdateDto.getDocuments(), id);
        }

        // 12. Update Salary Details
        if (employeeUpdateDto.getSalaryDetails() != null) {
            updateSalaryDetails(employeeUpdateDto.getSalaryDetails(), id);
        }

        return updatedEmployee;
    }

    /**
     * Update employee with files including nominee photos
     * This method handles MultipartFile arrays for nominee photos during updates
     */
    @Transactional
    public Employee updateEmployeeWithFiles(Long id, EmployeeCreationDto employeeUpdateDto,
                                          MultipartFile profilePhoto,
                                          MultipartFile[] nomineePhotos,
                                          MultipartFile[] documents) {
        System.out.println("=== DEBUG: updateEmployeeWithFiles called for employee " + id + " ===");
        System.out.println("Profile photo provided: " + (profilePhoto != null && !profilePhoto.isEmpty()));
        System.out.println("Nominee photos provided: " + (nomineePhotos != null ? nomineePhotos.length : 0));
        System.out.println("Documents provided: " + (documents != null ? documents.length : 0));

        // 1. Get existing employee
        Employee employee = employeeRepo.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + id, null, null));

        // 2. Update employee basic details (same as existing method)
        employee.setEcpfNumber(employeeUpdateDto.getEcpfNumber());
        employee.setEmpId(employeeUpdateDto.getEmpId());
        employee.setPanNumber(employeeUpdateDto.getPanNumber());
        employee.setEmployeeName(employeeUpdateDto.getEmployeeName());
        employee.setEmail(employeeUpdateDto.getEmail());
        employee.setMobileNumber(employeeUpdateDto.getMobileNumber());
        employee.setRegisterNumber(employeeUpdateDto.getRegisterNumber());
        employee.setSection(employeeUpdateDto.getSection());
        employee.setFatherName(employeeUpdateDto.getFatherName());
        employee.setMotherName(employeeUpdateDto.getMotherName());
        employee.setDateOfBirth(employeeUpdateDto.getDateOfBirth());
        employee.setReligion(employeeUpdateDto.getReligion());
        employee.setCommunity(employeeUpdateDto.getCommunity());
        employee.setCaste(employeeUpdateDto.getCaste());
        employee.setPersonalIdentificationmark1(employeeUpdateDto.getPersonalIdentificationmark1());
        employee.setPersonalIdentificationmark2(employeeUpdateDto.getPersonalIdentificationmark2());
        employee.setCurrentDesignation(employeeUpdateDto.getCurrentDesignation());
        employee.setDateOfEntry(employeeUpdateDto.getDateOfEntry());
        employee.setDistrict(employeeUpdateDto.getDistrict());
        employee.setNativePlaceAndTaluk(employeeUpdateDto.getNativePlaceAndTaluk());
        employee.setMainEmployeeType(employeeUpdateDto.getMainEmployeeType());
        employee.setGender(employeeUpdateDto.getGender());

        // Save updated employee
        Employee updatedEmployee = employeeRepo.save(employee);

        // 3. Update profile photo if provided
        if (profilePhoto != null && !profilePhoto.isEmpty()) {
            try {
                System.out.println("UPDATE_WITH_FILES - Updating profile photo: " + profilePhoto.getOriginalFilename());
                String profilePhotoUrl = documentService.uploadPhotoAndUpdateEmployee(id, profilePhoto);
                System.out.println("UPDATE_WITH_FILES - Profile photo updated: " + profilePhotoUrl);
            } catch (Exception e) {
                System.err.println("UPDATE_WITH_FILES - Error updating profile photo: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 4. Update nominees with photos
        if (employeeUpdateDto.getNominees() != null && !employeeUpdateDto.getNominees().isEmpty()) {
            if (nomineePhotos != null && nomineePhotos.length > 0) {
                System.out.println("UPDATE_WITH_FILES - Updating nominees WITH photos");
                updateNomineesWithPhotos(employeeUpdateDto.getNominees(), id, nomineePhotos);
            } else {
                System.out.println("UPDATE_WITH_FILES - Updating nominees WITHOUT photos");
                updateNominees(employeeUpdateDto.getNominees(), id);
            }
        }

        // 5. Upload new documents if provided
        if (documents != null && documents.length > 0) {
            for (MultipartFile document : documents) {
                if (document != null && !document.isEmpty()) {
                    try {
                        System.out.println("UPDATE_WITH_FILES - Uploading document: " + document.getOriginalFilename());
                        digitization.digitization.module.Document savedDoc = documentService.storeDocument(id, document);
                        System.out.println("UPDATE_WITH_FILES - Document uploaded: " + savedDoc.getFileUrl());
                    } catch (Exception e) {
                        System.err.println("UPDATE_WITH_FILES - Error uploading document " + document.getOriginalFilename() + ": " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            }
        }

        // 6. Update other related records (same as existing method)
        updateProfile(employeeUpdateDto, id);

        if (employeeUpdateDto.getAccountDetails() != null) {
            updateAccountDetails(employeeUpdateDto.getAccountDetails(), id);
        }

        if (employeeUpdateDto.getServiceHistory() != null && !employeeUpdateDto.getServiceHistory().isEmpty()) {
            updateServiceHistory(employeeUpdateDto.getServiceHistory(), updatedEmployee);
        }

        if (employeeUpdateDto.getLeaveBalances() != null && !employeeUpdateDto.getLeaveBalances().isEmpty()) {
            updateLeaveBalances(employeeUpdateDto.getLeaveBalances(), updatedEmployee);
        }

        if (employeeUpdateDto.getEducationQualifications() != null && !employeeUpdateDto.getEducationQualifications().isEmpty()) {
            updateEducationQualifications(employeeUpdateDto.getEducationQualifications(), id);
        }

        if (employeeUpdateDto.getPunishmentDetails() != null && !employeeUpdateDto.getPunishmentDetails().isEmpty()) {
            updatePunishmentDetails(employeeUpdateDto.getPunishmentDetails(), id);
        }

        if (employeeUpdateDto.getTrainingDetails() != null && !employeeUpdateDto.getTrainingDetails().isEmpty()) {
            updateTrainingDetails(employeeUpdateDto.getTrainingDetails(), id);
        }

        if (employeeUpdateDto.getDocuments() != null && !employeeUpdateDto.getDocuments().isEmpty()) {
            createDocuments(employeeUpdateDto.getDocuments(), id);
        }

        if (employeeUpdateDto.getSalaryDetails() != null) {
            updateSalaryDetails(employeeUpdateDto.getSalaryDetails(), id);
        }

        System.out.println("UPDATE_WITH_FILES - Employee update completed successfully: " + id);
        return updatedEmployee;
    }

    @Override
    @Transactional
    public boolean deleteEmployee(Long empId) {
        try {
            Optional<Employee> employeeOpt = employeeRepo.findById(empId);
            if (!employeeOpt.isPresent()) {
                System.out.println("Employee not found with ID: " + empId);
                return false;
            }

            Employee employee = employeeOpt.get();
            System.out.println("Starting deletion process for employee ID: " + empId + " (" + employee.getEmployeeName() + ")");

            // 1. Delete all documents and associated files
            deleteEmployeeDocuments(empId);

            // 2. Delete employee profile photo if exists
            deleteEmployeeProfilePhoto(employee);

            // 3. Delete related records explicitly (in case cascade doesn't work properly)
            deleteEmployeeRelatedRecords(empId);

            // 4. Delete employee status
            deleteEmployeeStatus(empId);

            // 5. Finally delete the employee record (cascade should handle remaining relationships)
            employeeRepo.deleteById(empId);

            System.out.println("Successfully deleted employee ID: " + empId + " and all related records");
            return true;

        } catch (Exception e) {
            System.err.println("Error deleting employee ID " + empId + ": " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to delete employee and related records: " + e.getMessage(), e);
        }
    }

    /**
     * Delete all documents associated with an employee and their files from storage
     */
    private void deleteEmployeeDocuments(Long empId) {
        try {
            List<Document> documents = documentRepository.findByEmployee_Id(empId);
            for (Document document : documents) {
                // Delete file from storage
                if (document.getFilePath() != null) {
                    fileStorageService.deleteFile(document.getFilePath());
                }
                // Delete document record
                documentRepository.delete(document);
            }
            System.out.println("Deleted " + documents.size() + " documents for employee ID: " + empId);
        } catch (Exception e) {
            System.err.println("Error deleting documents for employee ID " + empId + ": " + e.getMessage());
        }
    }

    /**
     * Delete employee profile photo from storage
     */
    private void deleteEmployeeProfilePhoto(Employee employee) {
        try {
            if (employee.getProfilePhoto() != null && !employee.getProfilePhoto().isEmpty()) {
                fileStorageService.deleteFile(employee.getProfilePhoto());
                System.out.println("Deleted profile photo for employee ID: " + employee.getId());
            }
        } catch (Exception e) {
            System.err.println("Error deleting profile photo for employee ID " + employee.getId() + ": " + e.getMessage());
        }
    }

    /**
     * Delete all related records for an employee
     */
    private void deleteEmployeeRelatedRecords(Long empId) {
        try {
            // Delete salary details
            List<SalaryDetails> salaryDetailsList = salaryDetailsRepository.findAllByEmployeeIdOrderByUpdatedAtDesc(empId);
            if (!salaryDetailsList.isEmpty()) {
                salaryDetailsRepository.deleteAll(salaryDetailsList);
                System.out.println("Deleted " + salaryDetailsList.size() + " salary details records for employee ID: " + empId);
            }

            // Delete service history
            List<ServiceHistory> serviceHistoryList = serviceRepository.findByEmployeeIdOrderByDateDesc(empId);
            if (!serviceHistoryList.isEmpty()) {
                serviceRepository.deleteAll(serviceHistoryList);
                System.out.println("Deleted " + serviceHistoryList.size() + " service history records for employee ID: " + empId);
            }

            // Delete leave records
            List<Leave> leaveList = leaveRepository.findByEmployeeId(empId);
            if (!leaveList.isEmpty()) {
                leaveRepository.deleteAll(leaveList);
                System.out.println("Deleted " + leaveList.size() + " leave records for employee ID: " + empId);
            }

            // Delete education qualifications
            List<EducationQualification> educationList = educationQualificationRepository.findByEmployeeId(empId);
            if (!educationList.isEmpty()) {
                educationQualificationRepository.deleteAll(educationList);
                System.out.println("Deleted " + educationList.size() + " education qualification records for employee ID: " + empId);
            }

            // Delete punishment details
            List<PunishmentDetails> punishmentList = punishmentDetailsRepository.findByEmployeeId(empId);
            if (!punishmentList.isEmpty()) {
                punishmentDetailsRepository.deleteAll(punishmentList);
                System.out.println("Deleted " + punishmentList.size() + " punishment details records for employee ID: " + empId);
            }

            // Delete training details
            List<TrainingDetails> trainingList = trainingDetailsRepository.findByEmployeeId(empId);
            if (!trainingList.isEmpty()) {
                trainingDetailsRepository.deleteAll(trainingList);
                System.out.println("Deleted " + trainingList.size() + " training details records for employee ID: " + empId);
            }

            // Delete nominees
            List<Nominee> nomineeList = nomineeRepository.findByEmployeeId(empId);
            if (!nomineeList.isEmpty()) {
                nomineeRepository.deleteAll(nomineeList);
                System.out.println("Deleted " + nomineeList.size() + " nominee records for employee ID: " + empId);
            }

            // Delete profile
            Optional<Profile> profileOpt = profileRepository.findByEmployeeId(empId);
            if (profileOpt.isPresent()) {
                profileRepository.delete(profileOpt.get());
                System.out.println("Deleted profile record for employee ID: " + empId);
            }

            // Delete account details
            Optional<AccountDetails> accountDetailsOpt = accountDetailsRepository.findByEmployeeId(empId);
            if (accountDetailsOpt.isPresent()) {
                accountDetailsRepository.delete(accountDetailsOpt.get());
                System.out.println("Deleted account details record for employee ID: " + empId);
            }

        } catch (Exception e) {
            System.err.println("Error deleting related records for employee ID " + empId + ": " + e.getMessage());
            throw e;
        }
    }

    /**
     * Delete employee status record
     */
    private void deleteEmployeeStatus(Long empId) {
        try {
            if (employeeStatusService.existsByEmpId(empId)) {
                employeeStatusService.deleteEmployeeStatus(empId);
                System.out.println("Deleted employee status for employee ID: " + empId);
            }
        } catch (Exception e) {
            System.err.println("Error deleting employee status for employee ID " + empId + ": " + e.getMessage());
        }
    }

    @Override
    public List<digitization.digitization.dto.EmployeeDetailDto> getEmployeesByCreatedBy(String createdBy) {
        try {
            // Get employee statuses by createdBy
            List<EmployeeStatus> employeeStatuses = employeeStatusRepository.findByCreatedByOrderByUpdatedAtDesc(createdBy);

            List<digitization.digitization.dto.EmployeeDetailDto> employeeDetails = new ArrayList<>();

            for (EmployeeStatus empStatus : employeeStatuses) {
                try {
                    // Get employee with all details
                    digitization.digitization.dto.EmployeeDetailDto employeeDetail = getEmployeeWithAllDetails(empStatus.getEmpId());
                    employeeDetails.add(employeeDetail);
                } catch (Exception e) {
                    // Log error but continue with other employees
                    System.out.println("Warning: Could not get details for employee ID " + empStatus.getEmpId() + ": " + e.getMessage());
                }
            }

            return employeeDetails;
        } catch (Exception e) {
            throw new RuntimeException("Error getting employees by createdBy: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EmployeeListDto> getEmployeesBasicByCreatedBy(String createdBy) {
        try {
            // Get employee statuses by createdBy
            List<EmployeeStatus> employeeStatuses = employeeStatusRepository.findByCreatedByOrderByUpdatedAtDesc(createdBy);

            List<EmployeeListDto> employeeList = new ArrayList<>();

            for (EmployeeStatus empStatus : employeeStatuses) {
                try {
                    // Get basic employee info
                    Optional<Employee> employeeOpt = employeeRepo.findById(empStatus.getEmpId());
                    if (employeeOpt.isPresent()) {
                        Employee employee = employeeOpt.get();
                        EmployeeListDto employeeListDto = new EmployeeListDto();
                        employeeListDto.setId(employee.getId());
                        employeeListDto.setEmployeeName(employee.getEmployeeName());
                        employeeListDto.setEcpfNumber(employee.getEcpfNumber());
                        employeeListDto.setPanNumber(employee.getPanNumber());
                        employeeListDto.setEmail(employee.getEmail());
                        employeeListDto.setMobileNumber(employee.getMobileNumber());
                        employeeListDto.setCurrentDesignation(employee.getCurrentDesignation());
                        employeeListDto.setSection(employee.getSection());
                        employeeListDto.setDistrict(employee.getDistrict());
                        employeeListDto.setStatus(empStatus.getStatus());
                        employeeListDto.setCreatedBy(empStatus.getCreatedBy());
                        employeeListDto.setCreatedAt(empStatus.getCreatedAt());

                        employeeList.add(employeeListDto);
                    }
                } catch (Exception e) {
                    // Log error but continue with other employees
                    System.out.println("Warning: Could not get basic info for employee ID " + empStatus.getEmpId() + ": " + e.getMessage());
                }
            }

            return employeeList;
        } catch (Exception e) {
            throw new RuntimeException("Error getting basic employees by createdBy: " + e.getMessage(), e);
        }
    }

    @Override
    public List<digitization.digitization.dto.EmployeeDetailDto> getEmployeeDetailsByCreatedBy(String createdBy) {
        try {
            // Get employees directly from employee table by createdBy
            List<Employee> employees = employeeRepo.findByCreatedByOrderByIdDesc(createdBy);

            List<digitization.digitization.dto.EmployeeDetailDto> employeeDetails = new ArrayList<>();

            for (Employee employee : employees) {
                try {
                    // Get complete employee details for each employee
                    digitization.digitization.dto.EmployeeDetailDto employeeDetail = getEmployeeWithAllDetails(employee.getId());
                    employeeDetails.add(employeeDetail);
                } catch (Exception e) {
                    // Log error but continue with other employees
                    System.out.println("Warning: Could not get details for employee ID " + employee.getId() + ": " + e.getMessage());
                }
            }

            return employeeDetails;
        } catch (Exception e) {
            throw new RuntimeException("Error getting employee details by createdBy: " + e.getMessage(), e);
        }
    }

    // Helper methods for creating related records
    private void createProfile(EmployeeCreationDto employeeCreationDto, Long employeeId) {
        Profile profile = new Profile();

        // Set the employee relationship
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            profile.setEmployee(employee);
        }

        if (employeeCreationDto.getProfile() != null) {
            ProfileDto profileDto = employeeCreationDto.getProfile();
            profile.setFatherName(profileDto.getFatherName());
            profile.setMotherName(profileDto.getMotherName());
            profile.setDateOfBirth(profileDto.getDateOfBirth());
            profile.setCommunity(profileDto.getCommunity());
            profile.setCaste(profileDto.getCaste());
            profile.setDistrict(profileDto.getDistrict());
            profile.setNativeplaceandtaluk(profileDto.getNativeplaceandtaluk());
            profile.setGender(profileDto.getGender());
            // Present address fields
            profile.setPresentDoorNo(profileDto.getPresentDoorNo());
            profile.setPresentBuildingName(profileDto.getPresentBuildingName());
            profile.setPresentStreetAddress(profileDto.getPresentStreetAddress());
            profile.setPresentCity(profileDto.getPresentCity());
            profile.setPresentPincode(profileDto.getPresentPincode());

            // Permanent address fields
            profile.setPermanentDoorNo(profileDto.getPermanentDoorNo());
            profile.setPermanentBuildingName(profileDto.getPermanentBuildingName());
            profile.setPermanentStreetAddress(profileDto.getPermanentStreetAddress());
            profile.setPermanentCity(profileDto.getPermanentCity());
            profile.setPermanentPincode(profileDto.getPermanentPincode());

            profile.setProfilephoto(profileDto.getProfilephoto());
            profile.setEmployeeType(profileDto.getEmployeeType());
            profile.setEmail(profileDto.getEmail());
        } else {
            profile.setFatherName(employeeCreationDto.getFatherName());
            profile.setMotherName(employeeCreationDto.getMotherName());
            profile.setDateOfBirth(employeeCreationDto.getDateOfBirth());
            profile.setCommunity(employeeCreationDto.getCommunity());
            profile.setCaste(employeeCreationDto.getCaste());
            profile.setDistrict(employeeCreationDto.getDistrict());
            profile.setNativeplaceandtaluk(employeeCreationDto.getNativePlaceAndTaluk());
            profile.setGender(employeeCreationDto.getGender());

            // Set address fields to null since employee only has full address text
            profile.setPresentDoorNo(null);
            profile.setPresentBuildingName(null);
            profile.setPresentStreetAddress(null);
            profile.setPresentCity(null);
            profile.setPresentPincode(null);
            profile.setPermanentDoorNo(null);
            profile.setPermanentBuildingName(null);
            profile.setPermanentStreetAddress(null);
            profile.setPermanentCity(null);
            profile.setPermanentPincode(null);

            // Set profile photo and employee type to null as defaults
            profile.setProfilephoto(null);
            profile.setEmployeeType(null);
            profile.setEmail(employeeCreationDto.getEmail());
        }

        profileRepository.save(profile);
    }

    private void createAccountDetails(AccountDetailsDto accountDetailsDto, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            AccountDetails accountDetails = new AccountDetails();
            accountDetails.setBankaccountnumber(accountDetailsDto.getBankaccountnumber());
            accountDetails.setIfsccode(accountDetailsDto.getIfsccode());
            accountDetails.setBankname(accountDetailsDto.getBankname());
            accountDetails.setUannumber(accountDetailsDto.getUannumber());
            accountDetails.setAadharnumber(accountDetailsDto.getAadharnumber());
            accountDetails.setEmployee(employee);

            accountDetailsRepository.save(accountDetails);
        }
    }

    private void createServiceHistory(List<ServiceHistoryDto> serviceHistoryDtos, Employee employee) {
        for (ServiceHistoryDto dto : serviceHistoryDtos) {
            ServiceHistory serviceHistory = new ServiceHistory();
            serviceHistory.setDate(dto.getDate());
            serviceHistory.setType(dto.getType());
            serviceHistory.setStatus(dto.getStatus());
            serviceHistory.setAppointmenttype(dto.getAppointmenttype());
            serviceHistory.setModeofappointment(dto.getModeofappointment());
            serviceHistory.setDateofappointment(dto.getDateofappointment());
            serviceHistory.setProceedingorderdate(dto.getProceedingorderdate());
            serviceHistory.setProceedingorderno(dto.getProceedingorderno());
            serviceHistory.setJoiningdate(dto.getJoiningdate());
            System.out.println("getting value" + dto.getPromoteddate());
            serviceHistory.setPromoteddate(dto.getPromoteddate());
            serviceHistory.setFromdesignation(dto.getFromdesignation());
            serviceHistory.setTopromoted(dto.getTopromoted());
            serviceHistory.setFromdate(dto.getFromdate());
            serviceHistory.setTodate(dto.getTodate());

            // Handle both typeofincrement and incrementtype for backward compatibility
            String incrementType = dto.getTypeofincrement();
            if (incrementType == null && dto.getIncrementtype() != null) {
                incrementType = dto.getIncrementtype();
                System.out.println("Using incrementtype field: " + incrementType);
            }
            serviceHistory.setTypeofincrement(incrementType);

            serviceHistory.setFromplace(dto.getFromplace());
            serviceHistory.setToplace(dto.getToplace());
            serviceHistory.setDesignation(dto.getDesignation());
            serviceHistory.setOriginaldesignation(dto.getOriginaldesignation());
            serviceHistory.setParentdepartment(dto.getParentdepartment());

            // Set salary-related fields
            serviceHistory.setBasicPay(dto.getBasicPay());

            // Handle both da and daField for backward compatibility
            Double daValue = dto.getDa();
            if (daValue == null && dto.getDaField() != null) {
                daValue = dto.getDaField();
                System.out.println("Using daField: " + daValue);
            }
            serviceHistory.setDa(daValue);

            serviceHistory.setBasicPlusDA(dto.getBasicPlusDA());

            // Set other fields that might be missing
            serviceHistory.setCaseDate(dto.getCaseDate());
            serviceHistory.setCaseNumber(dto.getCaseNumber());
            serviceHistory.setDescription(dto.getDescription());

            // Handle involved persons - use the array method to ensure proper conversion
            Long[] personsInvolved = dto.getPersonsInvolved();
            if (personsInvolved != null && personsInvolved.length > 0) {
                serviceHistory.setPersonsInvolved(personsInvolved);
                System.out.println("CREATE - Set personsInvolved: " + java.util.Arrays.toString(personsInvolved) + " -> stored as: '" + serviceHistory.getInvolvedPersons() + "'");
            } else {
                serviceHistory.setInvolvedPersons(null);
                System.out.println("CREATE - No personsInvolved provided, set to null");
            }

            serviceHistory.setPresentStatus(dto.getPresentStatus());
            serviceHistory.setPunishmentdate(dto.getPunishmentdate());
            serviceHistory.setWithholdingFromDate(dto.getWithholdingFromDate());
            serviceHistory.setWithholdingToDate(dto.getWithholdingToDate());
            serviceHistory.setHasCaseDetails(dto.getHasCaseDetails());
            serviceHistory.setPunishmenttype(dto.getPunishmenttype());
            serviceHistory.setCasedetails(dto.getCasedetails());

            serviceHistory.setEmployee(employee);

            serviceRepository.save(serviceHistory);
        }
    }

    private void createLeaveBalances(List<LeaveDto> leaveDtos, Employee employee) {
        for (LeaveDto dto : leaveDtos) {
            Leave leave = new Leave();
            leave.setLeaveType(dto.getLeaveType());
            leave.setOpeningBalance(dto.getOpeningBalance());
            leave.setClosingBalance(dto.getClosingBalance());
            leave.setEntryDate(dto.getEntryDate());
            leave.setEmployee(employee);

            leaveRepository.save(leave);
        }
    }

    private void createEducationQualifications(List<EducationQualificationDto> educationDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            for (EducationQualificationDto dto : educationDtos) {
                EducationQualification education = new EducationQualification();
                education.setQualification(dto.getQualification());
                education.setCoursename(dto.getCoursename());
                education.setSchoolname(dto.getSchoolname());
                education.setCollegename(dto.getCollegename());
                education.setUniversityname(dto.getUniversityname());
                education.setSpecialization(dto.getSpecialization());
                education.setEmployee(employee);

                educationQualificationRepository.save(education);
            }
        }
    }

    private void createPunishmentDetails(List<PunishmentDetailsDto> punishmentDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            for (PunishmentDetailsDto dto : punishmentDtos) {
                PunishmentDetails punishment = new PunishmentDetails();
                punishment.setPunishmenttype(dto.getPunishmenttype());
                punishment.setDate(dto.getDate());
                punishment.setCasedetails(dto.getCasedetails());
                punishment.setCaseDate(dto.getCaseDate());
                punishment.setCaseNumber(dto.getCaseNumber());
                punishment.setDescription(dto.getDescription());
                punishment.setPersonsInvolved(dto.getPersonsInvolved());
                punishment.setPresentStatus(dto.getPresentStatus());
                punishment.setWithHoldingFromDate(dto.getWithHoldingFromDate());
                punishment.setWithHoldingToDate(dto.getWithHoldingToDate());
                punishment.setEmployee(employee);

                punishmentDetailsRepository.save(punishment);
            }
        }
    }

    private void createTrainingDetails(List<TrainingDetailsDto> trainingDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            for (TrainingDetailsDto dto : trainingDtos) {
                TrainingDetails training = new TrainingDetails();
                training.setTrainingtype(dto.getTrainingtype());
                training.setDate(dto.getDate());
                training.setEmployee(employee);

                trainingDetailsRepository.save(training);
            }
        }
    }

    private void createNominees(List<NomineeDto> nomineeDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            System.out.println("=== DEBUG: createNominees called for employee " + employeeId + " with " + nomineeDtos.size() + " nominees ===");

            for (int i = 0; i < nomineeDtos.size(); i++) {
                NomineeDto dto = nomineeDtos.get(i);
                System.out.println("Processing nominee " + (i+1) + ": " + dto.getNomineename());
                System.out.println("  Nominee photo from DTO: " + dto.getNomineePhoto());

                Nominee nominee = new Nominee();
                nominee.setNomineename(dto.getNomineename());
                nominee.setAddress(dto.getAddress());
                nominee.setRelationship(dto.getRelationship());
                nominee.setAge(dto.getAge());
                nominee.setPercentageofshare(dto.getPercentageofshare());
                nominee.setGender(dto.getGender());

                // Set nominee photo - ensure it follows the same pattern as profile photos
                if (dto.getNomineePhoto() != null && !dto.getNomineePhoto().trim().isEmpty()) {
                    System.out.println("CREATION - Processing nominee photo: " + dto.getNomineePhoto() + " for employee: " + employeeId);
                    String processedPhotoPath = processAndStoreNomineePhoto(dto.getNomineePhoto(), employeeId);
                    System.out.println("CREATION - Processed nominee photo result: " + processedPhotoPath);
                    nominee.setNomineePhoto(processedPhotoPath);
                    System.out.println("CREATION - Set nominee photo in entity: " + nominee.getNomineePhoto());
                } else {
                    System.out.println("CREATION - No nominee photo provided for " + dto.getNomineename() + ", setting to null");
                    nominee.setNomineePhoto(null);
                }

                nominee.setEmployee(employee);

                Nominee savedNominee = nomineeRepository.save(nominee);
                System.out.println("CREATION - Saved nominee to database: " + savedNominee.getNomineename() + " with photo: " + savedNominee.getNomineePhoto());
            }
            System.out.println("=== DEBUG: createNominees completed for employee " + employeeId + " ===");
        }
    }

    private void createNomineesWithPhotos(List<NomineeDto> nomineeDtos, Long employeeId, MultipartFile[] nomineePhotos) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            for (int i = 0; i < nomineeDtos.size(); i++) {
                NomineeDto dto = nomineeDtos.get(i);
                Nominee nominee = new Nominee();
                nominee.setNomineename(dto.getNomineename());
                nominee.setAddress(dto.getAddress());
                nominee.setRelationship(dto.getRelationship());
                nominee.setAge(dto.getAge());
                nominee.setPercentageofshare(dto.getPercentageofshare());
                nominee.setGender(dto.getGender());

                // Upload nominee photo if provided
                if (i < nomineePhotos.length && nomineePhotos[i] != null && !nomineePhotos[i].isEmpty()) {
                    try {
                        System.out.println("Uploading nominee photo for " + dto.getNomineename() + ": " + nomineePhotos[i].getOriginalFilename());

                        // Use the same logic as the working update process - store file using FileStorageService
                        digitization.digitization.dto.FileStorageResult storageResult = fileStorageService.storeFile(nomineePhotos[i], employeeId);

                        // Also create document record for tracking
                        digitization.digitization.dto.DocumentDto documentDto = documentService.uploadDocument(nomineePhotos[i], employeeId);

                        // Store the relative path (just the filename) in the nominee table - same as update process
                        nominee.setNomineePhoto(storageResult.getRelativePath());

                        System.out.println("Nominee photo uploaded and stored: " + storageResult.getRelativePath());

                    } catch (Exception e) {
                        System.out.println("Error uploading nominee photo for " + dto.getNomineename() + ": " + e.getMessage());
                        e.printStackTrace();
                        nominee.setNomineePhoto(null);
                    }
                } else {
                    nominee.setNomineePhoto(null);
                }

                nominee.setEmployee(employee);
                Nominee savedNominee = nomineeRepository.save(nominee);
                System.out.println("Saved nominee to database: " + savedNominee.getNomineename() + " with photo: " + savedNominee.getNomineePhoto());
            }
        }
    }

    private void createDocuments(List<digitization.digitization.dto.DocumentCreationDto> documentDtos, Long employeeId) {
        for (digitization.digitization.dto.DocumentCreationDto dto : documentDtos) {
            // Create document placeholder - actual file will be uploaded separately
            documentService.createDocumentPlaceholder(
                employeeId,
                dto.getFileName(),
                dto.getFileType() != null ? dto.getFileType() : "application/pdf"
            );
        }
    }

    private void createSalaryDetails(SalaryDetailsDto salaryDetailsDto, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            SalaryDetails salaryDetails = new SalaryDetails();
            salaryDetails.setLastSalaryRevisedDate(salaryDetailsDto.getLastSalaryRevisedDate());
            salaryDetails.setCurrentWithdrawSalary(salaryDetailsDto.getCurrentWithdrawSalary());
            salaryDetails.setGroup(salaryDetailsDto.getGroup());
            salaryDetails.setPayband(salaryDetailsDto.getPayband());
            salaryDetails.setGradepay(salaryDetailsDto.getGradepay());
            salaryDetails.setEmployee(employee);
            salaryDetails.setCreatedBy(salaryDetailsDto.getCreatedBy());

            // Use the service to create salary details
            SalaryDetailsDto dto = new SalaryDetailsDto();
            dto.setLastSalaryRevisedDate(salaryDetailsDto.getLastSalaryRevisedDate());
            dto.setCurrentWithdrawSalary(salaryDetailsDto.getCurrentWithdrawSalary());
            dto.setGroup(salaryDetailsDto.getGroup());
            dto.setPayband(salaryDetailsDto.getPayband());
            dto.setGradepay(salaryDetailsDto.getGradepay());
            dto.setEmployeeId(employeeId);
            dto.setCreatedBy(salaryDetailsDto.getCreatedBy());

            salaryDetailsService.createSalaryDetails(dto);
        }
    }
    private void createStatusDetails(EmployeeStatusDto employeeStatusDto, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            EmployeeStatus statusDetails = new EmployeeStatus();
            statusDetails.setStatus(employeeStatusDto.getStatus());
            statusDetails.setEmpId(employeeId);
            statusDetails.setCreatedBy(employeeStatusDto.getCreatedBy());

            // Use the service to create salary details
            EmployeeStatusDto dto = new EmployeeStatusDto();
            dto.setStatus(employeeStatusDto.getStatus());
            dto.setStatus(employeeStatusDto.getStatus());
            dto.setEmpId(employeeId);
            dto.setCreatedBy(statusDetails.getCreatedBy());
            dto.setStatus("PENDING");
            employeeStatusService.createEmployeeStatus(employeeId, employeeStatusDto.getStatus());
        }
    }

    private void updateSalaryDetails(SalaryDetailsDto salaryDetailsDto, Long employeeId) {
        try {
            // Check if salary details exist for this employee
            if (salaryDetailsService.existsByEmployeeId(employeeId)) {
                // Get existing salary details
                SalaryDetailsDto existingSalaryDetails = salaryDetailsService.getSalaryDetailsByEmployeeId(employeeId);

                // Update the existing record
                salaryDetailsDto.setEmployeeId(employeeId);
                salaryDetailsService.updateSalaryDetails(existingSalaryDetails.getId(), salaryDetailsDto);
            } else {
                // Create new salary details if none exist
                createSalaryDetails(salaryDetailsDto, employeeId);
            }
        } catch (Exception e) {
            // If getting existing salary details fails, try to create new ones
            createSalaryDetails(salaryDetailsDto, employeeId);
        }
    }

    private SalaryDetailsDto getSalaryDetailsByEmployeeId(Long employeeId) {
        try {
            return salaryDetailsService.getSalaryDetailsByEmployeeId(employeeId);
        } catch (Exception e) {
            // Return null if no salary details found
            return null;
        }
    }

    // Update methods for related records
    private void updateProfile(EmployeeCreationDto employeeUpdateDto, Long employeeId) {
        // Find existing profile by employee email or create new one
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null && employee.getEmail() != null) {
//            java.util.Optional<Profile> existingProfileOpt = profileRepository.findByEmail(employee.getEmail());
            java.util.Optional<Profile> existingProfileOpt = profileRepository.findByEmployeeId(employee.getId());


            Profile profile;

            if (existingProfileOpt.isPresent()) {
                profile = existingProfileOpt.get();
            } else {
                profile = new Profile();
                profile.setEmployee(employee);
            }

            if (employeeUpdateDto.getProfile() != null) {
                ProfileDto profileDto = employeeUpdateDto.getProfile();
                profile.setFatherName(profileDto.getFatherName());
                profile.setMotherName(profileDto.getMotherName());
                profile.setDateOfBirth(profileDto.getDateOfBirth());
                profile.setCommunity(profileDto.getCommunity());
                profile.setCaste(profileDto.getCaste());
                profile.setDistrict(profileDto.getDistrict());
                profile.setNativeplaceandtaluk(profileDto.getNativeplaceandtaluk());
//                profile.setPresentaddress(profileDto.getPresentaddress());
//                profile.setPermanentaddress(profileDto.getPermanentaddress());
                profile.setEmail(profileDto.getEmail());
                profile.setGender(profileDto.getGender());
            } else {
                // Use employee data as default
                profile.setFatherName(employeeUpdateDto.getFatherName());
                profile.setMotherName(employeeUpdateDto.getMotherName());
                profile.setDateOfBirth(employeeUpdateDto.getDateOfBirth());
                profile.setCommunity(employeeUpdateDto.getCommunity());
                profile.setCaste(employeeUpdateDto.getCaste());
                profile.setDistrict(employeeUpdateDto.getDistrict());
                profile.setNativeplaceandtaluk(employeeUpdateDto.getNativePlaceAndTaluk());
                profile.setGender(employeeUpdateDto.getGender());
                profile.setEmail(employeeUpdateDto.getEmail());
            }

            profileRepository.save(profile);
        }
    }

    private void updateAccountDetails(AccountDetailsDto accountDetailsDto, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            java.util.Optional<AccountDetails> existingAccountOpt = accountDetailsRepository.findByEmployeeId(employeeId);
            AccountDetails accountDetails;

            if (existingAccountOpt.isPresent()) {
                accountDetails = existingAccountOpt.get();
            } else {
                accountDetails = new AccountDetails();
                accountDetails.setEmployee(employee);
            }

            accountDetails.setBankaccountnumber(accountDetailsDto.getBankaccountnumber());
            accountDetails.setIfsccode(accountDetailsDto.getIfsccode());
            accountDetails.setBankname(accountDetailsDto.getBankname());
            accountDetails.setUannumber(accountDetailsDto.getUannumber());
            accountDetails.setAadharnumber(accountDetailsDto.getAadharnumber());

            accountDetailsRepository.save(accountDetails);
        }
    }

    private void updateServiceHistory(List<ServiceHistoryDto> serviceHistoryDtos, Employee employee) {
        // Delete existing service history records
        List<ServiceHistory> existingRecords = serviceRepository.findByEmployeeIdOrderByDateDesc(employee.getId());
        serviceRepository.deleteAll(existingRecords);

        // Create new service history records
        for (ServiceHistoryDto dto : serviceHistoryDtos) {
            ServiceHistory serviceHistory = new ServiceHistory();
            serviceHistory.setDate(dto.getDate());
            serviceHistory.setType(dto.getType());
            serviceHistory.setStatus(dto.getStatus());
            serviceHistory.setAppointmenttype(dto.getAppointmenttype());
            serviceHistory.setModeofappointment(dto.getModeofappointment());
            serviceHistory.setDateofappointment(dto.getDateofappointment());
            serviceHistory.setProceedingorderdate(dto.getProceedingorderdate());
            serviceHistory.setProceedingorderno(dto.getProceedingorderno());
            serviceHistory.setJoiningdate(dto.getJoiningdate());
            serviceHistory.setPromoteddate(dto.getPromoteddate());
            serviceHistory.setFromdesignation(dto.getFromdesignation());
            serviceHistory.setTopromoted(dto.getTopromoted());
            serviceHistory.setFromdate(dto.getFromdate());
            serviceHistory.setTodate(dto.getTodate());

            // Handle both typeofincrement and incrementtype for backward compatibility
            String incrementType = dto.getTypeofincrement();
            if (incrementType == null && dto.getIncrementtype() != null) {
                incrementType = dto.getIncrementtype();
                System.out.println("UPDATE - Using incrementtype field: " + incrementType);
            }
            serviceHistory.setTypeofincrement(incrementType);

            serviceHistory.setFromplace(dto.getFromplace());
            serviceHistory.setToplace(dto.getToplace());
            serviceHistory.setDesignation(dto.getDesignation());
            serviceHistory.setOriginaldesignation(dto.getOriginaldesignation());
            serviceHistory.setParentdepartment(dto.getParentdepartment());

            // Set salary-related fields
            serviceHistory.setBasicPay(dto.getBasicPay());

            // Handle both da and daField for backward compatibility
            Double daValue = dto.getDa();
            if (daValue == null && dto.getDaField() != null) {
                daValue = dto.getDaField();
                System.out.println("UPDATE - Using daField: " + daValue);
            }
            serviceHistory.setDa(daValue);

            serviceHistory.setBasicPlusDA(dto.getBasicPlusDA());

            // Set other fields
            serviceHistory.setCaseDate(dto.getCaseDate());
            serviceHistory.setCaseNumber(dto.getCaseNumber());
            serviceHistory.setDescription(dto.getDescription());

            // Handle involved persons - use the array method to ensure proper conversion
            Long[] personsInvolved = dto.getPersonsInvolved();
            if (personsInvolved != null && personsInvolved.length > 0) {
                serviceHistory.setPersonsInvolved(personsInvolved);
                System.out.println("UPDATE - Set personsInvolved: " + java.util.Arrays.toString(personsInvolved) + " -> stored as: '" + serviceHistory.getInvolvedPersons() + "'");
            } else {
                serviceHistory.setInvolvedPersons(null);
                System.out.println("UPDATE - No personsInvolved provided, set to null");
            }

            serviceHistory.setPresentStatus(dto.getPresentStatus());
            serviceHistory.setWithholdingFromDate(dto.getWithholdingFromDate());
            serviceHistory.setWithholdingToDate(dto.getWithholdingToDate());
            serviceHistory.setHasCaseDetails(dto.getHasCaseDetails());
            serviceHistory.setPunishmenttype(dto.getPunishmenttype());
            serviceHistory.setCasedetails(dto.getCasedetails());
            serviceHistory.setPunishmentdate(dto.getPunishmentdate());
            serviceHistory.setEmployee(employee);

            serviceRepository.save(serviceHistory);
        }
    }

    private void updateLeaveBalances(List<LeaveDto> leaveDtos, Employee employee) {
        // Delete existing leave records
        List<Leave> existingRecords = leaveRepository.findByEmployeeId(employee.getId());
        leaveRepository.deleteAll(existingRecords);

        // Create new leave records
        for (LeaveDto dto : leaveDtos) {
            Leave leave = new Leave();
            leave.setLeaveType(dto.getLeaveType());
            leave.setOpeningBalance(dto.getOpeningBalance());
            leave.setClosingBalance(dto.getClosingBalance());
            leave.setEntryDate(dto.getEntryDate());
            leave.setEmployee(employee);

            leaveRepository.save(leave);
        }
    }

    private void updateEducationQualifications(List<EducationQualificationDto> educationDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            // Delete existing education records
            List<EducationQualification> existingRecords = educationQualificationRepository.findByEmployeeId(employeeId);
            educationQualificationRepository.deleteAll(existingRecords);

            // Create new education records
            for (EducationQualificationDto dto : educationDtos) {
                EducationQualification education = new EducationQualification();
                education.setQualification(dto.getQualification());
                education.setCoursename(dto.getCoursename());
                education.setSchoolname(dto.getSchoolname());
                education.setCollegename(dto.getCollegename());
                education.setUniversityname(dto.getUniversityname());
                education.setSpecialization(dto.getSpecialization());
                education.setEmployee(employee);

                educationQualificationRepository.save(education);
            }
        }
    }

    private void updatePunishmentDetails(List<PunishmentDetailsDto> punishmentDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            // Delete existing punishment records
            List<PunishmentDetails> existingRecords = punishmentDetailsRepository.findByEmployeeId(employeeId);
            punishmentDetailsRepository.deleteAll(existingRecords);

            // Create new punishment records
            for (PunishmentDetailsDto dto : punishmentDtos) {
                PunishmentDetails punishment = new PunishmentDetails();
                punishment.setPunishmenttype(dto.getPunishmenttype());
                punishment.setDate(dto.getDate());
                punishment.setCasedetails(dto.getCasedetails());
                punishment.setCaseDate(dto.getCaseDate());
                punishment.setCaseNumber(dto.getCaseNumber());
                punishment.setDescription(dto.getDescription());
                punishment.setPersonsInvolved(dto.getPersonsInvolved());
                punishment.setPresentStatus(dto.getPresentStatus());
                punishment.setWithHoldingFromDate(dto.getWithHoldingFromDate());
                punishment.setWithHoldingToDate(dto.getWithHoldingToDate());
                punishment.setEmployee(employee);

                punishmentDetailsRepository.save(punishment);
            }
        }
    }

    private void updateTrainingDetails(List<TrainingDetailsDto> trainingDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            // Delete existing training records
            List<TrainingDetails> existingRecords = trainingDetailsRepository.findByEmployeeId(employeeId);
            trainingDetailsRepository.deleteAll(existingRecords);

            // Create new training records
            for (TrainingDetailsDto dto : trainingDtos) {
                TrainingDetails training = new TrainingDetails();
                training.setTrainingtype(dto.getTrainingtype());
                training.setDate(dto.getDate());
                training.setEmployee(employee);

                trainingDetailsRepository.save(training);
            }
        }
    }

    private void updateNominees(List<NomineeDto> nomineeDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            System.out.println("=== DEBUG: updateNominees called for employee " + employeeId + " with " + nomineeDtos.size() + " nominees ===");

            // Get existing nominee records to preserve photos when not provided
            List<Nominee> existingRecords = nomineeRepository.findByEmployeeId(employeeId);
            System.out.println("UPDATE - Found " + existingRecords.size() + " existing nominee records");

            // Create a map of existing nominees by name for photo preservation
            Map<String, String> existingNomineePhotos = new HashMap<>();
            for (Nominee existing : existingRecords) {
                if (existing.getNomineename() != null && existing.getNomineePhoto() != null) {
                    existingNomineePhotos.put(existing.getNomineename().trim().toLowerCase(), existing.getNomineePhoto());
                    System.out.println("UPDATE - Preserved photo for existing nominee: " + existing.getNomineename() + " -> " + existing.getNomineePhoto());
                }
            }

            // Delete existing nominee records
            nomineeRepository.deleteAll(existingRecords);
            System.out.println("UPDATE - Deleted existing nominee records");

            // Create new nominee records
            for (int i = 0; i < nomineeDtos.size(); i++) {
                NomineeDto dto = nomineeDtos.get(i);
                System.out.println("UPDATE - Processing nominee " + (i+1) + ": " + dto.getNomineename());
                System.out.println("UPDATE - Nominee photo from DTO: " + dto.getNomineePhoto());

                Nominee nominee = new Nominee();
                nominee.setNomineename(dto.getNomineename());
                nominee.setAddress(dto.getAddress());
                nominee.setRelationship(dto.getRelationship());
                nominee.setAge(dto.getAge());
                nominee.setPercentageofshare(dto.getPercentageofshare());
                nominee.setGender(dto.getGender());

                // Set nominee photo - preserve existing photo if no new photo provided or placeholder detected
                String nomineePhotoFromDto = dto.getNomineePhoto();
                boolean hasValidPhotoInDto = nomineePhotoFromDto != null &&
                                           !nomineePhotoFromDto.trim().isEmpty() &&
                                           !isPlaceholderValue(nomineePhotoFromDto.trim());

                if (hasValidPhotoInDto) {
                    System.out.println("UPDATE - Processing nominee photo: " + nomineePhotoFromDto + " for employee: " + employeeId);
                    try {
                        String processedPhotoPath = processAndStoreNomineePhoto(nomineePhotoFromDto, employeeId);
                        System.out.println("UPDATE - Processed nominee photo result: " + processedPhotoPath);

                        if (processedPhotoPath != null) {
                            nominee.setNomineePhoto(processedPhotoPath);
                            System.out.println("UPDATE - Set nominee photo in entity: " + nominee.getNomineePhoto());
                        } else {
                            // Processing failed, try to preserve existing photo
                            String nomineeName = dto.getNomineename();
                            if (nomineeName != null) {
                                String existingPhoto = existingNomineePhotos.get(nomineeName.trim().toLowerCase());
                                if (existingPhoto != null) {
                                    System.out.println("UPDATE - Photo processing failed, preserving existing photo for " + dto.getNomineename() + ": " + existingPhoto);
                                    nominee.setNomineePhoto(existingPhoto);
                                } else {
                                    System.out.println("UPDATE - Photo processing failed and no existing photo found for " + dto.getNomineename() + ", setting to null");
                                    nominee.setNomineePhoto(null);
                                }
                            } else {
                                nominee.setNomineePhoto(null);
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("UPDATE - Error processing nominee photo for " + dto.getNomineename() + ": " + e.getMessage());
                        e.printStackTrace();

                        // Try to preserve existing photo on error
                        String nomineeName = dto.getNomineename();
                        if (nomineeName != null) {
                            String existingPhoto = existingNomineePhotos.get(nomineeName.trim().toLowerCase());
                            if (existingPhoto != null) {
                                System.out.println("UPDATE - Error occurred, preserving existing photo for " + dto.getNomineename() + ": " + existingPhoto);
                                nominee.setNomineePhoto(existingPhoto);
                            } else {
                                nominee.setNomineePhoto(null);
                            }
                        } else {
                            nominee.setNomineePhoto(null);
                        }
                    }
                } else {
                    // No valid photo in DTO (null, empty, or placeholder) - preserve existing photo
                    String nomineeName = dto.getNomineename();
                    if (nomineeName != null) {
                        String existingPhoto = existingNomineePhotos.get(nomineeName.trim().toLowerCase());
                        if (existingPhoto != null) {
                            if (nomineePhotoFromDto != null && isPlaceholderValue(nomineePhotoFromDto.trim())) {
                                System.out.println("UPDATE - Detected placeholder '" + nomineePhotoFromDto + "', preserving existing photo for " + dto.getNomineename() + ": " + existingPhoto);
                            } else {
                                System.out.println("UPDATE - No photo in DTO, preserving existing photo for " + dto.getNomineename() + ": " + existingPhoto);
                            }
                            nominee.setNomineePhoto(existingPhoto);
                        } else {
                            System.out.println("UPDATE - No existing photo found for " + dto.getNomineename() + ", setting to null");
                            nominee.setNomineePhoto(null);
                        }
                    } else {
                        System.out.println("UPDATE - No nominee name provided, setting photo to null");
                        nominee.setNomineePhoto(null);
                    }
                }

                nominee.setEmployee(employee);

                Nominee savedNominee = nomineeRepository.save(nominee);
                System.out.println("UPDATE - Saved nominee to database: " + savedNominee.getNomineename() + " with photo: " + savedNominee.getNomineePhoto());
            }
            System.out.println("=== DEBUG: updateNominees completed for employee " + employeeId + " ===");
        }
    }

    /**
     * Update nominees with photo files (similar to createNomineesWithPhotos)
     * This method handles MultipartFile[] for nominee photos during updates
     */
    private void updateNomineesWithPhotos(List<NomineeDto> nomineeDtos, Long employeeId, MultipartFile[] nomineePhotos) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            System.out.println("=== DEBUG: updateNomineesWithPhotos called for employee " + employeeId + " with " + nomineeDtos.size() + " nominees and " + (nomineePhotos != null ? nomineePhotos.length : 0) + " photos ===");

            // Delete existing nominee records
            List<Nominee> existingRecords = nomineeRepository.findByEmployeeId(employeeId);
            System.out.println("UPDATE_WITH_PHOTOS - Deleting " + existingRecords.size() + " existing nominee records");
            nomineeRepository.deleteAll(existingRecords);

            // Create new nominee records with photos
            for (int i = 0; i < nomineeDtos.size(); i++) {
                NomineeDto dto = nomineeDtos.get(i);
                System.out.println("UPDATE_WITH_PHOTOS - Processing nominee " + (i+1) + ": " + dto.getNomineename());

                Nominee nominee = new Nominee();
                nominee.setNomineename(dto.getNomineename());
                nominee.setAddress(dto.getAddress());
                nominee.setRelationship(dto.getRelationship());
                nominee.setAge(dto.getAge());
                nominee.setPercentageofshare(dto.getPercentageofshare());
                nominee.setGender(dto.getGender());

                // Upload nominee photo if provided
                if (nomineePhotos != null && i < nomineePhotos.length && nomineePhotos[i] != null && !nomineePhotos[i].isEmpty()) {
                    try {
                        System.out.println("UPDATE_WITH_PHOTOS - Uploading nominee photo for " + dto.getNomineename() + ": " + nomineePhotos[i].getOriginalFilename());

                        // Use the same logic as the working creation process - store file using FileStorageService
                        digitization.digitization.dto.FileStorageResult storageResult = fileStorageService.storeFile(nomineePhotos[i], employeeId);

                        // Also create document record for tracking
                        digitization.digitization.dto.DocumentDto documentDto = documentService.uploadDocument(nomineePhotos[i], employeeId);

                        // Store the relative path (just the filename) in the nominee table
                        nominee.setNomineePhoto(storageResult.getRelativePath());

                        System.out.println("UPDATE_WITH_PHOTOS - Nominee photo uploaded and stored: " + storageResult.getRelativePath());

                    } catch (Exception e) {
                        System.err.println("UPDATE_WITH_PHOTOS - Error uploading nominee photo for " + dto.getNomineename() + ": " + e.getMessage());
                        e.printStackTrace();
                        nominee.setNomineePhoto(null);
                    }
                } else {
                    // Check if nominee photo is provided in DTO (as URL/path)
                    if (dto.getNomineePhoto() != null && !dto.getNomineePhoto().trim().isEmpty()) {
                        System.out.println("UPDATE_WITH_PHOTOS - Processing nominee photo from DTO: " + dto.getNomineePhoto());
                        try {
                            String processedPhotoPath = processAndStoreNomineePhoto(dto.getNomineePhoto(), employeeId);
                            nominee.setNomineePhoto(processedPhotoPath);
                            System.out.println("UPDATE_WITH_PHOTOS - Processed nominee photo: " + processedPhotoPath);
                        } catch (Exception e) {
                            System.err.println("UPDATE_WITH_PHOTOS - Error processing nominee photo from DTO: " + e.getMessage());
                            nominee.setNomineePhoto(null);
                        }
                    } else {
                        System.out.println("UPDATE_WITH_PHOTOS - No nominee photo provided for " + dto.getNomineename());
                        nominee.setNomineePhoto(null);
                    }
                }

                nominee.setEmployee(employee);

                Nominee savedNominee = nomineeRepository.save(nominee);
                System.out.println("UPDATE_WITH_PHOTOS - Saved nominee to database: " + savedNominee.getNomineename() + " with photo: " + savedNominee.getNomineePhoto());
            }
            System.out.println("=== DEBUG: updateNomineesWithPhotos completed for employee " + employeeId + " ===");
        }
    }

    // Helper method to convert Employee to EmployeeListDto
    private EmployeeListDto convertToListDto(Employee employee) {
        EmployeeListDto dto = new EmployeeListDto();
        dto.setId(employee.getId());
        dto.setEcpfNumber(employee.getEcpfNumber());
        dto.setPanNumber(employee.getPanNumber());
        dto.setEmployeeName(employee.getEmployeeName());
        dto.setEmail(employee.getEmail());
        dto.setMobileNumber(employee.getMobileNumber());
        dto.setRegisterNumber(employee.getRegisterNumber());
        dto.setSection(employee.getSection());
        dto.setCurrentDesignation(employee.getCurrentDesignation());
        dto.setDateOfEntry(employee.getDateOfEntry());
        dto.setDistrict(employee.getDistrict());

        // Get employee status information - complete status table data
        try {
            Optional<EmployeeStatus> employeeStatusOpt = employeeStatusRepository.findByEmpId(employee.getId());
            if (employeeStatusOpt.isPresent()) {
                EmployeeStatus employeeStatus = employeeStatusOpt.get();
                // Set all status table fields
                dto.setStatus(employeeStatus.getStatus());
                dto.setApprovedBy(employeeStatus.getApprovedBy());
                dto.setRejectedBy(employeeStatus.getRejectedBy());
                dto.setRemarks(employeeStatus.getRemarks());
                dto.setCreatedBy(employeeStatus.getCreatedBy());
                dto.setCreatedAt(employeeStatus.getCreatedAt());
                dto.setUpdatedAt(employeeStatus.getUpdatedAt());
            } else {
                // Fallback to employee's createdBy if no status record exists
                dto.setCreatedBy(employee.getCreatedBy());
                dto.setStatus("unknown");
                dto.setApprovedBy(null);
                dto.setRejectedBy(null);
                dto.setRemarks(null);
                dto.setCreatedAt(null);
                dto.setUpdatedAt(null);
            }
        } catch (Exception e) {
            // Fallback to employee's createdBy if status lookup fails
            dto.setCreatedBy(employee.getCreatedBy());
            dto.setStatus("unknown");
            dto.setApprovedBy(null);
            dto.setRejectedBy(null);
            dto.setRemarks(null);
            dto.setCreatedAt(null);
            dto.setUpdatedAt(null);
        }

        return dto;
    }

    // Helper methods to get related data by employee ID
    private digitization.digitization.dto.ProfileDto getProfileByEmployeeId(Long employeeId) {
        try {
            // Use the employee relationship to find profile
            java.util.Optional<digitization.digitization.module.Profile> profileOpt = profileRepository.findByEmployeeId(employeeId);
            if (profileOpt.isPresent()) {
                digitization.digitization.module.Profile profile = profileOpt.get();
                digitization.digitization.dto.ProfileDto dto = new digitization.digitization.dto.ProfileDto();
                dto.setId(profile.getId());
                dto.setFatherName(profile.getFatherName());
                dto.setMotherName(profile.getMotherName());
                dto.setDateOfBirth(profile.getDateOfBirth());
                dto.setCommunity(profile.getCommunity());
                dto.setCaste(profile.getCaste());
                dto.setDistrict(profile.getDistrict());
                dto.setNativeplaceandtaluk(profile.getNativeplaceandtaluk());

                // Present address fields
                dto.setPresentDoorNo(profile.getPresentDoorNo());
                dto.setPresentBuildingName(profile.getPresentBuildingName());
                dto.setPresentStreetAddress(profile.getPresentStreetAddress());
                dto.setPresentCity(profile.getPresentCity());
                dto.setPresentPincode(profile.getPresentPincode());

                // Permanent address fields
                dto.setPermanentDoorNo(profile.getPermanentDoorNo());
                dto.setPermanentBuildingName(profile.getPermanentBuildingName());
                dto.setPermanentStreetAddress(profile.getPermanentStreetAddress());
                dto.setPermanentCity(profile.getPermanentCity());
                dto.setPermanentPincode(profile.getPermanentPincode());

                dto.setProfilephoto(profile.getProfilephoto());
                dto.setEmployeeType(profile.getEmployeeType());
                dto.setEmail(profile.getEmail());
                dto.setGender(profile.getGender());
                return dto;
            }
        } catch (Exception e) {
            // Log error but don't fail the whole request
        }
        return null;
    }

    private digitization.digitization.dto.AccountDetailsDto getAccountDetailsByEmployeeId(Long employeeId) {
        try {
            java.util.Optional<digitization.digitization.module.AccountDetails> accountDetailsOpt = accountDetailsRepository.findByEmployeeId(employeeId);
            if (accountDetailsOpt.isPresent()) {
                digitization.digitization.module.AccountDetails accountDetails = accountDetailsOpt.get();
                digitization.digitization.dto.AccountDetailsDto dto = new digitization.digitization.dto.AccountDetailsDto();
                dto.setId(accountDetails.getId());
                dto.setBankaccountnumber(accountDetails.getBankaccountnumber());
                dto.setIfsccode(accountDetails.getIfsccode());
                dto.setBankname(accountDetails.getBankname());
                dto.setUannumber(accountDetails.getUannumber());
                dto.setAadharnumber(accountDetails.getAadharnumber());
                return dto;
            }
        } catch (Exception e) {
            // Log error but don't fail the whole request
        }
        return null;
    }

    private java.util.List<digitization.digitization.dto.ServiceHistoryDto> getServiceHistoryByEmployeeId(Long employeeId) {
        try {
            java.util.List<digitization.digitization.module.ServiceHistory> serviceHistoryList = serviceRepository.findByEmployeeIdOrderByDateDesc(employeeId);
            return serviceHistoryList.stream()
                .map(this::convertServiceHistoryToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            // Log error but don't fail the whole request
            return new java.util.ArrayList<>();
        }
    }

    private java.util.List<digitization.digitization.dto.LeaveDto> getLeaveBalancesByEmployeeId(Long employeeId) {
        try {
            // LeaveRepository has findByEmployeeId method
            java.util.List<digitization.digitization.module.Leave> employeeLeaves = leaveRepository.findByEmployeeId(employeeId);

            return employeeLeaves.stream()
                .map(this::convertLeaveToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            return new java.util.ArrayList<>();
        }
    }

    private java.util.List<digitization.digitization.dto.EducationQualificationDto> getEducationQualificationsByEmployeeId(Long employeeId) {
        try {
            java.util.List<digitization.digitization.module.EducationQualification> educationList = educationQualificationRepository.findByEmployeeId(employeeId);
            return educationList.stream()
                .map(this::convertEducationToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            return new java.util.ArrayList<>();
        }
    }

    private java.util.List<digitization.digitization.dto.PunishmentDetailsDto> getPunishmentDetailsByEmployeeId(Long employeeId) {
        try {
            java.util.List<digitization.digitization.module.PunishmentDetails> punishmentList = punishmentDetailsRepository.findByEmployeeId(employeeId);
            return punishmentList.stream()
                .map(this::convertPunishmentToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            return new java.util.ArrayList<>();
        }
    }

    private java.util.List<digitization.digitization.dto.TrainingDetailsDto> getTrainingDetailsByEmployeeId(Long employeeId) {
        try {
            java.util.List<digitization.digitization.module.TrainingDetails> trainingList = trainingDetailsRepository.findByEmployeeId(employeeId);
            return trainingList.stream()
                .map(this::convertTrainingToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            return new java.util.ArrayList<>();
        }
    }

    private java.util.List<digitization.digitization.dto.NomineeDto> getNomineesByEmployeeId(Long employeeId) {
        try {
            java.util.List<digitization.digitization.module.Nominee> nomineeList = nomineeRepository.findByEmployeeId(employeeId);
            return nomineeList.stream()
                .map(this::convertNomineeToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            return new java.util.ArrayList<>();
        }
    }

    // Converter methods
    private digitization.digitization.dto.ServiceHistoryDto convertServiceHistoryToDto(digitization.digitization.module.ServiceHistory serviceHistory) {
        digitization.digitization.dto.ServiceHistoryDto dto = new digitization.digitization.dto.ServiceHistoryDto();
        dto.setId(serviceHistory.getId());
        dto.setDate(serviceHistory.getDate());
        dto.setType(serviceHistory.getType());
        dto.setStatus(serviceHistory.getStatus());
        dto.setAppointmenttype(serviceHistory.getAppointmenttype());
        dto.setModeofappointment(serviceHistory.getModeofappointment());
        dto.setDateofappointment(serviceHistory.getDateofappointment());
        dto.setProceedingorderdate(serviceHistory.getProceedingorderdate());
        dto.setProceedingorderno(serviceHistory.getProceedingorderno());
        dto.setJoiningdate(serviceHistory.getJoiningdate());
        dto.setPromoteddate(serviceHistory.getPromoteddate());
        dto.setFromdesignation(serviceHistory.getFromdesignation());
        dto.setTopromoted(serviceHistory.getTopromoted());
        dto.setFromdate(serviceHistory.getFromdate());
        dto.setTodate(serviceHistory.getTodate());
        dto.setTypeofincrement(serviceHistory.getTypeofincrement());
        dto.setFromplace(serviceHistory.getFromplace());
        dto.setToplace(serviceHistory.getToplace());
        dto.setDesignation(serviceHistory.getDesignation());
        dto.setOriginaldesignation(serviceHistory.getOriginaldesignation());
        dto.setParentdepartment(serviceHistory.getParentdepartment());
        dto.setPunishmenttype(serviceHistory.getPunishmenttype());
        dto.setCasedetails(serviceHistory.getCasedetails());
        dto.setPunishmentdate(serviceHistory.getPunishmentdate());
        dto.setWithholdingFromDate(serviceHistory.getWithholdingFromDate());
        dto.setWithholdingToDate(serviceHistory.getWithholdingToDate());
        dto.setHasCaseDetails(serviceHistory.getHasCaseDetails());

        // Set additional fields that might be missing
        dto.setCaseDate(serviceHistory.getCaseDate());
        dto.setCaseNumber(serviceHistory.getCaseNumber());
        dto.setDescription(serviceHistory.getDescription());
        dto.setInvolvedPersons(serviceHistory.getInvolvedPersons());
        dto.setPresentStatus(serviceHistory.getPresentStatus());
        dto.setBasicPay(serviceHistory.getBasicPay());
        dto.setDa(serviceHistory.getDa());
        dto.setBasicPlusDA(serviceHistory.getBasicPlusDA());

        // Populate involved persons with names
        try {
            Long[] personsInvolved = serviceHistory.getPersonsInvolved();
            if (personsInvolved != null && personsInvolved.length > 0) {
                List<Long> personIds = Arrays.asList(personsInvolved);
                List<PersonDto> personsWithNames = getEmployeeNamesByIds(personIds);
                dto.setInvolvedPersonsWithNames(personsWithNames);
            } else {
                dto.setInvolvedPersonsWithNames(new ArrayList<>());
            }
        } catch (Exception e) {
            System.out.println("Error populating involved persons with names: " + e.getMessage());
            dto.setInvolvedPersonsWithNames(new ArrayList<>());
        }

        return dto;
    }

    private digitization.digitization.dto.LeaveDto convertLeaveToDto(digitization.digitization.module.Leave leave) {
        digitization.digitization.dto.LeaveDto dto = new digitization.digitization.dto.LeaveDto();
        dto.setId(leave.getId());
        dto.setLeaveType(leave.getLeaveType());
        dto.setOpeningBalance(leave.getOpeningBalance());
        dto.setClosingBalance(leave.getClosingBalance());
        dto.setEntryDate(leave.getEntryDate());
        return dto;
    }

    private digitization.digitization.dto.EducationQualificationDto convertEducationToDto(digitization.digitization.module.EducationQualification education) {
        digitization.digitization.dto.EducationQualificationDto dto = new digitization.digitization.dto.EducationQualificationDto();
        dto.setId(education.getId());
        dto.setQualification(education.getQualification());
        dto.setCoursename(education.getCoursename());
        dto.setSchoolname(education.getSchoolname());
        dto.setCollegename(education.getCollegename());
        dto.setUniversityname(education.getUniversityname());
        dto.setSpecialization(education.getSpecialization());
        return dto;
    }

    private digitization.digitization.dto.PunishmentDetailsDto convertPunishmentToDto(digitization.digitization.module.PunishmentDetails punishment) {
        digitization.digitization.dto.PunishmentDetailsDto dto = new digitization.digitization.dto.PunishmentDetailsDto();
        dto.setId(punishment.getId());
        dto.setPunishmenttype(punishment.getPunishmenttype());
        dto.setDate(punishment.getDate());
        dto.setCasedetails(punishment.getCasedetails());
        dto.setCaseDate(punishment.getCaseDate());
        dto.setCaseNumber(punishment.getCaseNumber());
        dto.setDescription(punishment.getDescription());
        dto.setPersonsInvolved(punishment.getPersonsInvolved());
        dto.setPresentStatus(punishment.getPresentStatus());
        dto.setWithHoldingFromDate(punishment.getWithHoldingFromDate());
        dto.setWithHoldingToDate(punishment.getWithHoldingToDate());
        return dto;
    }

    private digitization.digitization.dto.TrainingDetailsDto convertTrainingToDto(digitization.digitization.module.TrainingDetails training) {
        digitization.digitization.dto.TrainingDetailsDto dto = new digitization.digitization.dto.TrainingDetailsDto();
        dto.setId(training.getId());
        dto.setTrainingtype(training.getTrainingtype());
        dto.setDate(training.getDate());
        return dto;
    }

    private digitization.digitization.dto.NomineeDto convertNomineeToDto(digitization.digitization.module.Nominee nominee) {
        digitization.digitization.dto.NomineeDto dto = new digitization.digitization.dto.NomineeDto();
        dto.setId(nominee.getId());
        dto.setNomineename(nominee.getNomineename());
        dto.setAddress(nominee.getAddress());
        dto.setRelationship(nominee.getRelationship());
        dto.setAge(nominee.getAge());
        dto.setPercentageofshare(nominee.getPercentageofshare());
        dto.setGender(nominee.getGender());

        // Convert to full URL for frontend usage (same logic as profile photos)
        if (nominee.getNomineePhoto() != null && !nominee.getNomineePhoto().trim().isEmpty()) {
            String nomineePhoto = nominee.getNomineePhoto().trim();
            // Check if it already starts with /api/files/
            if (nomineePhoto.startsWith("/api/files/")) {
                // Already a complete URL, use as is
                dto.setNomineePhoto(nomineePhoto);
            } else {
                // It's a relative path or filename, add /api/files/ prefix
                dto.setNomineePhoto("/api/files/" + nomineePhoto);
            }
        } else {
            dto.setNomineePhoto(null);
        }

        return dto;
    }

    /**
     * Process and store nominee photo to ensure it follows the same pattern as profile photos
     * This method converts old format URLs to employee-specific directory format
     */
    private String processAndStoreNomineePhoto(String nomineePhotoUrl, Long employeeId) {
        System.out.println("processAndStoreNomineePhoto called with URL: " + nomineePhotoUrl + ", employeeId: " + employeeId);

        if (nomineePhotoUrl == null || nomineePhotoUrl.trim().isEmpty()) {
            System.out.println("URL is null or empty, returning null");
            return null;
        }

        String cleanUrl = nomineePhotoUrl.trim();
        System.out.println("Clean URL: " + cleanUrl);

        // Handle special placeholder values that indicate no actual file
        if (isPlaceholderValue(cleanUrl)) {
            System.out.println("Detected placeholder value '" + cleanUrl + "', returning null");
            return null;
        }

        try {
            // First check if this URL corresponds to an existing document for this employee
            java.util.List<digitization.digitization.module.Document> documents = documentRepository.findByEmployee_Id(employeeId);
            System.out.println("Found " + documents.size() + " documents for employee " + employeeId);

            for (digitization.digitization.module.Document document : documents) {
                if (document.getFileUrl() != null && document.getFileUrl().equals(cleanUrl)) {
                    // Found matching document - return the document's fileUrl
                    System.out.println("Found matching document with fileUrl: " + document.getFileUrl());
                    return document.getFileUrl();
                }
            }

            // Check if it's already in the correct employee-specific format
            if (cleanUrl.startsWith("/api/files/employee_" + employeeId + "/")) {
                // Verify the file actually exists before returning the URL
                String filename = cleanUrl.substring(("/api/files/employee_" + employeeId + "/").length());
                if (fileExistsInEmployeeDirectory(filename, employeeId)) {
                    System.out.println("URL already in correct format and file exists");
                    return cleanUrl;
                } else {
                    System.out.println("URL in correct format but file doesn't exist: " + cleanUrl);
                    return null;
                }
            }

            // If it's a full URL with /api/files/, extract filename and convert
            if (cleanUrl.startsWith("/api/files/")) {
                String filename = cleanUrl.substring("/api/files/".length());
                System.out.println("Extracted filename from full URL: " + filename);
                if (!filename.startsWith("employee_")) {
                    // This is old format like "/api/files/echarts.png"
                    // Check if the file actually exists before converting
                    if (fileExistsInUploadsDirectory(filename)) {
                        String convertedUrl = convertToEmployeeSpecificFormat(filename, employeeId);
                        System.out.println("Converting nominee photo from old format '" + filename + "' to new format: " + convertedUrl);
                        return convertedUrl;
                    } else {
                        System.out.println("Old format file doesn't exist: " + filename);
                        return null;
                    }
                }
            } else {
                // It's just a filename without /api/files/ prefix
                System.out.println("Processing filename without /api/files/ prefix: '" + cleanUrl + "'");

                // Check if this file already exists in the employee directory
                if (fileExistsInEmployeeDirectory(cleanUrl, employeeId)) {
                    boolean isProperFormat = isProperEmployeeFormatFile(cleanUrl);
                    System.out.println("File exists, is proper format: " + isProperFormat);

                    if (isProperFormat) {
                        // File is already in proper format and exists
                        String properUrl = "/api/files/employee_" + employeeId + "/" + cleanUrl;
                        System.out.println("File already in proper format and exists, using: " + properUrl);
                        return properUrl;
                    } else {
                        // File exists but not in proper format - use as is for backward compatibility
                        String properUrl = "/api/files/employee_" + employeeId + "/" + cleanUrl;
                        System.out.println("File exists but not in proper format, using as is: " + properUrl);
                        return properUrl;
                    }
                } else {
                    System.out.println("File doesn't exist in employee directory: " + cleanUrl);
                    return null;
                }
            }

            System.out.println("No conversion needed, returning original URL: " + cleanUrl);
            return cleanUrl;

        } catch (Exception e) {
            System.out.println("Error processing nominee photo URL: " + e.getMessage());
            e.printStackTrace();
            return null; // Return null instead of the potentially invalid URL
        }
    }

    /**
     * Check if a value is a placeholder that indicates no actual photo
     */
    private boolean isPlaceholderValue(String value) {
        if (value == null) return true;
        String cleanValue = value.trim().toLowerCase();
        return cleanValue.equals("pending_upload") ||
               cleanValue.equals("placeholder") ||
               cleanValue.equals("null") ||
               cleanValue.equals("undefined") ||
               cleanValue.equals("") ||
               cleanValue.equals("no_photo");
    }

    /**
     * Check if a file exists in the employee's directory
     */
    private boolean fileExistsInEmployeeDirectory(String filename, Long employeeId) {
        try {
            String[] possiblePaths = {
                "uploads/employee_" + employeeId + "/" + filename,
                "./uploads/employee_" + employeeId + "/" + filename,
                "uploads\\employee_" + employeeId + "\\" + filename,  // Windows path
                System.getProperty("user.dir") + "/uploads/employee_" + employeeId + "/" + filename
            };

            for (String path : possiblePaths) {
                java.io.File file = new java.io.File(path);
                if (file.exists() && file.isFile()) {
                    System.out.println("File exists at: " + path);
                    return true;
                }
            }

            System.out.println("File not found in any employee directory path: " + filename);
            return false;
        } catch (Exception e) {
            System.out.println("Error checking file existence: " + e.getMessage());
            return false;
        }
    }

    /**
     * Check if a file exists in the general uploads directory
     */
    private boolean fileExistsInUploadsDirectory(String filename) {
        try {
            String[] possiblePaths = {
                "uploads/" + filename,
                "./uploads/" + filename,
                "uploads\\" + filename,  // Windows path
                System.getProperty("user.dir") + "/uploads/" + filename
            };

            for (String path : possiblePaths) {
                java.io.File file = new java.io.File(path);
                if (file.exists() && file.isFile()) {
                    System.out.println("File exists at: " + path);
                    return true;
                }
            }

            System.out.println("File not found in any uploads directory path: " + filename);
            return false;
        } catch (Exception e) {
            System.out.println("Error checking file existence: " + e.getMessage());
            return false;
        }
    }

    /**
     * Convert filename to proper employee format (same as profile photos)
     * This method should only generate the URL format - the actual file should be uploaded separately
     */
    private String convertToProperEmployeeFormat(String originalFilename, Long employeeId) {
        try {
            System.out.println("convertToProperEmployeeFormat called with: " + originalFilename + ", employeeId: " + employeeId);

            // Check if the file already exists in the employee directory with proper format
            // This happens when the file was uploaded via the proper upload endpoint
            String employeeDir = "uploads/employee_" + employeeId + "/";
            java.io.File directory = new java.io.File(employeeDir);

            System.out.println("Checking directory: " + employeeDir);
            System.out.println("Directory absolute path: " + directory.getAbsolutePath());
            System.out.println("Directory exists: " + directory.exists());
            System.out.println("Is directory: " + directory.isDirectory());

            // Also check alternative paths
            String[] possiblePaths = {
                "uploads/employee_" + employeeId + "/",
                "./uploads/employee_" + employeeId + "/",
                "uploads\\employee_" + employeeId + "\\",  // Windows path
                System.getProperty("user.dir") + "/uploads/employee_" + employeeId + "/"
            };

            java.io.File foundDirectory = null;
            for (String path : possiblePaths) {
                java.io.File testDir = new java.io.File(path);
                System.out.println("Testing path: " + path + " -> exists: " + testDir.exists() + ", isDir: " + testDir.isDirectory());
                if (testDir.exists() && testDir.isDirectory()) {
                    foundDirectory = testDir;
                    employeeDir = path;
                    System.out.println("Found directory at: " + path);
                    break;
                }
            }

            if (foundDirectory != null && foundDirectory.exists() && foundDirectory.isDirectory()) {
                java.io.File[] files = foundDirectory.listFiles();
                System.out.println("Found " + (files != null ? files.length : 0) + " files in directory");

                if (files != null) {
                    // Find the most recent file with matching extension and proper format
                    java.io.File mostRecentFile = null;
                    long mostRecentTime = 0;

                    String originalExt = "";
                    int originalDotIndex = originalFilename.lastIndexOf('.');
                    if (originalDotIndex > 0) {
                        originalExt = originalFilename.substring(originalDotIndex).toLowerCase();
                    }
                    System.out.println("Looking for files with extension: " + originalExt);

                    for (java.io.File file : files) {
                        String fileName = file.getName();
                        System.out.println("Checking file: " + fileName);

                        // Check if this file has the same extension as the original
                        String fileExt = "";
                        int fileDotIndex = fileName.lastIndexOf('.');
                        if (fileDotIndex > 0) {
                            fileExt = fileName.substring(fileDotIndex).toLowerCase();
                        }

                        // If extensions match and file is in proper format
                        if (originalExt.equals(fileExt) && isProperEmployeeFormatFile(fileName)) {
                            System.out.println("File " + fileName + " matches extension and format");
                            long fileTime = file.lastModified();
                            if (fileTime > mostRecentTime) {
                                mostRecentFile = file;
                                mostRecentTime = fileTime;
                                System.out.println("New most recent file: " + fileName);
                            }
                        }
                    }

                    if (mostRecentFile != null) {
                        String properUrl = "/api/files/employee_" + employeeId + "/" + mostRecentFile.getName();
                        System.out.println("Found existing proper format file: " + properUrl);
                        return properUrl;
                    } else {
                        System.out.println("No matching proper format file found");
                    }
                } else {
                    System.out.println("Directory is empty or cannot list files");
                }
            } else {
                System.out.println("Directory does not exist: " + employeeDir);
            }

            // If no existing proper format file found, don't generate virtual URLs
            // This prevents "File not found" errors when the frontend tries to access non-existent files
            System.out.println("No existing proper format file found for: " + originalFilename);
            System.out.println("Not generating virtual URL to prevent file not found errors");
            return null;

        } catch (Exception e) {
            System.out.println("Error converting to proper employee format: " + e.getMessage());
            e.printStackTrace();
            // Fallback: use original filename in employee directory
            String fallbackUrl = "/api/files/employee_" + employeeId + "/" + originalFilename;
            System.out.println("Using fallback URL: " + fallbackUrl);
            return fallbackUrl;
        }
    }

    /**
     * Check if filename is already in proper employee format (timestamp_uuid.extension)
     */
    private boolean isProperEmployeeFormatFile(String filename) {
        try {
            System.out.println("Checking format for filename: '" + filename + "'");

            // Proper format: 20250621_225948_14959308.png
            // Pattern: YYYYMMDD_HHMMSS_xxxxxxxx.extension

            // Remove extension
            String nameWithoutExt = filename;
            int lastDotIndex = filename.lastIndexOf('.');
            if (lastDotIndex > 0) {
                nameWithoutExt = filename.substring(0, lastDotIndex);
            }

            System.out.println("Name without extension: '" + nameWithoutExt + "'");

            // Split by underscore
            String[] parts = nameWithoutExt.split("_");
            System.out.println("Split parts: " + java.util.Arrays.toString(parts));
            System.out.println("Number of parts: " + parts.length);

            // Should have exactly 3 parts: date, time, uuid
            if (parts.length == 3) {
                String datePart = parts[0];
                String timePart = parts[1];
                String uuidPart = parts[2];

                System.out.println("Date part: '" + datePart + "' (length: " + datePart.length() + ")");
                System.out.println("Time part: '" + timePart + "' (length: " + timePart.length() + ")");
                System.out.println("UUID part: '" + uuidPart + "' (length: " + uuidPart.length() + ")");

                // Check date part (8 digits: YYYYMMDD)
                boolean dateValid = datePart.length() == 8 && datePart.matches("\\d{8}");
                System.out.println("Date valid: " + dateValid);

                if (dateValid) {
                    // Check time part (6 digits: HHMMSS)
                    boolean timeValid = timePart.length() == 6 && timePart.matches("\\d{6}");
                    System.out.println("Time valid: " + timeValid);

                    if (timeValid) {
                        // Check UUID part (8 characters: alphanumeric)
                        boolean uuidValid = uuidPart.length() == 8 && uuidPart.matches("[a-fA-F0-9]{8}");
                        System.out.println("UUID valid: " + uuidValid);

                        if (uuidValid) {
                            System.out.println("File '" + filename + "' is in proper employee format");
                            return true;
                        }
                    }
                }
            }

            System.out.println("File '" + filename + "' is NOT in proper employee format");
            return false;

        } catch (Exception e) {
            System.out.println("Error checking file format for '" + filename + "': " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Create a virtual employee-specific URL that maps to the original file
     * This creates consistent URL format without moving files
     */
    private String createVirtualEmployeeUrl(String originalFilename, Long employeeId) {
        try {
            System.out.println("createVirtualEmployeeUrl called with: " + originalFilename + ", employeeId: " + employeeId);

            // Store the mapping between virtual URL and actual filename
            // We'll create a virtual URL that looks like employee-specific format
            // but internally maps to the original file

            // Generate a consistent hash-based identifier from the original filename
            // This ensures the same filename always gets the same virtual URL
            String hashId = generateConsistentId(originalFilename);

            // Extract file extension
            String fileExtension = "";
            int lastDotIndex = originalFilename.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < originalFilename.length() - 1) {
                fileExtension = originalFilename.substring(lastDotIndex);
            }

            // Create virtual filename that looks like the profile photo format
            String virtualFilename = "nominee_" + hashId + fileExtension;

            // Create the virtual URL
            String virtualUrl = "/api/files/employee_" + employeeId + "/" + virtualFilename;

            System.out.println("Created virtual URL: " + virtualUrl + " -> maps to: " + originalFilename);

            // Store the mapping for the file controller to use
            storeFileMapping(virtualUrl, originalFilename);

            return virtualUrl;

        } catch (Exception e) {
            System.out.println("Error creating virtual URL: " + e.getMessage());
            // Fallback: return original filename with /api/files/ prefix
            return "/api/files/" + originalFilename;
        }
    }

    /**
     * Generate a consistent ID from filename for virtual URLs
     */
    private String generateConsistentId(String filename) {
        try {
            // Use hashCode to generate consistent ID from filename
            int hash = filename.hashCode();
            // Convert to positive hex string
            String hashStr = Integer.toHexString(Math.abs(hash));
            // Ensure it's 8 characters like UUID format
            return String.format("%8s", hashStr).replace(' ', '0').substring(0, 8);
        } catch (Exception e) {
            // Fallback to simple timestamp
            return String.valueOf(System.currentTimeMillis()).substring(8);
        }
    }

    /**
     * Store mapping between virtual URL and actual filename
     * This can be enhanced with database storage or cache if needed
     */
    private void storeFileMapping(String virtualUrl, String actualFilename) {
        // For now, we'll log the mapping
        // In production, you might want to store this in database or cache
        System.out.println("FILE_MAPPING: " + virtualUrl + " -> " + actualFilename);

        // You could implement database storage here:
        // fileMappingRepository.save(new FileMapping(virtualUrl, actualFilename));
    }

    /**
     * Convert old format filename to employee-specific directory format (URL only, no file operations)
     */
    private String convertToEmployeeSpecificFormat(String originalFilename, Long employeeId) {
        try {
            System.out.println("convertToEmployeeSpecificFormat called with: " + originalFilename + ", employeeId: " + employeeId);

            // Generate timestamp and unique ID like profile photos
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            String timestamp = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String uniqueId = java.util.UUID.randomUUID().toString().substring(0, 8);

            System.out.println("Generated timestamp: " + timestamp + ", uniqueId: " + uniqueId);

            // Extract file extension from original filename
            String fileExtension = "";
            int lastDotIndex = originalFilename.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < originalFilename.length() - 1) {
                fileExtension = originalFilename.substring(lastDotIndex);
            }

            System.out.println("Extracted file extension: " + fileExtension);

            // Create new filename in the same format as profile photos
            String newFilename = timestamp + "_" + uniqueId + fileExtension;
            System.out.println("Generated new filename: " + newFilename);

            // Return the new URL in employee-specific directory format
            String finalUrl = "/api/files/employee_" + employeeId + "/" + newFilename;
            System.out.println("Final converted URL: " + finalUrl);
            return finalUrl;

        } catch (Exception e) {
            System.out.println("Error converting filename format: " + e.getMessage());
            e.printStackTrace();
            // Fallback: just put the original filename in employee directory
            String fallbackUrl = "/api/files/employee_" + employeeId + "/" + originalFilename;
            System.out.println("Using fallback URL: " + fallbackUrl);
            return fallbackUrl;
        }
    }

    /**
     * Legacy method - kept for backward compatibility
     * Process nominee photo URL to ensure it follows the same pattern as profile photos
     * This ensures consistency between profile photos and nominee photos (employee-specific directories)
     */
    private String processNomineePhotoUrl(String nomineePhotoUrl, Long employeeId) {
        if (nomineePhotoUrl == null || nomineePhotoUrl.trim().isEmpty()) {
            return null;
        }

        // Remove leading/trailing whitespace
        String cleanUrl = nomineePhotoUrl.trim();

        try {
            // Check if this URL corresponds to an existing document for this employee
            java.util.List<digitization.digitization.module.Document> documents = documentRepository.findByEmployee_Id(employeeId);

            for (digitization.digitization.module.Document document : documents) {
                if (document.getFileUrl() != null && document.getFileUrl().equals(cleanUrl)) {
                    // Found matching document - return the document's fileUrl
                    // This ensures nominee table stores the same fileUrl as document table
                    return document.getFileUrl();
                }
            }

            // If no matching document found, process the URL format
            if (cleanUrl.startsWith("/api/files/")) {
                // Extract the path after /api/files/
                String pathAfterApiFiles = cleanUrl.substring("/api/files/".length());

                // Check if it's already in the correct employee-specific format
                if (pathAfterApiFiles.startsWith("employee_" + employeeId + "/")) {
                    // Already in correct format, return as is
                    return cleanUrl;
                } else if (pathAfterApiFiles.contains("employee_")) {
                    // It's in employee format but for different employee, keep as is
                    return cleanUrl;
                } else {
                    // Old format - just a filename like "sample2.png"
                    // For backward compatibility, keep as is but log warning
                    System.out.println("Warning: Nominee photo '" + pathAfterApiFiles + "' for employee " + employeeId +
                                     " is not in employee-specific directory format. Consider re-uploading via /nominee/uploadNomineePhoto endpoint.");
                    return cleanUrl;
                }
            } else {
                // It's a relative path or filename without /api/files/ prefix
                // Check if it's in the correct format
                if (cleanUrl.startsWith("employee_" + employeeId + "/")) {
                    // Add the /api/files/ prefix to make it a complete URL
                    return "/api/files/" + cleanUrl;
                } else if (cleanUrl.contains("employee_")) {
                    // Different employee format, add prefix and return
                    return "/api/files/" + cleanUrl;
                } else {
                    // Just a filename, add prefix for backward compatibility
                    return "/api/files/" + cleanUrl;
                }
            }

        } catch (Exception e) {
            // If there's any error accessing documents, fall back to storing the URL as is
            System.out.println("Warning: Could not process nominee photo URL: " + e.getMessage());
            return cleanUrl;
        }
    }

}
