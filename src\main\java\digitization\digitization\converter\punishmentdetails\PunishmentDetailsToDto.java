package digitization.digitization.converter.punishmentdetails;

import digitization.digitization.dto.PunishmentDetailsDto;
import digitization.digitization.module.PunishmentDetails;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class PunishmentDetailsToDto implements Converter<PunishmentDetails, PunishmentDetailsDto> {
    @Override
    public PunishmentDetailsDto convert(PunishmentDetails punishmentDetails) {
        PunishmentDetailsDto punishmentDetailsDto = new PunishmentDetailsDto();
        punishmentDetailsDto.setId(punishmentDetails.getId());
        punishmentDetailsDto.setPunishmenttype(punishmentDetails.getPunishmenttype());
        punishmentDetailsDto.setDate(punishmentDetails.getDate());
        punishmentDetailsDto.setCasedetails(punishmentDetails.getCasedetails());
        punishmentDetailsDto.setCaseDate(punishmentDetails.getCaseDate());
        punishmentDetailsDto.setCaseNumber(punishmentDetails.getCaseNumber());
        punishmentDetailsDto.setDescription(punishmentDetails.getDescription());
        punishmentDetailsDto.setPersonsInvolved(punishmentDetails.getPersonsInvolved());
        punishmentDetailsDto.setPresentStatus(punishmentDetails.getPresentStatus());
        punishmentDetailsDto.setWithHoldingFromDate(punishmentDetails.getWithHoldingFromDate());
        punishmentDetailsDto.setWithHoldingToDate(punishmentDetails.getWithHoldingToDate());
        return punishmentDetailsDto;
    }
}
