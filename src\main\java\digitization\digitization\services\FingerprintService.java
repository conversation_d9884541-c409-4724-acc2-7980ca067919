package digitization.digitization.services;

import org.springframework.stereotype.Service;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.Base64;

@Service
public class FingerprintService {

    public byte[] extractAndDecodeFingerprint(String xmlResponse) {
        try {
            // Parse the XML response
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlResponse)));

            // Extract the fingerprint data
            NodeList nodeList = document.getElementsByTagName("Data");
            if (nodeList.getLength() > 0) {
                Element dataElement = (Element) nodeList.item(0);
                String fingerprintData = dataElement.getTextContent();

                // Decode the base64 data
                return Base64.getDecoder().decode(fingerprintData);
            } else {
                throw new RuntimeException("No fingerprint data found in the XML response.");
            }
        } catch (Exception e) {
            throw new RuntimeException("Error extracting and decoding fingerprint data: " + e.getMessage(), e);
        }
    }
}
