package digitization.digitization.converter.service;

import digitization.digitization.dto.ServiceHistoryDto;
import digitization.digitization.module.ServiceHistory;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class DtotoService implements Converter<ServiceHistoryDto, ServiceHistory> {
    @Override
    public ServiceHistory convert(ServiceHistoryDto serv) {
        ServiceHistory serviceHistory=new ServiceHistory();
        serviceHistory.setId(serv.getId());
        serviceHistory.setDate(serv.getDate());
        serviceHistory.setType(serv.getType());
        serviceHistory.setStatus(serv.getStatus());
        serviceHistory.setAppointmenttype(serv.getAppointmenttype());
        serviceHistory.setModeofappointment(serv.getModeofappointment());
        serviceHistory.setDateofappointment(serv.getDateofappointment());
        serviceHistory.setProceedingorderdate(serv.getProceedingorderdate());
        serviceHistory.setJoiningdate(serv.getJoiningdate());
        serviceHistory.setPromoteddate(serv.getPromoteddate());
        serviceHistory.setFromdesignation(serv.getFromdesignation());
        serviceHistory.setTopromoted(serv.getTopromoted());
        serviceHistory.setFromdate(serv.getFromdate());
        serviceHistory.setTodate(serv.getTodate());
        serviceHistory.setTypeofincrement(serv.getTypeofincrement());
        serviceHistory.setFromplace(serv.getFromplace());
        serviceHistory.setToplace(serv.getToplace());
        serviceHistory.setDesignation(serv.getDesignation());
        serviceHistory.setOriginaldesignation(serv.getOriginaldesignation());
        serviceHistory.setParentdepartment(serv.getParentdepartment());
        serviceHistory.setPunishmenttype(serv.getPunishmenttype());
        serviceHistory.setCasedetails(serv.getCasedetails());
        serviceHistory.setCaseDate(serv.getCaseDate());
        serviceHistory.setCaseNumber(serv.getCaseNumber());
        serviceHistory.setDescription(serv.getDescription());
        serviceHistory.setInvolvedPersons(serv.getInvolvedPersons());
        serviceHistory.setPresentStatus(serv.getPresentStatus());
        serviceHistory.setPunishmentdate(serv.getPunishmentdate());
        serviceHistory.setWithholdingFromDate(serv.getWithholdingFromDate());
        serviceHistory.setWithholdingToDate(serv.getWithholdingToDate());
        serviceHistory.setHasCaseDetails(serv.getHasCaseDetails());
        serviceHistory.setBasicPay(serv.getBasicPay());
        serviceHistory.setDa(serv.getDa());
        serviceHistory.setBasicPlusDA(serv.getBasicPlusDA());
        serviceHistory.setProceedingorderno(serv.getProceedingorderno());
        return serviceHistory;
    }
}
