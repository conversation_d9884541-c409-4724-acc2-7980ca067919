package digitization.digitization.dto;

import digitization.digitization.module.Document;
import digitization.digitization.module.Leave;
import digitization.digitization.module.ServiceHistory;
import java.time.LocalDate;
import java.util.List;

public class EmployeeDto {



    private Long id;
    private String ecpfNumber;
    private String panNumber;
    private String employeeName;
    private String fatherName;
    private String motherName;
    private LocalDate dateOfBirth;
    private String religion;
    private String mainEmployeeType;
    private String community;
    private String caste;
    private String personalIdentificationmark1;
    private String personalIdentificationmark2;
    private String currentDesignation;
    private LocalDate dateOfEntry;
    private String nativePlaceAndTaluk;
    private String district;

    public EmployeeDto() {
    }
    public EmployeeDto(Long id, String ecpfNumber, String panNumber, String employeeName, String fatherName, String motherName, LocalDate dateOfBirth, String religion, String community, String caste, String personalIdentificationmark1, String personalIdentificationmark2, String currentDesignation, LocalDate dateOfEntry, String nativePlaceAndTaluk, String district) {
        this.id = id;
        this.ecpfNumber = ecpfNumber;
        this.panNumber = panNumber;
        this.employeeName = employeeName;
        this.fatherName = fatherName;
        this.motherName = motherName;
        this.dateOfBirth = dateOfBirth;
        this.religion = religion;
        this.community = community;
        this.caste = caste;
        this.personalIdentificationmark1 = personalIdentificationmark1;
        this.personalIdentificationmark2 = personalIdentificationmark2;
        this.currentDesignation = currentDesignation;
        this.dateOfEntry = dateOfEntry;
        this.nativePlaceAndTaluk = nativePlaceAndTaluk;
        this.district = district;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEcpfNumber() {
        return ecpfNumber;
    }

    public void setEcpfNumber(String ecpfNumber) {
        this.ecpfNumber = ecpfNumber;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getFatherName() {
        return fatherName;
    }

    public void setFatherName(String fatherName) {
        this.fatherName = fatherName;
    }

    public String getMotherName() {
        return motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getReligion() {
        return religion;
    }

    public void setReligion(String religion) {
        this.religion = religion;
    }

    public String getCommunity() {
        return community;
    }

    public void setCommunity(String community) {
        this.community = community;
    }

    public String getCaste() {
        return caste;
    }

    public void setCaste(String caste) {
        this.caste = caste;
    }

    public String getPersonalIdentificationmark1() {
        return personalIdentificationmark1;
    }

    public void setPersonalIdentificationmark1(String personalIdentificationmark1) {
        this.personalIdentificationmark1 = personalIdentificationmark1;
    }

    public String getPersonalIdentificationmark2() {
        return personalIdentificationmark2;
    }

    public void setPersonalIdentificationmark2(String personalIdentificationmark2) {
        this.personalIdentificationmark2 = personalIdentificationmark2;
    }

    public String getCurrentDesignation() {
        return currentDesignation;
    }

    public void setCurrentDesignation(String currentDesignation) {
        this.currentDesignation = currentDesignation;
    }

    public LocalDate getDateOfEntry() {
        return dateOfEntry;
    }

    public void setDateOfEntry(LocalDate dateOfEntry) {
        this.dateOfEntry = dateOfEntry;
    }

    public String getNativePlaceAndTaluk() {
        return nativePlaceAndTaluk;
    }

    public void setNativePlaceAndTaluk(String nativePlaceAndTaluk) {
        this.nativePlaceAndTaluk = nativePlaceAndTaluk;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getMainEmployeeType() {
        return mainEmployeeType;
    }

    public void setMainEmployeeType(String mainEmployeeType) {
        this.mainEmployeeType = mainEmployeeType;
    }
}
