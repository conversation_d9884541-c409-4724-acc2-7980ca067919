package digitization.digitization.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class EmployeeWithStatusDto {

    // Employee fields
    private Long id;
    private String employeeName;
    private String fatherName;
    private LocalDate dateOfBirth;
    private String religion;
    private String community;
    private String identificationMark1;
    private String identificationMark2;
    private String designation;
    private LocalDate dateOfEntry;
    private String district;
    private String authority;
    private String educationalQualification;
    private String location;

    // Status fields
    private String status;
    private String approvedBy;
    private String rejectedBy;
    private String remarks;
    private String createdBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime statusCreatedAt;
    private LocalDateTime statusUpdatedAt;

    public EmployeeWithStatusDto() {
    }

    public EmployeeWithStatusDto(Long id, String employeeName, String fatherName, LocalDate dateOfBirth,
                                 String religion, String community, String identificationMark1, String identificationMark2,
                                 String designation, LocalDate dateOfEntry, String district, String authority,
                                 String educationalQualification, String location, String status, String approvedBy,
                                 String rejectedBy, String remarks, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.employeeName = employeeName;
        this.fatherName = fatherName;
        this.dateOfBirth = dateOfBirth;
        this.religion = religion;
        this.community = community;
        this.identificationMark1 = identificationMark1;
        this.identificationMark2 = identificationMark2;
        this.designation = designation;
        this.dateOfEntry = dateOfEntry;
        this.district = district;
        this.authority = authority;
        this.educationalQualification = educationalQualification;
        this.location = location;
        this.status = status;
        this.approvedBy = approvedBy;
        this.rejectedBy = rejectedBy;
        this.remarks = remarks;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getFatherName() {
        return fatherName;
    }

    public void setFatherName(String fatherName) {
        this.fatherName = fatherName;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getReligion() {
        return religion;
    }

    public void setReligion(String religion) {
        this.religion = religion;
    }

    public String getCommunity() {
        return community;
    }

    public void setCommunity(String community) {
        this.community = community;
    }

    public String getIdentificationMark1() {
        return identificationMark1;
    }

    public void setIdentificationMark1(String identificationMark1) {
        this.identificationMark1 = identificationMark1;
    }

    public String getIdentificationMark2() {
        return identificationMark2;
    }

    public void setIdentificationMark2(String identificationMark2) {
        this.identificationMark2 = identificationMark2;
    }

    public String getDesignation() {
        return designation;
    }

    public void setDesignation(String designation) {
        this.designation = designation;
    }

    public LocalDate getDateOfEntry() {
        return dateOfEntry;
    }

    public void setDateOfEntry(LocalDate dateOfEntry) {
        this.dateOfEntry = dateOfEntry;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAuthority() {
        return authority;
    }

    public void setAuthority(String authority) {
        this.authority = authority;
    }

    public String getEducationalQualification() {
        return educationalQualification;
    }

    public void setEducationalQualification(String educationalQualification) {
        this.educationalQualification = educationalQualification;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public String getRejectedBy() {
        return rejectedBy;
    }

    public void setRejectedBy(String rejectedBy) {
        this.rejectedBy = rejectedBy;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getStatusCreatedAt() {
        return statusCreatedAt;
    }

    public void setStatusCreatedAt(LocalDateTime statusCreatedAt) {
        this.statusCreatedAt = statusCreatedAt;
    }

    public LocalDateTime getStatusUpdatedAt() {
        return statusUpdatedAt;
    }

    public void setStatusUpdatedAt(LocalDateTime statusUpdatedAt) {
        this.statusUpdatedAt = statusUpdatedAt;
    }

    @Override
    public String toString() {
        return "EmployeeWithStatusDto{" +
                "id=" + id +
                ", employeeName='" + employeeName + '\'' +
                ", fatherName='" + fatherName + '\'' +
                ", dateOfBirth=" + dateOfBirth +
                ", religion='" + religion + '\'' +
                ", community='" + community + '\'' +
                ", identificationMark1='" + identificationMark1 + '\'' +
                ", identificationMark2='" + identificationMark2 + '\'' +
                ", designation='" + designation + '\'' +
                ", dateOfEntry=" + dateOfEntry +
                ", district='" + district + '\'' +
                ", authority='" + authority + '\'' +
                ", educationalQualification='" + educationalQualification + '\'' +
                ", location='" + location + '\'' +
                ", status='" + status + '\'' +
                ", approvedBy='" + approvedBy + '\'' +
                ", rejectedBy='" + rejectedBy + '\'' +
                ", remarks='" + remarks + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
