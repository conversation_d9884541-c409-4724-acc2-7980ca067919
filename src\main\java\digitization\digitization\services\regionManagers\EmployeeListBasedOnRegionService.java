package digitization.digitization.services.regionManagers;

import digitization.digitization.module.Employee;
import digitization.digitization.module.EmployeeStatus;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface EmployeeListBasedOnRegionService {

    List<EmployeeStatus> getPendingEmployeesByRegion(String status, String district);

    List<Employee> getPendingEmployeesByDistrict(String role, String district);

    void updateEmployeeStatus(Long empId, String newStatus, String updatedBy, String remarks, String role);
    List<Employee> getSUPERINTENDENTandAMAPPROVEDEmployeesByDistrict(String role, String district);
    List<Employee> getACCEPTED_BY_DRM_MAEmployeesByDistrict(String role, String district);
}