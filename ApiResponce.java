package digitization.digitization.response.responce;

import com.fasterxml.jackson.annotation.JsonInclude;

public class ApiResponce {

    private boolean status;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long totalRecords;
    private Object data;


    public ApiResponce() {
    }

    public ApiResponce(boolean status, Object data) {
        this.status = status;
        this.data = data;
    }

    public ApiResponce(boolean status, Long totalRecords, Object data) {
        this.status = status;
        this.totalRecords = totalRecords;
        this.data = data;
    }



    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public Long getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(Long totalRecords) {
        this.totalRecords = totalRecords;
    }
}

