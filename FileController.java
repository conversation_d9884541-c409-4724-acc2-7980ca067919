package digitization.digitization.controller;

import digitization.digitization.services.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@CrossOrigin(value = "*")
@RequestMapping("/api/files")
public class FileController {

    @Autowired
    private FileStorageService fileStorageService;

    @GetMapping("/employee_{employeeId}/{fileName:.+}")
    public ResponseEntity<Resource> downloadFile(
            @PathVariable Long employeeId,
            @PathVariable String fileName,
            HttpServletRequest request) {

        // Construct the file path
        String filePath = "employee_" + employeeId + "/" + fileName;
        
        // Load file as Resource
        Resource resource = fileStorageService.loadFileAsResource(filePath);

        // Try to determine file's content type
        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            // Could not determine file type
        }

        // Fallback to the default content type if type could not be determined
        if (contentType == null) {
            contentType = "application/octet-stream";
        }

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }

    @GetMapping("/{filePath:.+}")
    public ResponseEntity<Resource> downloadFileByPath(
            @PathVariable String filePath,
            HttpServletRequest request) {

        // Load file as Resource
        Resource resource = fileStorageService.loadFileAsResource(filePath);

        // Try to determine file's content type
        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            // Could not determine file type
        }

        // Fallback to the default content type if type could not be determined
        if (contentType == null) {
            contentType = "application/octet-stream";
        }

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }

    @GetMapping("/view/employee_{employeeId}/{fileName:.+}")
    public ResponseEntity<Resource> viewFile(
            @PathVariable Long employeeId,
            @PathVariable String fileName,
            HttpServletRequest request) {

        // Construct the file path
        String filePath = "employee_" + employeeId + "/" + fileName;
        
        // Load file as Resource
        Resource resource = fileStorageService.loadFileAsResource(filePath);

        // For PDF files, set content type to display in browser
        String contentType = "application/pdf";

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }
}
