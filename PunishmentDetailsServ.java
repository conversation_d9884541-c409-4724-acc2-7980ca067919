package digitization.digitization.services.implementation;

import digitization.digitization.module.PunishmentDetails;

import java.time.LocalDate;
import java.util.List;

public interface PunishmentDetailsServ {
    List<PunishmentDetails> getPunishmentDetailsList();
    PunishmentDetails getPunishmentDetailsById(Long id);
    PunishmentDetails createPunishmentDetails(PunishmentDetails punishmentDetails);
    PunishmentDetails updatePunishmentDetails(Long id, PunishmentDetails punishmentDetailsDetails);
    boolean deletePunishmentDetails(Long punishmentDetailsId);
    List<PunishmentDetails> getPunishmentDetailsByType(String punishmenttype);
    List<PunishmentDetails> getPunishmentDetailsByDate(LocalDate date);
    List<PunishmentDetails> getPunishmentDetailsByDateRange(LocalDate startDate, LocalDate endDate);
    List<PunishmentDetails> searchPunishmentDetailsByCaseDetails(String keyword);
    List<PunishmentDetails> getAllPunishmentDetailsOrderByDate();
    List<PunishmentDetails> getPunishmentDetailsByTypeOrderByDate(String punishmenttype);
}
