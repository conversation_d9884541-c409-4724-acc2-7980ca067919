//package digitization.digitization;
//
//import digitization.digitization.module.*;
//import digitization.digitization.repository.*;
//import digitization.digitization.services.EmployeeService;
//import digitization.digitization.services.EmployeeStatusService;
//import digitization.digitization.services.DocumentService;
//import digitization.digitization.services.FileStorageService;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.test.context.ActiveProfiles;
//
//import java.time.LocalDate;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Optional;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//@ActiveProfiles("test")
//class EmployeeDeleteTest {
//
//    @Mock
//    private EmployeeRepo employeeRepo;
//
//    @Mock
//    private DocumentRepository documentRepository;
//
//    @Mock
//    private ServiceRepository serviceRepository;
//
//    @Mock
//    private LeaveRepository leaveRepository;
//
//    @Mock
//    private EducationQualificationRepository educationQualificationRepository;
//
//    @Mock
//    private PunishmentDetailsRepository punishmentDetailsRepository;
//
//    @Mock
//    private TrainingDetailsRepository trainingDetailsRepository;
//
//    @Mock
//    private NomineeRepository nomineeRepository;
//
//    @Mock
//    private ProfileRepository profileRepository;
//
//    @Mock
//    private AccountDetailsRepository accountDetailsRepository;
//
//    @Mock
//    private SalaryDetailsRepository salaryDetailsRepository;
//
//    @Mock
//    private EmployeeStatusService employeeStatusService;
//
//    @Mock
//    private DocumentService documentService;
//
//    @Mock
//    private FileStorageService fileStorageService;
//
//    @InjectMocks
//    private EmployeeService employeeService;
//
//    private Employee testEmployee;
//    private Long testEmployeeId = 1L;
//
//    @BeforeEach
//    void setUp() {
//        testEmployee = new Employee();
//        testEmployee.setId(testEmployeeId);
//        testEmployee.setEmployeeName("Test Employee");
//        testEmployee.setEcpfNumber("TEST123");
//        testEmployee.setPanNumber("**********");
//        testEmployee.setEmail("<EMAIL>");
//        testEmployee.setProfilePhoto("test-photo.jpg");
//    }
//
//    @Test
//    void testDeleteEmployee_Success() {
//        // Arrange
//        when(employeeRepo.findById(testEmployeeId)).thenReturn(Optional.of(testEmployee));
//
//        // Mock related records
//        List<Document> documents = Arrays.asList(createTestDocument());
//        List<ServiceHistory> serviceHistories = Arrays.asList(createTestServiceHistory());
//        List<Leave> leaves = Arrays.asList(createTestLeave());
//        List<EducationQualification> educations = Arrays.asList(createTestEducation());
//        List<PunishmentDetails> punishments = Arrays.asList(createTestPunishment());
//        List<TrainingDetails> trainings = Arrays.asList(createTestTraining());
//        List<Nominee> nominees = Arrays.asList(createTestNominee());
//        List<SalaryDetails> salaryDetails = Arrays.asList(createTestSalaryDetails());
//
//        when(documentRepository.findByEmployee_Id(testEmployeeId)).thenReturn(documents);
//        when(serviceRepository.findByEmployeeIdOrderByDateDesc(testEmployeeId)).thenReturn(serviceHistories);
//        when(leaveRepository.findByEmployeeId(testEmployeeId)).thenReturn(leaves);
//        when(educationQualificationRepository.findByEmployeeId(testEmployeeId)).thenReturn(educations);
//        when(punishmentDetailsRepository.findByEmployeeId(testEmployeeId)).thenReturn(punishments);
//        when(trainingDetailsRepository.findByEmployeeId(testEmployeeId)).thenReturn(trainings);
//        when(nomineeRepository.findByEmployeeId(testEmployeeId)).thenReturn(nominees);
//        when(salaryDetailsRepository.findAllByEmployeeIdOrderByUpdatedAtDesc(testEmployeeId)).thenReturn(salaryDetails);
//
//        when(profileRepository.findByEmployeeId(testEmployeeId)).thenReturn(Optional.of(createTestProfile()));
//        when(accountDetailsRepository.findByEmployeeId(testEmployeeId)).thenReturn(Optional.of(createTestAccountDetails()));
//
//        when(employeeStatusService.existsByEmpId(testEmployeeId)).thenReturn(true);
//
//        // Act
//        boolean result = employeeService.deleteEmployee(testEmployeeId);
//
//        // Assert
//        assertTrue(result);
//
//        // Verify all delete operations were called
//        verify(documentRepository).deleteAll(documents);
//        verify(serviceRepository).deleteAll(serviceHistories);
//        verify(leaveRepository).deleteAll(leaves);
//        verify(educationQualificationRepository).deleteAll(educations);
//        verify(punishmentDetailsRepository).deleteAll(punishments);
//        verify(trainingDetailsRepository).deleteAll(trainings);
//        verify(nomineeRepository).deleteAll(nominees);
//        verify(salaryDetailsRepository).deleteAll(salaryDetails);
//        verify(profileRepository).delete(any(Profile.class));
//        verify(accountDetailsRepository).delete(any(AccountDetails.class));
//        verify(employeeStatusService).deleteEmployeeStatus(testEmployeeId);
//        verify(employeeRepo).deleteById(testEmployeeId);
//    }
//
//    @Test
//    void testDeleteEmployee_NotFound() {
//        // Arrange
//        when(employeeRepo.findById(testEmployeeId)).thenReturn(Optional.empty());
//
//        // Act
//        boolean result = employeeService.deleteEmployee(testEmployeeId);
//
//        // Assert
//        assertFalse(result);
//        verify(employeeRepo, never()).deleteById(any());
//    }
//
//    @Test
//    void testDeleteEmployee_ExceptionHandling() {
//        // Arrange
//        when(employeeRepo.findById(testEmployeeId)).thenReturn(Optional.of(testEmployee));
//        when(documentRepository.findByEmployee_Id(testEmployeeId)).thenThrow(new RuntimeException("Database error"));
//
//        // Act & Assert
//        assertThrows(RuntimeException.class, () -> employeeService.deleteEmployee(testEmployeeId));
//    }
//
//    // Helper methods to create test objects
//    private Document createTestDocument() {
//        Document doc = new Document();
//        doc.setId(1L);
//        doc.setFileName("test.pdf");
//        doc.setFilePath("/uploads/test.pdf");
//        return doc;
//    }
//
//    private ServiceHistory createTestServiceHistory() {
//        ServiceHistory history = new ServiceHistory();
//        history.setId(1L);
//        history.setDate(LocalDate.now());
//        return history;
//    }
//
//    private Leave createTestLeave() {
//        Leave leave = new Leave();
//        leave.setId(1L);
//        leave.setLeaveType("Annual");
//        return leave;
//    }
//
//    private EducationQualification createTestEducation() {
//        EducationQualification education = new EducationQualification();
//        education.setId(1L);
//        education.setQualification("Bachelor's");
//        return education;
//    }
//
//    private PunishmentDetails createTestPunishment() {
//        PunishmentDetails punishment = new PunishmentDetails();
//        punishment.setId(1L);
//        punishment.setPunishmenttype("Warning");
//        return punishment;
//    }
//
//    private TrainingDetails createTestTraining() {
//        TrainingDetails training = new TrainingDetails();
//        training.setId(1L);
//        training.setTrainingtype("Technical");
//        return training;
//    }
//
//    private Nominee createTestNominee() {
//        Nominee nominee = new Nominee();
//        nominee.setId(1L);
//        nominee.setNomineename("Test Nominee");
//        return nominee;
//    }
//
//    private SalaryDetails createTestSalaryDetails() {
//        SalaryDetails salary = new SalaryDetails();
//        salary.setId(1L);
//        return salary;
//    }
//
//    private Profile createTestProfile() {
//        Profile profile = new Profile();
//        profile.setId(1L);
//        return profile;
//    }
//
//    private AccountDetails createTestAccountDetails() {
//        AccountDetails account = new AccountDetails();
//        account.setId(1L);
//        return account;
//    }
//}
