package digitization.digitization.dto;

import java.time.LocalDate;
import java.util.List;

public class EmployeeCreationDto {

    // Employee basic details
    private String ecpfNumber;
    private String empId;
    private String panNumber;
    private String employeeName;
    private String email;
    private String mobileNumber;
    private String registerNumber;
    private String section;
    private String presentAddress;
    private String permanentAddress;
    private String fatherName;
    private String motherName;
    private LocalDate dateOfBirth;
    private String religion;
    private String community;
    private String caste;
    private String personalIdentificationmark1;
    private String personalIdentificationmark2;
    private String currentDesignation;
    private LocalDate dateOfEntry;
    private String district;
    private String nativePlaceAndTaluk;
    private String mainEmployeeType;
    private String seasonalCategory;
    private String loadManCategory;

    // New fields added
    private String spouseName;
    private LocalDate supernumericDateOfJoining;
    private String supernumericRemarks;
    private String othersRemarks;
    private String maritalStatus;

    // User who is creating the employee
    private String createdBy;

    private String gender;

    // Salary fields (for backward compatibility - will be mapped to salaryDetails)
    private String group;
    private String payband;
    private java.math.BigDecimal gradepay;

    // Profile details (if different from employee)
    private ProfileDto profile;

    // Account details
    private AccountDetailsDto accountDetails;

    // Service history records
    private List<ServiceHistoryDto> serviceHistory;

    // Leave balances
    private List<LeaveDto> leaveBalances;

    // Education qualifications
    private List<EducationQualificationDto> educationQualifications;

    // Punishment details
    private List<PunishmentDetailsDto> punishmentDetails;

    // Training details
    private List<TrainingDetailsDto> trainingDetails;

    // Nominees
    private List<NomineeDto> nominees;

    // Documents
    private List<DocumentCreationDto> documents;

    // Salary details
    private SalaryDetailsDto salaryDetails;

    private EmployeeStatusDto employeeStatusDto;

    public EmployeeCreationDto() {
    }

    public String getMainEmployeeType() {
        return mainEmployeeType;
    }

    public void setMainEmployeeType(String mainEmployeeType) {
        this.mainEmployeeType = mainEmployeeType;
    }

    public String getSeasonalCategory() {
        return seasonalCategory;
    }

    public void setSeasonalCategory(String seasonalCategory) {
        this.seasonalCategory = seasonalCategory;
    }

    public String getLoadManCategory() {
        return loadManCategory;
    }

    public void setLoadManCategory(String loadManCategory) {
        this.loadManCategory = loadManCategory;
    }

    // Getters and setters
    public String getEcpfNumber() {
        return ecpfNumber;
    }

    public void setEcpfNumber(String ecpfNumber) {
        this.ecpfNumber = ecpfNumber;
    }

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getRegisterNumber() {
        return registerNumber;
    }

    public void setRegisterNumber(String registerNumber) {
        this.registerNumber = registerNumber;
    }

    public String getSection() {
        return section;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getPayband() {
        return payband;
    }

    public void setPayband(String payband) {
        this.payband = payband;
    }

    public java.math.BigDecimal getGradepay() {
        return gradepay;
    }

    public void setGradepay(java.math.BigDecimal gradepay) {
        this.gradepay = gradepay;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getPresentAddress() {
        return presentAddress;
    }

    public void setPresentAddress(String presentAddress) {
        this.presentAddress = presentAddress;
    }

    public String getPermanentAddress() {
        return permanentAddress;
    }

    public void setPermanentAddress(String permanentAddress) {
        this.permanentAddress = permanentAddress;
    }

    public String getFatherName() {
        return fatherName;
    }

    public void setFatherName(String fatherName) {
        this.fatherName = fatherName;
    }

    public String getMotherName() {
        return motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getReligion() {
        return religion;
    }

    public void setReligion(String religion) {
        this.religion = religion;
    }

    public String getCommunity() {
        return community;
    }

    public void setCommunity(String community) {
        this.community = community;
    }

    public String getCaste() {
        return caste;
    }

    public void setCaste(String caste) {
        this.caste = caste;
    }

    public String getPersonalIdentificationmark1() {
        return personalIdentificationmark1;
    }

    public void setPersonalIdentificationmark1(String personalIdentificationmark1) {
        this.personalIdentificationmark1 = personalIdentificationmark1;
    }

    public String getPersonalIdentificationmark2() {
        return personalIdentificationmark2;
    }

    public void setPersonalIdentificationmark2(String personalIdentificationmark2) {
        this.personalIdentificationmark2 = personalIdentificationmark2;
    }

    public String getCurrentDesignation() {
        return currentDesignation;
    }

    public void setCurrentDesignation(String currentDesignation) {
        this.currentDesignation = currentDesignation;
    }

    public LocalDate getDateOfEntry() {
        return dateOfEntry;
    }

    public void setDateOfEntry(LocalDate dateOfEntry) {
        this.dateOfEntry = dateOfEntry;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getNativePlaceAndTaluk() {
        return nativePlaceAndTaluk;
    }

    public void setNativePlaceAndTaluk(String nativePlaceAndTaluk) {
        this.nativePlaceAndTaluk = nativePlaceAndTaluk;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public ProfileDto getProfile() {
        return profile;
    }

    public void setProfile(ProfileDto profile) {
        this.profile = profile;
    }

    public AccountDetailsDto getAccountDetails() {
        return accountDetails;
    }

    public void setAccountDetails(AccountDetailsDto accountDetails) {
        this.accountDetails = accountDetails;
    }

    public List<ServiceHistoryDto> getServiceHistory() {
        return serviceHistory;
    }

    public void setServiceHistory(List<ServiceHistoryDto> serviceHistory) {
        this.serviceHistory = serviceHistory;
    }

    public List<LeaveDto> getLeaveBalances() {
        return leaveBalances;
    }

    public void setLeaveBalances(List<LeaveDto> leaveBalances) {
        this.leaveBalances = leaveBalances;
    }

    public List<EducationQualificationDto> getEducationQualifications() {
        return educationQualifications;
    }

    public void setEducationQualifications(List<EducationQualificationDto> educationQualifications) {
        this.educationQualifications = educationQualifications;
    }

    public List<PunishmentDetailsDto> getPunishmentDetails() {
        return punishmentDetails;
    }

    public void setPunishmentDetails(List<PunishmentDetailsDto> punishmentDetails) {
        this.punishmentDetails = punishmentDetails;
    }

    public List<TrainingDetailsDto> getTrainingDetails() {
        return trainingDetails;
    }

    public void setTrainingDetails(List<TrainingDetailsDto> trainingDetails) {
        this.trainingDetails = trainingDetails;
    }

    public List<NomineeDto> getNominees() {
        return nominees;
    }

    public void setNominees(List<NomineeDto> nominees) {
        this.nominees = nominees;
    }

    public List<DocumentCreationDto> getDocuments() {
        return documents;
    }

    public void setDocuments(List<DocumentCreationDto> documents) {
        this.documents = documents;
    }

    public SalaryDetailsDto getSalaryDetails() {
        return salaryDetails;
    }

    public void setSalaryDetails(SalaryDetailsDto salaryDetails) {
        this.salaryDetails = salaryDetails;
    }

    public EmployeeStatusDto getEmployeeStatusDto() {
        return employeeStatusDto;
    }

    public void setEmployeeStatusDto(EmployeeStatusDto employeeStatusDto) {
        this.employeeStatusDto = employeeStatusDto;
    }

    public String getSpouseName() {
        return spouseName;
    }

    public void setSpouseName(String spouseName) {
        this.spouseName = spouseName;
    }

    public LocalDate getSupernumericDateOfJoining() {
        return supernumericDateOfJoining;
    }

    public void setSupernumericDateOfJoining(LocalDate supernumericDateOfJoining) {
        this.supernumericDateOfJoining = supernumericDateOfJoining;
    }

    public String getSupernumericRemarks() {
        return supernumericRemarks;
    }

    public void setSupernumericRemarks(String supernumericRemarks) {
        this.supernumericRemarks = supernumericRemarks;
    }

    public String getOthersRemarks() {
        return othersRemarks;
    }

    public void setOthersRemarks(String othersRemarks) {
        this.othersRemarks = othersRemarks;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }
}
