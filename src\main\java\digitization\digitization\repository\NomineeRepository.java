package digitization.digitization.repository;

import digitization.digitization.module.Nominee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface NomineeRepository extends JpaRepository<Nominee, Long> {
    Optional<Nominee> findById(Long id);
    List<Nominee> findByNomineename(String nomineename);
    List<Nominee> findByRelationship(String relationship);
    List<Nominee> findByGender(String gender);
    List<Nominee> findByAge(Integer age);
    List<Nominee> findByAgeBetween(Integer minAge, Integer maxAge);
    List<Nominee> findByPercentageofshare(Double percentageofshare);
    List<Nominee> findByPercentageofshareGreaterThan(Double percentage);
    List<Nominee> findByPercentageofshareGreaterThanEqual(Double percentage);

    @Query("SELECT n FROM Nominee n WHERE n.nomineename LIKE %:name%")
    List<Nominee> findByNomineeNameContaining(@Param("name") String name);

    @Query("SELECT n FROM Nominee n WHERE n.address LIKE %:keyword%")
    List<Nominee> findByAddressContaining(@Param("keyword") String keyword);

    List<Nominee> findByRelationshipAndGender(String relationship, String gender);
    List<Nominee> findByOrderByPercentageofshareDesc();
    List<Nominee> findByOrderByNomineenameAsc();
    List<Nominee> findByEmployeeId(Long employeeId);
}
