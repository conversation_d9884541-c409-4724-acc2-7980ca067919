package digitization.digitization.controller;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.PunishmentDetails;
import digitization.digitization.services.PunishmentDetailsService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("punishmentdetails")
public class PunishmentDetailsController {
    
    @Autowired
    PunishmentDetailsService punishmentDetailsService;

    @GetMapping("/getlist")
    public List<PunishmentDetails> getPunishmentDetailsList() {
        return punishmentDetailsService.getPunishmentDetailsList();
    }

    @GetMapping("/getPunishmentDetails/{id}")
    public ResponseEntity<PunishmentDetails> getPunishmentDetailsById(@PathVariable Long id) throws Exception {
        PunishmentDetails punishmentDetails = punishmentDetailsService.getPunishmentDetailsById(id);

        if (punishmentDetails == null) {
            throw new ResourceNotFoundException("Punishment details not found with id: " + id, null, null);
        }

        return ResponseEntity.ok(punishmentDetails);
    }

    @GetMapping("/getByType/{punishmenttype}")
    public List<PunishmentDetails> getPunishmentDetailsByType(@PathVariable String punishmenttype) {
        return punishmentDetailsService.getPunishmentDetailsByType(punishmenttype);
    }

    @GetMapping("/getByDate/{date}")
    public List<PunishmentDetails> getPunishmentDetailsByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        return punishmentDetailsService.getPunishmentDetailsByDate(date);
    }

    @GetMapping("/getByDateRange")
    public List<PunishmentDetails> getPunishmentDetailsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return punishmentDetailsService.getPunishmentDetailsByDateRange(startDate, endDate);
    }

    @GetMapping("/searchByCaseDetails/{keyword}")
    public List<PunishmentDetails> searchPunishmentDetailsByCaseDetails(@PathVariable String keyword) {
        return punishmentDetailsService.searchPunishmentDetailsByCaseDetails(keyword);
    }

    @GetMapping("/getAllOrderByDate")
    public List<PunishmentDetails> getAllPunishmentDetailsOrderByDate() {
        return punishmentDetailsService.getAllPunishmentDetailsOrderByDate();
    }

    @GetMapping("/getByTypeOrderByDate/{punishmenttype}")
    public List<PunishmentDetails> getPunishmentDetailsByTypeOrderByDate(@PathVariable String punishmenttype) {
        return punishmentDetailsService.getPunishmentDetailsByTypeOrderByDate(punishmenttype);
    }

    @PostMapping("/createPunishmentDetails")
    public PunishmentDetails createPunishmentDetails(@Valid @RequestBody PunishmentDetails punishmentDetails) {
        return punishmentDetailsService.createPunishmentDetails(punishmentDetails);
    }

    @PutMapping("/updatePunishmentDetails/{id}")
    public PunishmentDetails updatePunishmentDetails(@PathVariable Long id, @Valid @RequestBody PunishmentDetails punishmentDetailsDetails) {
        return punishmentDetailsService.updatePunishmentDetails(id, punishmentDetailsDetails);
    }

    @DeleteMapping("/deletePunishmentDetails/{id}")
    public ResponseEntity<?> deletePunishmentDetails(@PathVariable Long id) {
        boolean deleted = punishmentDetailsService.deletePunishmentDetails(id);
        if (deleted) {
            return ResponseEntity.ok().build();
        } else {
            throw new ResourceNotFoundException("Punishment details not found with id: " + id, null, null);
        }
    }
}
