package digitization.digitization.services;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.TrainingDetails;
import digitization.digitization.repository.TrainingDetailsRepository;
import digitization.digitization.services.implementation.TrainingDetailsServ;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
public class TrainingDetailsService implements TrainingDetailsServ {
    
    @Autowired
    TrainingDetailsRepository trainingDetailsRepository;

    @Override
    public List<TrainingDetails> getTrainingDetailsList() {
        List<TrainingDetails> list = trainingDetailsRepository.findAll();
        return list;
    }

    @Override
    public TrainingDetails getTrainingDetailsById(Long trainingDetailsId) {
        Optional<TrainingDetails> optionalTrainingDetails = this.trainingDetailsRepository.findById(trainingDetailsId);
        if (!optionalTrainingDetails.isPresent()) {
            throw new ResourceNotFoundException("Training details not found with id: " + trainingDetailsId, null, null);
        }
        return optionalTrainingDetails.get();
    }

    @Override
    public TrainingDetails createTrainingDetails(TrainingDetails trainingDetails) {
        TrainingDetails trainDetails = new TrainingDetails();
        trainDetails.setId(trainingDetails.getId());
        trainDetails.setTrainingtype(trainingDetails.getTrainingtype());
        trainDetails.setDate(trainingDetails.getDate());

        trainingDetailsRepository.save(trainDetails);
        return trainDetails;
    }

    @Override
    public TrainingDetails updateTrainingDetails(Long id, TrainingDetails trainingDetailsDetails) {
        TrainingDetails trainingDetails = trainingDetailsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Training details not found with id: " + id, null, null));

        trainingDetails.setTrainingtype(trainingDetailsDetails.getTrainingtype());
        trainingDetails.setDate(trainingDetailsDetails.getDate());

        return trainingDetailsRepository.save(trainingDetails);
    }

    @Override
    public boolean deleteTrainingDetails(Long trainingDetailsId) {
        try {
            Optional<TrainingDetails> trainingDetails = trainingDetailsRepository.findById(trainingDetailsId);
            if (trainingDetails.isPresent()) {
                trainingDetailsRepository.deleteById(trainingDetailsId);
                return true;
            } else {
                System.out.println("Training details not found with ID: " + trainingDetailsId);
                return false;
            }
        } catch (Exception e) {
            System.out.println("Error deleting training details: " + e.getMessage());
            return false;
        }
    }

    @Override
    public List<TrainingDetails> getTrainingDetailsByType(String trainingtype) {
        return trainingDetailsRepository.findByTrainingtype(trainingtype);
    }

    @Override
    public List<TrainingDetails> getTrainingDetailsByDate(LocalDate date) {
        return trainingDetailsRepository.findByDate(date);
    }

    @Override
    public List<TrainingDetails> getTrainingDetailsByDateRange(LocalDate startDate, LocalDate endDate) {
        return trainingDetailsRepository.findByDateBetween(startDate, endDate);
    }

    @Override
    public List<TrainingDetails> getTrainingDetailsAfterDate(LocalDate date) {
        return trainingDetailsRepository.findByDateAfter(date);
    }

    @Override
    public List<TrainingDetails> getTrainingDetailsBeforeDate(LocalDate date) {
        return trainingDetailsRepository.findByDateBefore(date);
    }

    @Override
    public List<TrainingDetails> searchTrainingDetailsByType(String keyword) {
        return trainingDetailsRepository.findByTrainingTypeContaining(keyword);
    }

    @Override
    public List<TrainingDetails> getAllTrainingDetailsOrderByDateDesc() {
        return trainingDetailsRepository.findByOrderByDateDesc();
    }

    @Override
    public List<TrainingDetails> getAllTrainingDetailsOrderByDateAsc() {
        return trainingDetailsRepository.findByOrderByDateAsc();
    }

    @Override
    public List<TrainingDetails> getTrainingDetailsByTypeOrderByDateDesc(String trainingtype) {
        return trainingDetailsRepository.findByTrainingtypeOrderByDateDesc(trainingtype);
    }

    @Override
    public List<TrainingDetails> getTrainingDetailsByTypeOrderByDateAsc(String trainingtype) {
        return trainingDetailsRepository.findByTrainingtypeOrderByDateAsc(trainingtype);
    }

    @Override
    public List<String> getDistinctTrainingTypes() {
        return trainingDetailsRepository.findDistinctTrainingTypes();
    }
}
