package digitization.digitization.repository;

import digitization.digitization.dto.EmployeeNameDto;
import digitization.digitization.module.Employee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EmployeeRepo extends JpaRepository<Employee,Long> {
    Optional<Employee> findById(Long id);
    List<Employee> findAllByOrderByIdDesc();

    @Query("SELECT COUNT(e) FROM Employee e")
    Long getEmployeeCount();



    @Query(value = "SELECT e.* FROM employee e INNER JOIN employee_status es ON e.id = es.emp_id WHERE es.status = :status ORDER BY es.updated_at DESC", nativeQuery = true)
    List<Employee> findEmployeesByStatus(@Param("status") String status);

    Optional<Employee> findByEcpfNumberAndPanNumber(String empId, String panNumber);
    Optional<Employee> findByPanNumber(String panNumber);

    List<Employee> findByIdInAndDistrict(List<Long> empIds, String district);

    /**
     * Find employees by IDs and district with flexible district matching (handles whitespace variations)
     */
    @Query("SELECT e FROM Employee e WHERE e.id IN :empIds AND TRIM(REPLACE(e.district, ' ', '')) = TRIM(REPLACE(:district, ' ', ''))")
    List<Employee> findByIdInAndDistrictFlexible(@Param("empIds") List<Long> empIds, @Param("district") String district);

    List<Employee> findByCreatedByOrderByIdDesc(String createdBy);

    /**
     * Get employee names by status - optimized query to fetch only id and name (case-insensitive)
     */
    @Query(value = "SELECT e.id, e.employee_name FROM employee e INNER JOIN employee_status es ON e.id = es.emp_id WHERE UPPER(es.status) = UPPER(:status) ORDER BY es.updated_at DESC", nativeQuery = true)
    List<Object[]> findEmployeeNamesByStatus(@Param("status") String status);

    /**
     * Get employee names for approved status - handles both 'approved' and 'APPROVED'
     */
    @Query(value = "SELECT e.id, e.employee_name FROM employee e INNER JOIN employee_status es ON e.id = es.emp_id WHERE UPPER(es.status) IN ('APPROVED', 'ACCEPTED') ORDER BY es.updated_at DESC", nativeQuery = true)
    List<Object[]> findApprovedEmployeeNames();

    /**
     * Get employee names by list of IDs
     */
    @Query(value = "SELECT e.id, e.employee_name FROM employee e WHERE e.id IN :ids", nativeQuery = true)
    List<Object[]> findEmployeeNamesByIds(@Param("ids") List<Long> ids);

}
