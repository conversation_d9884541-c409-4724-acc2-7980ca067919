package digitization.digitization.response.responce;

import java.io.Serializable;

public class TokenResponce implements Serializable {

    private  final static Long serialVersionUID = -858581165856L;

    private boolean status;
    private String token;

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public TokenResponce(String token){
        this.token = token;
    }

    public TokenResponce(boolean status, String token) {
        this.status = status;
        this.token = token;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
