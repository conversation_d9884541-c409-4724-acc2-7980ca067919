package digitization.digitization.auth;

import digitization.digitization.common.ApiResponseBean;
import digitization.digitization.dto.authDto.AuthDto;
import digitization.digitization.dto.authDto.EmployeeAuthDto;
import jakarta.servlet.http.HttpServletRequest;

public interface AuthService {
    ApiResponseBean authenticate(AuthDto authDto, HttpServletRequest request);

    ApiResponseBean employeeLogin(EmployeeAuthDto token);
}
