package digitization.digitization.common;

import java.util.Random;

public class UniqueIdGenerator {

    public static String getId() {


        String randomString = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String Capital_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String Small_chars = "abcdefghijklmnopqrstuvwxyz";


        String values = randomString + Capital_chars + Small_chars + Capital_chars ;



        Random rndm_method = new Random();

        char[] password = new char[30];

        for (int i = 0; i < 30; i++) {

            password[i] = values.charAt(rndm_method.nextInt(values.length()));

        }
        return String.valueOf(password);

    }

    public static String getUserId() {


        String randomString = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        String Capital_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String Small_chars = "abcdefghijklmnopqrstuvwxyz";
        String numbers = "0123456789";


        String values = randomString + Capital_chars + Small_chars + numbers + Small_chars + Capital_chars + numbers;



        Random rndm_method = new Random();

        char[] password = new char[8];

        for (int i = 0; i < 8; i++) {

            password[i] = values.charAt(rndm_method.nextInt(values.length()));

        }
        return String.valueOf(password);

    }


}

