package digitization.digitization.services;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.AccountDetails;
import digitization.digitization.repository.AccountDetailsRepository;
import digitization.digitization.services.implementation.AccountDetailsServ;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class AccountDetailsService implements AccountDetailsServ {
    
    @Autowired
    AccountDetailsRepository accountDetailsRepository;

    @Override
    public List<AccountDetails> getAccountDetailsList() {
        List<AccountDetails> list = accountDetailsRepository.findAll();
        return list;
    }

    @Override
    public AccountDetails getAccountDetailsById(Long accountDetailsId) {
        Optional<AccountDetails> optionalAccountDetails = this.accountDetailsRepository.findById(accountDetailsId);
        if (!optionalAccountDetails.isPresent()) {
            throw new ResourceNotFoundException("Account details not found with id: " + accountDetailsId, null, null);
        }
        return optionalAccountDetails.get();
    }

    @Override
    public AccountDetails createAccountDetails(AccountDetails accountDetails) {
        AccountDetails accDetails = new AccountDetails();
        accDetails.setId(accountDetails.getId());
        accDetails.setBankaccountnumber(accountDetails.getBankaccountnumber());
        accDetails.setIfsccode(accountDetails.getIfsccode());
        accDetails.setBankname(accountDetails.getBankname());
        accDetails.setUannumber(accountDetails.getUannumber());
        accDetails.setAadharnumber(accountDetails.getAadharnumber());

        accountDetailsRepository.save(accDetails);
        return accDetails;
    }

    @Override
    public AccountDetails updateAccountDetails(Long id, AccountDetails accountDetailsDetails) {
        AccountDetails accountDetails = accountDetailsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Account details not found with id: " + id, null, null));

        accountDetails.setBankaccountnumber(accountDetailsDetails.getBankaccountnumber());
        accountDetails.setIfsccode(accountDetailsDetails.getIfsccode());
        accountDetails.setBankname(accountDetailsDetails.getBankname());
        accountDetails.setUannumber(accountDetailsDetails.getUannumber());
        accountDetails.setAadharnumber(accountDetailsDetails.getAadharnumber());

        return accountDetailsRepository.save(accountDetails);
    }

    @Override
    public boolean deleteAccountDetails(Long accountDetailsId) {
        try {
            Optional<AccountDetails> accountDetails = accountDetailsRepository.findById(accountDetailsId);
            if (accountDetails.isPresent()) {
                accountDetailsRepository.deleteById(accountDetailsId);
                return true;
            } else {
                System.out.println("Account details not found with ID: " + accountDetailsId);
                return false;
            }
        } catch (Exception e) {
            System.out.println("Error deleting account details: " + e.getMessage());
            return false;
        }
    }

    @Override
    public AccountDetails getAccountDetailsByBankAccountNumber(String bankaccountnumber) {
        Optional<AccountDetails> optionalAccountDetails = this.accountDetailsRepository.findByBankaccountnumber(bankaccountnumber);
        if (!optionalAccountDetails.isPresent()) {
            throw new ResourceNotFoundException("Account details not found with bank account number: " + bankaccountnumber, null, null);
        }
        return optionalAccountDetails.get();
    }

    @Override
    public AccountDetails getAccountDetailsByAadharNumber(String aadharnumber) {
        Optional<AccountDetails> optionalAccountDetails = this.accountDetailsRepository.findByAadharnumber(aadharnumber);
        if (!optionalAccountDetails.isPresent()) {
            throw new ResourceNotFoundException("Account details not found with Aadhar number: " + aadharnumber, null, null);
        }
        return optionalAccountDetails.get();
    }

    @Override
    public AccountDetails getAccountDetailsByUanNumber(String uannumber) {
        Optional<AccountDetails> optionalAccountDetails = this.accountDetailsRepository.findByUannumber(uannumber);
        if (!optionalAccountDetails.isPresent()) {
            throw new ResourceNotFoundException("Account details not found with UAN number: " + uannumber, null, null);
        }
        return optionalAccountDetails.get();
    }
}
