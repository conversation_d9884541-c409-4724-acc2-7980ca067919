package digitization.digitization.services;

import digitization.digitization.dto.SalaryDetailsDto;
import digitization.digitization.module.Employee;
import digitization.digitization.module.SalaryDetails;
import digitization.digitization.repository.EmployeeRepo;
import digitization.digitization.repository.SalaryDetailsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class SalaryDetailsService {

    @Autowired
    private SalaryDetailsRepository salaryDetailsRepository;

    @Autowired
    private EmployeeRepo employeeRepository;

    /**
     * Create salary details for an employee
     */
    public SalaryDetailsDto createSalaryDetails(SalaryDetailsDto salaryDetailsDto) {
        // Check if employee exists
        Optional<Employee> employeeOpt = employeeRepository.findById(salaryDetailsDto.getEmployeeId());
        if (employeeOpt.isEmpty()) {
            throw new RuntimeException("Employee not found with ID: " + salaryDetailsDto.getEmployeeId());
        }

        Employee employee = employeeOpt.get();

        // Check if salary details already exist for this employee
        if (salaryDetailsRepository.existsByEmployeeId(salaryDetailsDto.getEmployeeId())) {
            throw new RuntimeException("Salary details already exist for employee ID: " + salaryDetailsDto.getEmployeeId());
        }

        SalaryDetails salaryDetails = new SalaryDetails();
        salaryDetails.setLastSalaryRevisedDate(salaryDetailsDto.getLastSalaryRevisedDate());
        salaryDetails.setCurrentWithdrawSalary(salaryDetailsDto.getCurrentWithdrawSalary());
        salaryDetails.setEmployee(employee);
        salaryDetails.setCreatedBy(salaryDetailsDto.getCreatedBy());

        SalaryDetails savedSalaryDetails = salaryDetailsRepository.save(salaryDetails);
        return convertToDto(savedSalaryDetails);
    }

    /**
     * Update salary details for an employee
     */
    public SalaryDetailsDto updateSalaryDetails(Long id, SalaryDetailsDto salaryDetailsDto) {
        Optional<SalaryDetails> salaryDetailsOpt = salaryDetailsRepository.findById(id);
        if (salaryDetailsOpt.isEmpty()) {
            throw new RuntimeException("Salary details not found with ID: " + id);
        }

        SalaryDetails salaryDetails = salaryDetailsOpt.get();
        salaryDetails.setLastSalaryRevisedDate(salaryDetailsDto.getLastSalaryRevisedDate());
        salaryDetails.setCurrentWithdrawSalary(salaryDetailsDto.getCurrentWithdrawSalary());
        salaryDetails.setUpdatedBy(salaryDetailsDto.getUpdatedBy());

        SalaryDetails updatedSalaryDetails = salaryDetailsRepository.save(salaryDetails);
        return convertToDto(updatedSalaryDetails);
    }

    /**
     * Get salary details by employee ID
     */
    public SalaryDetailsDto getSalaryDetailsByEmployeeId(Long employeeId) {
        Optional<SalaryDetails> salaryDetailsOpt = salaryDetailsRepository.findByEmployeeId(employeeId);
        if (salaryDetailsOpt.isEmpty()) {
            throw new RuntimeException("Salary details not found for employee ID: " + employeeId);
        }
        return convertToDto(salaryDetailsOpt.get());
    }

    /**
     * Get salary details by ID
     */
    public SalaryDetailsDto getSalaryDetailsById(Long id) {
        Optional<SalaryDetails> salaryDetailsOpt = salaryDetailsRepository.findById(id);
        if (salaryDetailsOpt.isEmpty()) {
            throw new RuntimeException("Salary details not found with ID: " + id);
        }
        return convertToDto(salaryDetailsOpt.get());
    }

    /**
     * Get all salary details
     */
    public List<SalaryDetailsDto> getAllSalaryDetails() {
        List<SalaryDetails> salaryDetailsList = salaryDetailsRepository.findAllByOrderByUpdatedAtDesc();
        return salaryDetailsList.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Get salary details by created by
     */
    public List<SalaryDetailsDto> getSalaryDetailsByCreatedBy(String createdBy) {
        List<SalaryDetails> salaryDetailsList = salaryDetailsRepository.findByCreatedByOrderByUpdatedAtDesc(createdBy);
        return salaryDetailsList.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Delete salary details by ID
     */
    public boolean deleteSalaryDetails(Long id) {
        if (salaryDetailsRepository.existsById(id)) {
            salaryDetailsRepository.deleteById(id);
            return true;
        }
        return false;
    }

    /**
     * Delete salary details by employee ID
     */
    public boolean deleteSalaryDetailsByEmployeeId(Long employeeId) {
        if (salaryDetailsRepository.existsByEmployeeId(employeeId)) {
            salaryDetailsRepository.deleteByEmployeeId(employeeId);
            return true;
        }
        return false;
    }

    /**
     * Check if salary details exist for an employee
     */
    public boolean existsByEmployeeId(Long employeeId) {
        return salaryDetailsRepository.existsByEmployeeId(employeeId);
    }

    /**
     * Convert SalaryDetails entity to DTO
     */
    private SalaryDetailsDto convertToDto(SalaryDetails salaryDetails) {
        SalaryDetailsDto dto = new SalaryDetailsDto();
        dto.setId(salaryDetails.getId());
        dto.setLastSalaryRevisedDate(salaryDetails.getLastSalaryRevisedDate());
        dto.setCurrentWithdrawSalary(salaryDetails.getCurrentWithdrawSalary());
        dto.setEmployeeId(salaryDetails.getEmployee().getId());
        dto.setCreatedBy(salaryDetails.getCreatedBy());
        dto.setUpdatedBy(salaryDetails.getUpdatedBy());
        dto.setCreatedAt(salaryDetails.getCreatedAt());
        dto.setUpdatedAt(salaryDetails.getUpdatedAt());
        return dto;
    }
}
