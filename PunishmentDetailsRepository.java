package digitization.digitization.repository;

import digitization.digitization.module.PunishmentDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface PunishmentDetailsRepository extends JpaRepository<PunishmentDetails, Long> {
    Optional<PunishmentDetails> findById(Long id);
    List<PunishmentDetails> findByPunishmenttype(String punishmenttype);
    List<PunishmentDetails> findByDate(LocalDate date);
    List<PunishmentDetails> findByDateBetween(LocalDate startDate, LocalDate endDate);

    @Query("SELECT p FROM PunishmentDetails p WHERE p.casedetails LIKE %:keyword%")
    List<PunishmentDetails> findByCaseDetailsContaining(@Param("keyword") String keyword);

    List<PunishmentDetails> findByOrderByDateDesc();
    List<PunishmentDetails> findByPunishmenttypeOrderByDateDesc(String punishmenttype);
    List<PunishmentDetails> findByEmployeeId(Long employeeId);
}
