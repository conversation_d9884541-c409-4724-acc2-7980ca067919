package digitization.digitization.converter.service;

import digitization.digitization.dto.ServiceHistoryDto;
import digitization.digitization.module.ServiceHistory;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class ServicetoDto implements Converter<ServiceHistory, ServiceHistoryDto> {
    @Override
    public ServiceHistoryDto convert(ServiceHistory serviceHistory) {
        ServiceHistoryDto serv=new ServiceHistoryDto();
        serv.setId(serviceHistory.getId());
        serv.setDate(serviceHistory.getDate());
        serv.setStatus(serviceHistory.getStatus());
        serv.setType(serviceHistory.getType());
        serv.setAppointmenttype(serviceHistory.getAppointmenttype());
        serv.setModeofappointment(serviceHistory.getModeofappointment());
        serv.setDateofappointment(serviceHistory.getDateofappointment());
        serv.setProceedingorderdate(serviceHistory.getProceedingorderdate());
        serv.setJoiningdate(serviceHistory.getJoiningdate());
        serv.setPromoteddate(serviceHistory.getPromoteddate());
        serv.setFromdesignation(serviceHistory.getFromdesignation());
        serv.setTopromoted(serviceHistory.getTopromoted());
        serv.setFromdate(serviceHistory.getFromdate());
        serv.setTodate(serviceHistory.getTodate());
        serv.setTypeofincrement(serviceHistory.getTypeofincrement());
        serv.setFromplace(serviceHistory.getFromplace());
        serv.setToplace(serviceHistory.getToplace());
        serv.setDesignation(serviceHistory.getDesignation());
        serv.setOriginaldesignation(serviceHistory.getOriginaldesignation());
        serv.setParentdepartment(serviceHistory.getParentdepartment());
        serv.setPunishmenttype(serviceHistory.getPunishmenttype());
        serv.setCasedetails(serviceHistory.getCasedetails());
        serv.setCaseDate(serviceHistory.getCaseDate());
        serv.setCaseNumber(serviceHistory.getCaseNumber());
        serv.setDescription(serviceHistory.getDescription());
        serv.setInvolvedPersons(serviceHistory.getInvolvedPersons());
        serv.setPresentStatus(serviceHistory.getPresentStatus());
        serv.setPunishmentdate(serviceHistory.getPunishmentdate());
        serv.setWithholdingFromDate(serviceHistory.getWithholdingFromDate());
        serv.setWithholdingToDate(serviceHistory.getWithholdingToDate());
        serv.setHasCaseDetails(serviceHistory.getHasCaseDetails());
        serv.setBasicPay(serviceHistory.getBasicPay());
        serv.setDa(serviceHistory.getDa());
        serv.setBasicPlusDA(serviceHistory.getBasicPlusDA());
        serv.setProceedingorderno(serviceHistory.getProceedingorderno());
        return serv;
    }
}
