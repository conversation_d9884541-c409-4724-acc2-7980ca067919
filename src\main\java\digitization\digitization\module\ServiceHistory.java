package digitization.digitization.module;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;

@Entity
@Table(name="service_history")
public class ServiceHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

//    @NotNull(message = "Date is required")
    private LocalDate date;

//    @NotBlank(message = "Type is required")
    private String type;

//    @NotBlank(message = "Status is required")
    private String status;

    private String appointmenttype;

    private String modeofappointment;

    private LocalDate dateofappointment;

    private String proceedingorderno;

    private LocalDate proceedingorderdate;

    private LocalDate joiningdate;

    private LocalDate promoteddate;

    private String fromdesignation;

    private String topromoted;

    private LocalDate fromdate;

    private LocalDate todate;

    private String typeofincrement;

    private String fromplace;

    private String toplace;

    private String designation;

    private String originaldesignation;

    private String parentdepartment;

    private String punishmenttype;

    private String casedetails;

    private Date caseDate;
    private String caseNumber;
    private String description;

    @Column(name = "involved_persons")
    private String involvedPersons;

    private String presentStatus;

    private LocalDate punishmentdate;
    private LocalDate withholdingFromDate;
    private LocalDate withholdingToDate;
    private String hasCaseDetails;

    private Double basicPay;

    private Double da;

    private Double basicPlusDA;

    // New fields added
    private LocalDate probationDateOfJoining;

    private LocalDate probationEndDate;

    private String fromOrganization;

    private String fromDesignationForeign;

    private String toDesignationForeign;

    private String fromDepartment;

    private String toDepartment;

    @ManyToOne
    @JoinColumn(name = "employee_id")
    private Employee employee;

    public ServiceHistory() {
    }

    public ServiceHistory(Long id, LocalDate date, String type, String status, String appointmenttype, String modeofappointment, LocalDate dateofappointment, String proceedingorderno, LocalDate proceedingorderdate, LocalDate joiningdate, LocalDate promoteddate, String fromdesignation, String topromoted, LocalDate fromdate, LocalDate todate, String typeofincrement, String fromplace, String toplace, String designation, String originaldesignation, String parentdepartment, String punishmenttype, String casedetails, Date caseDate, String caseNumber, String description, String involvedPersons, String presentStatus, LocalDate punishmentdate, LocalDate withholdingFromDate, LocalDate withholdingToDate, String hasCaseDetails, Double basicPay, Double da, Double basicPlusDA, LocalDate probationDateOfJoining, LocalDate probationEndDate, String fromOrganization, String fromDesignationForeign, String toDesignationForeign, String fromDepartment, String toDepartment, Employee employee) {
        this.id = id;
        this.date = date;
        this.type = type;
        this.status = status;
        this.appointmenttype = appointmenttype;
        this.modeofappointment = modeofappointment;
        this.dateofappointment = dateofappointment;
        this.proceedingorderno = proceedingorderno;
        this.proceedingorderdate = proceedingorderdate;
        this.joiningdate = joiningdate;
        this.promoteddate = promoteddate;
        this.fromdesignation = fromdesignation;
        this.topromoted = topromoted;
        this.fromdate = fromdate;
        this.todate = todate;
        this.typeofincrement = typeofincrement;
        this.fromplace = fromplace;
        this.toplace = toplace;
        this.designation = designation;
        this.originaldesignation = originaldesignation;
        this.parentdepartment = parentdepartment;
        this.punishmenttype = punishmenttype;
        this.casedetails = casedetails;
        this.caseDate = caseDate;
        this.caseNumber = caseNumber;
        this.description = description;
        this.involvedPersons = involvedPersons;
        this.presentStatus = presentStatus;
        this.punishmentdate = punishmentdate;
        this.withholdingFromDate = withholdingFromDate;
        this.withholdingToDate = withholdingToDate;
        this.hasCaseDetails = hasCaseDetails;
        this.basicPay = basicPay;
        this.da = da;
        this.basicPlusDA = basicPlusDA;
        this.probationDateOfJoining = probationDateOfJoining;
        this.probationEndDate = probationEndDate;
        this.fromOrganization = fromOrganization;
        this.fromDesignationForeign = fromDesignationForeign;
        this.toDesignationForeign = toDesignationForeign;
        this.fromDepartment = fromDepartment;
        this.toDepartment = toDepartment;
        this.employee = employee;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAppointmenttype() {
        return appointmenttype;
    }

    public void setAppointmenttype(String appointmenttype) {
        this.appointmenttype = appointmenttype;
    }

    public String getModeofappointment() {
        return modeofappointment;
    }

    public void setModeofappointment(String modeofappointment) {
        this.modeofappointment = modeofappointment;
    }

    public LocalDate getDateofappointment() {
        return dateofappointment;
    }

    public void setDateofappointment(LocalDate dateofappointment) {
        this.dateofappointment = dateofappointment;
    }

    public LocalDate getProceedingorderdate() {
        return proceedingorderdate;
    }

    public void setProceedingorderdate(LocalDate proceedingorderdate) {
        this.proceedingorderdate = proceedingorderdate;
    }

    public LocalDate getJoiningdate() {
        return joiningdate;
    }

    public void setJoiningdate(LocalDate joiningdate) {
        this.joiningdate = joiningdate;
    }

    public LocalDate getPromoteddate() {
        return promoteddate;
    }

    public void setPromoteddate(LocalDate promoteddate) {
        this.promoteddate = promoteddate;
    }

    public String getFromdesignation() {
        return fromdesignation;
    }

    public void setFromdesignation(String fromdesignation) {
        this.fromdesignation = fromdesignation;
    }

    public String getTopromoted() {
        return topromoted;
    }

    public void setTopromoted(String topromoted) {
        this.topromoted = topromoted;
    }

    public LocalDate getFromdate() {
        return fromdate;
    }

    public void setFromdate(LocalDate fromdate) {
        this.fromdate = fromdate;
    }

    public LocalDate getTodate() {
        return todate;
    }

    public void setTodate(LocalDate todate) {
        this.todate = todate;
    }

    public String getTypeofincrement() {
        return typeofincrement;
    }

    public void setTypeofincrement(String typeofincrement) {
        this.typeofincrement = typeofincrement;
    }

    public String getFromplace() {
        return fromplace;
    }

    public void setFromplace(String fromplace) {
        this.fromplace = fromplace;
    }

    public String getToplace() {
        return toplace;
    }

    public void setToplace(String toplace) {
        this.toplace = toplace;
    }

    public String getDesignation() {
        return designation;
    }

    public void setDesignation(String designation) {
        this.designation = designation;
    }

    public String getOriginaldesignation() {
        return originaldesignation;
    }

    public void setOriginaldesignation(String originaldesignation) {
        this.originaldesignation = originaldesignation;
    }

    public String getParentdepartment() {
        return parentdepartment;
    }

    public void setParentdepartment(String parentdepartment) {
        this.parentdepartment = parentdepartment;
    }

    public Employee getEmployee() {
        return employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }

    public String getProceedingorderno() {
        return proceedingorderno;
    }

    public void setProceedingorderno(String proceedingorderno) {
        this.proceedingorderno = proceedingorderno;
    }

    public String getPunishmenttype() {
        return punishmenttype;
    }

    public void setPunishmenttype(String punishmenttype) {
        this.punishmenttype = punishmenttype;
    }

    public String getCasedetails() {
        return casedetails;
    }

    public void setCasedetails(String casedetails) {
        this.casedetails = casedetails;
    }

    public Date getCaseDate() {
        return caseDate;
    }

    public void setCaseDate(Date caseDate) {
        this.caseDate = caseDate;
    }

    public String getCaseNumber() {
        return caseNumber;
    }

    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("personsInvolved")
    public Long[] getPersonsInvolved() {
        if (involvedPersons == null || involvedPersons.trim().isEmpty()) {
            return new Long[0];
        }
        return convertStringToLongArray(involvedPersons);
    }

    @JsonProperty("personsInvolved")
    public void setPersonsInvolved(Long[] personsInvolved) {
        if (personsInvolved == null || personsInvolved.length == 0) {
            this.involvedPersons = null;
        } else {
            this.involvedPersons = convertLongArrayToString(personsInvolved);
        }
        System.out.println("DEBUG: setPersonsInvolved() called with: " + Arrays.toString(personsInvolved) + " -> stored as: '" + this.involvedPersons + "'");
    }

    public String getInvolvedPersons() {
        return involvedPersons;
    }

    public void setInvolvedPersons(String involvedPersons) {
        this.involvedPersons = involvedPersons;
    }

    // Helper methods to convert between Long[] and String
    private Long[] convertStringToLongArray(String str) {
        if (str == null || str.trim().isEmpty()) {
            return new Long[0];
        }
        try {
            String[] parts = str.split(",");
            Long[] result = new Long[parts.length];
            for (int i = 0; i < parts.length; i++) {
                String part = parts[i].trim();
                if (!part.isEmpty()) {
                    result[i] = Long.parseLong(part);
                }
            }
            return result;
        } catch (NumberFormatException e) {
            System.out.println("Error converting string to Long array: '" + str + "', error: " + e.getMessage());
            return new Long[0];
        }
    }

    private String convertLongArrayToString(Long[] array) {
        if (array == null || array.length == 0) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < array.length; i++) {
            if (array[i] != null) {
                sb.append(array[i]);
                if (i < array.length - 1) {
                    sb.append(",");
                }
            }
        }
        return sb.toString();
    }

    public String getPresentStatus() {
        return presentStatus;
    }

    public void setPresentStatus(String presentStatus) {
        this.presentStatus = presentStatus;
    }

    public LocalDate getPunishmentdate() {
        return punishmentdate;
    }

    public void setPunishmentdate(LocalDate punishmentdate) {
        this.punishmentdate = punishmentdate;
    }

    public LocalDate getWithholdingFromDate() {
        return withholdingFromDate;
    }

    public void setWithholdingFromDate(LocalDate withholdingFromDate) {
        this.withholdingFromDate = withholdingFromDate;
    }

    public LocalDate getWithholdingToDate() {
        return withholdingToDate;
    }

    public void setWithholdingToDate(LocalDate withholdingToDate) {
        this.withholdingToDate = withholdingToDate;
    }

    public String getHasCaseDetails() {
        return hasCaseDetails;
    }

    public void setHasCaseDetails(String hasCaseDetails) {
        this.hasCaseDetails = hasCaseDetails;
    }

    public Double getBasicPay() {
        return basicPay;
    }

    public void setBasicPay(Double basicPay) {
        this.basicPay = basicPay;
    }

    public Double getDa() {
        return da;
    }

    public void setDa(Double da) {
        this.da = da;
    }

    public Double getBasicPlusDA() {
        return basicPlusDA;
    }

    public void setBasicPlusDA(Double basicPlusDA) {
        this.basicPlusDA = basicPlusDA;
    }

    public LocalDate getProbationDateOfJoining() {
        return probationDateOfJoining;
    }

    public void setProbationDateOfJoining(LocalDate probationDateOfJoining) {
        this.probationDateOfJoining = probationDateOfJoining;
    }

    public LocalDate getProbationEndDate() {
        return probationEndDate;
    }

    public void setProbationEndDate(LocalDate probationEndDate) {
        this.probationEndDate = probationEndDate;
    }

    public String getFromOrganization() {
        return fromOrganization;
    }

    public void setFromOrganization(String fromOrganization) {
        this.fromOrganization = fromOrganization;
    }

    public String getFromDesignationForeign() {
        return fromDesignationForeign;
    }

    public void setFromDesignationForeign(String fromDesignationForeign) {
        this.fromDesignationForeign = fromDesignationForeign;
    }

    public String getToDesignationForeign() {
        return toDesignationForeign;
    }

    public void setToDesignationForeign(String toDesignationForeign) {
        this.toDesignationForeign = toDesignationForeign;
    }

    public String getFromDepartment() {
        return fromDepartment;
    }

    public void setFromDepartment(String fromDepartment) {
        this.fromDepartment = fromDepartment;
    }

    public String getToDepartment() {
        return toDepartment;
    }

    public void setToDepartment(String toDepartment) {
        this.toDepartment = toDepartment;
    }

    @Override
    public String toString() {
        return "ServiceHistory{" +
                "id=" + id +
                ", date=" + date +
                ", type='" + type + '\'' +
                ", status='" + status + '\'' +
                ", appointmenttype='" + appointmenttype + '\'' +
                ", modeofappointment='" + modeofappointment + '\'' +
                ", dateofappointment=" + dateofappointment +
                ", proceedingorderno='" + proceedingorderno + '\'' +
                ", proceedingorderdate=" + proceedingorderdate +
                ", joiningdate=" + joiningdate +
                ", promoteddate=" + promoteddate +
                ", fromdesignation='" + fromdesignation + '\'' +
                ", topromoted='" + topromoted + '\'' +
                ", fromdate=" + fromdate +
                ", todate=" + todate +
                ", typeofincrement='" + typeofincrement + '\'' +
                ", fromplace='" + fromplace + '\'' +
                ", toplace='" + toplace + '\'' +
                ", designation='" + designation + '\'' +
                ", originaldesignation='" + originaldesignation + '\'' +
                ", parentdepartment='" + parentdepartment + '\'' +
                ", punishmenttype='" + punishmenttype + '\'' +
                ", casedetails='" + casedetails + '\'' +
                ", caseDate=" + caseDate +
                ", caseNumber='" + caseNumber + '\'' +
                ", description='" + description + '\'' +
                ", involvedPersons='" + involvedPersons + '\'' +
                ", presentStatus='" + presentStatus + '\'' +
                ", punishmentdate=" + punishmentdate +
                ", withholdingFromDate=" + withholdingFromDate +
                ", withholdingToDate=" + withholdingToDate +
                ", hasCaseDetails='" + hasCaseDetails + '\'' +
                ", basicPay=" + basicPay +
                ", da=" + da +
                ", basicPlusDA=" + basicPlusDA +
                '}';
    }
}
