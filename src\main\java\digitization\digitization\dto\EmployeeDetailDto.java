package digitization.digitization.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public class EmployeeDetailDto {

    // Employee basic details
    private Long id;
    private String ecpfNumber;
    private String panNumber;
    private String employeeName;
    private String email;
    private String mobileNumber;
    private String registerNumber;
    private String section;
    private String presentAddress;
    private String permanentAddress;
    private String fatherName;
    private String motherName;
    private LocalDate dateOfBirth;
    private String religion;
    private String community;
    private String caste;
    private String personalIdentificationmark1;
    private String personalIdentificationmark2;
    private String currentDesignation;
    private LocalDate dateOfEntry;
    private String district;
    private String nativePlaceAndTaluk;
    private String mainEmployeeType;
    private String seasonalCategory;
    private String loadManCategory;
    private String profilePhoto;
    private String createdBy;

    // New fields added
    private String spouseName;
    private LocalDate supernumericDateOfJoining;
    private String supernumericRemarks;
    private String othersRemarks;
    private String maritalStatus;

    // Employee timestamp fields
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    private String remarks;

    private String approvedBy;

    private String rejectedBy;

    // Employee status fields
    private String status;
    private LocalDateTime statusCreatedAt;
    private LocalDateTime statusUpdatedAt;
    private String statusApprovedBy;
    private String statusRejectedBy;
    private String statusRemarks;

    // Related data
    private ProfileDto profile;
    private AccountDetailsDto accountDetails;
    private List<ServiceHistoryDto> serviceHistory;
    private List<LeaveDto> leaveBalances;
    private List<EducationQualificationDto> educationQualifications;
    private List<PunishmentDetailsDto> punishmentDetails;
    private List<TrainingDetailsDto> trainingDetails;
    private List<NomineeDto> nominees;
    private List<DocumentDto> documents;
    private SalaryDetailsDto salaryDetails;

    public EmployeeDetailDto() {
    }

    // Getters and setters


    public EmployeeDetailDto(Long id, String ecpfNumber, String panNumber, String employeeName, String email, String mobileNumber, String registerNumber, String section, String presentAddress, String permanentAddress, String fatherName, String motherName, LocalDate dateOfBirth, String religion, String community, String caste, String personalIdentificationmark1, String personalIdentificationmark2, String currentDesignation, LocalDate dateOfEntry, String district, String nativePlaceAndTaluk, String mainEmployeeType, String seasonalCategory, String loadManCategory, String profilePhoto, String createdBy, LocalDateTime createdAt, LocalDateTime updatedAt, String remarks, String approvedBy, String rejectedBy, String status, LocalDateTime statusCreatedAt, LocalDateTime statusUpdatedAt, String statusApprovedBy, String statusRejectedBy, String statusRemarks, ProfileDto profile, AccountDetailsDto accountDetails, List<ServiceHistoryDto> serviceHistory, List<LeaveDto> leaveBalances, List<EducationQualificationDto> educationQualifications, List<PunishmentDetailsDto> punishmentDetails, List<TrainingDetailsDto> trainingDetails, List<NomineeDto> nominees, List<DocumentDto> documents, SalaryDetailsDto salaryDetails) {
        this.id = id;
        this.ecpfNumber = ecpfNumber;
        this.panNumber = panNumber;
        this.employeeName = employeeName;
        this.email = email;
        this.mobileNumber = mobileNumber;
        this.registerNumber = registerNumber;
        this.section = section;
        this.presentAddress = presentAddress;
        this.permanentAddress = permanentAddress;
        this.fatherName = fatherName;
        this.motherName = motherName;
        this.dateOfBirth = dateOfBirth;
        this.religion = religion;
        this.community = community;
        this.caste = caste;
        this.personalIdentificationmark1 = personalIdentificationmark1;
        this.personalIdentificationmark2 = personalIdentificationmark2;
        this.currentDesignation = currentDesignation;
        this.dateOfEntry = dateOfEntry;
        this.district = district;
        this.nativePlaceAndTaluk = nativePlaceAndTaluk;
        this.mainEmployeeType = mainEmployeeType;
        this.seasonalCategory = seasonalCategory;
        this.loadManCategory = loadManCategory;
        this.profilePhoto = profilePhoto;
        this.createdBy = createdBy;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.remarks = remarks;
        this.approvedBy = approvedBy;
        this.rejectedBy = rejectedBy;
        this.status = status;
        this.statusCreatedAt = statusCreatedAt;
        this.statusUpdatedAt = statusUpdatedAt;
        this.statusApprovedBy = statusApprovedBy;
        this.statusRejectedBy = statusRejectedBy;
        this.statusRemarks = statusRemarks;
        this.profile = profile;
        this.accountDetails = accountDetails;
        this.serviceHistory = serviceHistory;
        this.leaveBalances = leaveBalances;
        this.educationQualifications = educationQualifications;
        this.punishmentDetails = punishmentDetails;
        this.trainingDetails = trainingDetails;
        this.nominees = nominees;
        this.documents = documents;
        this.salaryDetails = salaryDetails;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEcpfNumber() {
        return ecpfNumber;
    }

    public void setEcpfNumber(String ecpfNumber) {
        this.ecpfNumber = ecpfNumber;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getRegisterNumber() {
        return registerNumber;
    }

    public void setRegisterNumber(String registerNumber) {
        this.registerNumber = registerNumber;
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getPresentAddress() {
        return presentAddress;
    }

    public void setPresentAddress(String presentAddress) {
        this.presentAddress = presentAddress;
    }

    public String getPermanentAddress() {
        return permanentAddress;
    }

    public void setPermanentAddress(String permanentAddress) {
        this.permanentAddress = permanentAddress;
    }



    public String getFatherName() {
        return fatherName;
    }

    public void setFatherName(String fatherName) {
        this.fatherName = fatherName;
    }

    public String getMotherName() {
        return motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getReligion() {
        return religion;
    }

    public void setReligion(String religion) {
        this.religion = religion;
    }

    public String getCommunity() {
        return community;
    }

    public void setCommunity(String community) {
        this.community = community;
    }

    public String getCaste() {
        return caste;
    }

    public void setCaste(String caste) {
        this.caste = caste;
    }

    public String getPersonalIdentificationmark1() {
        return personalIdentificationmark1;
    }

    public void setPersonalIdentificationmark1(String personalIdentificationmark1) {
        this.personalIdentificationmark1 = personalIdentificationmark1;
    }

    public String getPersonalIdentificationmark2() {
        return personalIdentificationmark2;
    }

    public void setPersonalIdentificationmark2(String personalIdentificationmark2) {
        this.personalIdentificationmark2 = personalIdentificationmark2;
    }

    public String getCurrentDesignation() {
        return currentDesignation;
    }

    public void setCurrentDesignation(String currentDesignation) {
        this.currentDesignation = currentDesignation;
    }

    public LocalDate getDateOfEntry() {
        return dateOfEntry;
    }

    public void setDateOfEntry(LocalDate dateOfEntry) {
        this.dateOfEntry = dateOfEntry;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getNativePlaceAndTaluk() {
        return nativePlaceAndTaluk;
    }

    public void setNativePlaceAndTaluk(String nativePlaceAndTaluk) {
        this.nativePlaceAndTaluk = nativePlaceAndTaluk;
    }

    public String getMainEmployeeType() {
        return mainEmployeeType;
    }

    public void setMainEmployeeType(String mainEmployeeType) {
        this.mainEmployeeType = mainEmployeeType;
    }

    public String getProfilePhoto() {
        return profilePhoto;
    }

    public void setProfilePhoto(String profilePhoto) {
        this.profilePhoto = profilePhoto;
    }


    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public String getRejectedBy() {
        return rejectedBy;
    }

    public void setRejectedBy(String rejectedBy) {
        this.rejectedBy = rejectedBy;
    }

    public ProfileDto getProfile() {
        return profile;
    }

    public void setProfile(ProfileDto profile) {
        this.profile = profile;
    }

    public AccountDetailsDto getAccountDetails() {
        return accountDetails;
    }

    public void setAccountDetails(AccountDetailsDto accountDetails) {
        this.accountDetails = accountDetails;
    }

    public List<ServiceHistoryDto> getServiceHistory() {
        return serviceHistory;
    }

    public void setServiceHistory(List<ServiceHistoryDto> serviceHistory) {
        this.serviceHistory = serviceHistory;
    }

    public List<LeaveDto> getLeaveBalances() {
        return leaveBalances;
    }

    public void setLeaveBalances(List<LeaveDto> leaveBalances) {
        this.leaveBalances = leaveBalances;
    }

    public List<EducationQualificationDto> getEducationQualifications() {
        return educationQualifications;
    }

    public void setEducationQualifications(List<EducationQualificationDto> educationQualifications) {
        this.educationQualifications = educationQualifications;
    }

    public List<PunishmentDetailsDto> getPunishmentDetails() {
        return punishmentDetails;
    }

    public void setPunishmentDetails(List<PunishmentDetailsDto> punishmentDetails) {
        this.punishmentDetails = punishmentDetails;
    }

    public List<TrainingDetailsDto> getTrainingDetails() {
        return trainingDetails;
    }

    public void setTrainingDetails(List<TrainingDetailsDto> trainingDetails) {
        this.trainingDetails = trainingDetails;
    }

    public List<NomineeDto> getNominees() {
        return nominees;
    }

    public void setNominees(List<NomineeDto> nominees) {
        this.nominees = nominees;
    }

    public List<DocumentDto> getDocuments() {
        return documents;
    }

    public void setDocuments(List<DocumentDto> documents) {
        this.documents = documents;
    }

    public SalaryDetailsDto getSalaryDetails() {
        return salaryDetails;
    }

    public void setSalaryDetails(SalaryDetailsDto salaryDetails) {
        this.salaryDetails = salaryDetails;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getStatusCreatedAt() {
        return statusCreatedAt;
    }

    public void setStatusCreatedAt(LocalDateTime statusCreatedAt) {
        this.statusCreatedAt = statusCreatedAt;
    }

    public LocalDateTime getStatusUpdatedAt() {
        return statusUpdatedAt;
    }

    public void setStatusUpdatedAt(LocalDateTime statusUpdatedAt) {
        this.statusUpdatedAt = statusUpdatedAt;
    }

    public String getStatusApprovedBy() {
        return statusApprovedBy;
    }

    public void setStatusApprovedBy(String statusApprovedBy) {
        this.statusApprovedBy = statusApprovedBy;
    }

    public String getStatusRejectedBy() {
        return statusRejectedBy;
    }

    public void setStatusRejectedBy(String statusRejectedBy) {
        this.statusRejectedBy = statusRejectedBy;
    }

    public String getStatusRemarks() {
        return statusRemarks;
    }

    public void setStatusRemarks(String statusRemarks) {
        this.statusRemarks = statusRemarks;
    }

    public String getSeasonalCategory() {
        return seasonalCategory;
    }

    public void setSeasonalCategory(String seasonalCategory) {
        this.seasonalCategory = seasonalCategory;
    }

    public String getLoadManCategory() {
        return loadManCategory;
    }

    public void setLoadManCategory(String loadManCategory) {
        this.loadManCategory = loadManCategory;
    }

    public String getSpouseName() {
        return spouseName;
    }

    public void setSpouseName(String spouseName) {
        this.spouseName = spouseName;
    }

    public LocalDate getSupernumericDateOfJoining() {
        return supernumericDateOfJoining;
    }

    public void setSupernumericDateOfJoining(LocalDate supernumericDateOfJoining) {
        this.supernumericDateOfJoining = supernumericDateOfJoining;
    }

    public String getSupernumericRemarks() {
        return supernumericRemarks;
    }

    public void setSupernumericRemarks(String supernumericRemarks) {
        this.supernumericRemarks = supernumericRemarks;
    }

    public String getOthersRemarks() {
        return othersRemarks;
    }

    public void setOthersRemarks(String othersRemarks) {
        this.othersRemarks = othersRemarks;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }
}
