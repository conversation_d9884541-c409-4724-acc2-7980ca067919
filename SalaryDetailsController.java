package digitization.digitization.controller;

import digitization.digitization.dto.SalaryDetailsDto;
import digitization.digitization.services.SalaryDetailsService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/salary-details")
@CrossOrigin(origins = "*")
public class SalaryDetailsController {

    @Autowired
    private SalaryDetailsService salaryDetailsService;

    /**
     * Create salary details for an employee
     */
    @PostMapping("/create")
    public ResponseEntity<?> createSalaryDetails(@Valid @RequestBody SalaryDetailsDto salaryDetailsDto) {
        try {
            SalaryDetailsDto createdSalaryDetails = salaryDetailsService.createSalaryDetails(salaryDetailsDto);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdSalaryDetails);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to create salary details");
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
        }
    }

    /**
     * Update salary details
     */
    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateSalaryDetails(@PathVariable Long id, @Valid @RequestBody SalaryDetailsDto salaryDetailsDto) {
        try {
            SalaryDetailsDto updatedSalaryDetails = salaryDetailsService.updateSalaryDetails(id, salaryDetailsDto);
            return ResponseEntity.ok(updatedSalaryDetails);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to update salary details");
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
        }
    }

    /**
     * Get salary details by ID
     */
    @GetMapping("/get/{id}")
    public ResponseEntity<?> getSalaryDetailsById(@PathVariable Long id) {
        try {
            SalaryDetailsDto salaryDetails = salaryDetailsService.getSalaryDetailsById(id);
            return ResponseEntity.ok(salaryDetails);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Salary details not found");
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
        }
    }

    /**
     * Get salary details by employee ID
     */
    @GetMapping("/employee/{employeeId}")
    public ResponseEntity<?> getSalaryDetailsByEmployeeId(@PathVariable Long employeeId) {
        try {
            SalaryDetailsDto salaryDetails = salaryDetailsService.getSalaryDetailsByEmployeeId(employeeId);
            return ResponseEntity.ok(salaryDetails);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Salary details not found for employee");
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
        }
    }

    /**
     * Get all salary details
     */
    @GetMapping("/all")
    public ResponseEntity<List<SalaryDetailsDto>> getAllSalaryDetails() {
        try {
            List<SalaryDetailsDto> salaryDetailsList = salaryDetailsService.getAllSalaryDetails();
            return ResponseEntity.ok(salaryDetailsList);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * Get salary details by created by
     */
    @GetMapping("/createdBy/{createdBy}")
    public ResponseEntity<List<SalaryDetailsDto>> getSalaryDetailsByCreatedBy(@PathVariable String createdBy) {
        try {
            List<SalaryDetailsDto> salaryDetailsList = salaryDetailsService.getSalaryDetailsByCreatedBy(createdBy);
            return ResponseEntity.ok(salaryDetailsList);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * Delete salary details by ID
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteSalaryDetails(@PathVariable Long id) {
        try {
            boolean deleted = salaryDetailsService.deleteSalaryDetails(id);
            if (deleted) {
                Map<String, String> response = new HashMap<>();
                response.put("message", "Salary details deleted successfully");
                return ResponseEntity.ok(response);
            } else {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "Salary details not found");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to delete salary details");
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Delete salary details by employee ID
     */
    @DeleteMapping("/employee/{employeeId}")
    public ResponseEntity<?> deleteSalaryDetailsByEmployeeId(@PathVariable Long employeeId) {
        try {
            boolean deleted = salaryDetailsService.deleteSalaryDetailsByEmployeeId(employeeId);
            if (deleted) {
                Map<String, String> response = new HashMap<>();
                response.put("message", "Salary details deleted successfully for employee");
                return ResponseEntity.ok(response);
            } else {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "Salary details not found for employee");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to delete salary details");
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Check if salary details exist for an employee
     */
    @GetMapping("/exists/employee/{employeeId}")
    public ResponseEntity<Map<String, Boolean>> checkSalaryDetailsExist(@PathVariable Long employeeId) {
        try {
            boolean exists = salaryDetailsService.existsByEmployeeId(employeeId);
            Map<String, Boolean> response = new HashMap<>();
            response.put("exists", exists);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
}
