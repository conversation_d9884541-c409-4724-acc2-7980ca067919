package digitization.digitization.controller.user;

import digitization.digitization.common.*;
import digitization.digitization.dto.BiometricData;
import digitization.digitization.dto.userDto.UserDto;
import digitization.digitization.module.User;
import digitization.digitization.response.responce.ApiResponce;
import digitization.digitization.response.responce.ResponseBean;
import digitization.digitization.response.responce.SuccessResponce;
import digitization.digitization.services.FingerprintService;
import digitization.digitization.services.userService.UsersService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.Base64;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

@RestController
@RequestMapping("user")
public class UserController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private UsersService usersService;

    @Autowired
    private FingerprintService fingerprintService;

    @PostMapping("userCreate")
    public ResponseEntity register( @RequestBody UserDto userDto, BindingResult bindingResult, HttpServletRequest request) {

        ApiResponseBean apiResponseBean = new ApiResponseBean();
        try {
//            String ip = IPFromRequest.getClientIp(request);
            if (bindingResult.hasErrors()) {
                apiResponseBean.setResponseType("VALIDATION");
                apiResponseBean.setValidationErrors(Validator.create(bindingResult));
                return ResponseEntity.status(200).body(apiResponseBean);
            }

            Boolean isUserNameExsist=usersService.isUserNameExists(userDto.getUsername());
            if(isUserNameExsist){
                apiResponseBean.setResponseType("Username Already Exists");
                return ResponseEntity.status(200).body(apiResponseBean);
            }
            Boolean isMobileExsist=usersService.isMobileExists(userDto.getMobile());
            if(isMobileExsist){
                apiResponseBean.setResponseType("MobileNumber Already Exists");
                return ResponseEntity.status(200).body(apiResponseBean);
            }
            Boolean isEmailExsist=usersService.isEmailExists(userDto.getEmail());
            if(isEmailExsist){
                apiResponseBean.setResponseType("Email Already Exists");
                return ResponseEntity.status(200).body(apiResponseBean);
            }

            User savedUserId = usersService.save(userDto);

            if (savedUserId == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Bad Request");
            }
            apiResponseBean.setResponseType("SUCCESS");
            apiResponseBean.setResponseData(savedUserId);
            return ResponseEntity.status(201).body(apiResponseBean);


        } catch (Exception e) {
            e.printStackTrace();
            logger.error(" INSERT : " + e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Bad Request");
        }
    }

    @GetMapping("list")
    public ResponseEntity<ApiResponce> getAllUsers() {
        return ResponseEntity.ok(usersService.list());
    }

    @GetMapping("listByRole")
    public ResponseEntity<ApiResponce> getAllUsersByRole() {
        return ResponseEntity.ok(usersService.listByRole());
    }


    @GetMapping("allList")
    public ApiResponce UserPagination(@RequestParam Integer pagesize, @RequestParam Integer start) {
        Pageable pageable = new Pageable(pagesize,start);
        ApiResponce userList = usersService.listOfPage(pageable);
        return userList;

    }

    @PostMapping("/extractFingerprint")
    public String extractFingerprint(@RequestBody BiometricData biometricData) {
        try {
            String xmlResponse = biometricData.getUidimg();

            // Parse the XML response
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlResponse)));

            // Extract the fingerprint data
            NodeList nodeList = document.getElementsByTagName("Data");
            if (nodeList.getLength() > 0) {
                Element dataElement = (Element) nodeList.item(0);
                String fingerprintData = dataElement.getTextContent();

                // Decode the base64 data
                byte[] decodedData = Base64.getDecoder().decode(fingerprintData);
                return "Fingerprint data extracted and decoded. Length: " + decodedData.length + " bytes.";
            } else {
                return "No fingerprint data found in the XML response.";
            }
        } catch (Exception e) {
            return "Error extracting and decoding fingerprint data: " + e.getMessage();
        }
    }


    @GetMapping("/view/{id}")
    public ApiResponce view(@PathVariable long id) {
        ApiResponce apiResponce = new ApiResponce();
        try {
            UserDto  user = usersService.viewById(id);
            if(user != null){
                return new  ApiResponce(true, user);
            }else{
                return new ApiResponce(false, "unable to get User Data");
            }
        } catch (Exception e) {
            logger.error("User VIEW : " + e.getMessage());
            throw new RuntimeException("Bad Request");

        }
    }

    @PostMapping("datatable-user")
    public DTResponseBean getDatatable(@RequestBody DTRequestBean dtRequestBean) {
        return usersService.listByDTRequest(dtRequestBean);
    }

    @PostMapping("update/{id}")
    public ResponseEntity update(@RequestBody UserDto userDto,@PathVariable long id ,BindingResult bindingResult, HttpServletRequest request) {
        try {
            ResponseBean responseBean = new ResponseBean();
            if (bindingResult.hasErrors()) {
                responseBean.setResponseType("VALIDATION");
                responseBean.setValidationErrors(Validator.create(bindingResult));
                return ResponseEntity.status(200).body(responseBean);
            }

            User user = usersService.userUpdate(userDto, id);
            if (user != null) {

                responseBean.setResponseType("SUCCESS");
                return ResponseEntity.status(201).body(responseBean);
            }
            return ResponseEntity.status(400).body("Bad Request");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
    @GetMapping("delete/{id}")
    public ResponseEntity delete(@PathVariable long id){

        try{
            SuccessResponce successResponce = usersService.deleteById(id);
            return ResponseEntity.status(201).body(successResponce);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}

