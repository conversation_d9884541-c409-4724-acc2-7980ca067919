package digitization.digitization.module;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "salary_details")
public class SalaryDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "last_salary_revised_date")
    private LocalDate lastSalaryRevisedDate;

    @Column(name = "current_withdraw_salary", precision = 10, scale = 2)
    private BigDecimal currentWithdrawSalary;

    @Column(name = "group_name")
    private String group;

    @Column(name = "payband")
    private String payband;

    @Column(name = "gradepay", precision = 10, scale = 2)
    private BigDecimal gradepay;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "employee_id", nullable = false)
    private Employee employee;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    public SalaryDetails() {
    }

    public SalaryDetails(LocalDate lastSalaryRevisedDate, BigDecimal currentWithdrawSalary, Employee employee) {
        this.lastSalaryRevisedDate = lastSalaryRevisedDate;
        this.currentWithdrawSalary = currentWithdrawSalary;
        this.employee = employee;
    }

    public SalaryDetails(LocalDate lastSalaryRevisedDate, BigDecimal currentWithdrawSalary, Employee employee, String createdBy) {
        this.lastSalaryRevisedDate = lastSalaryRevisedDate;
        this.currentWithdrawSalary = currentWithdrawSalary;
        this.employee = employee;
        this.createdBy = createdBy;
    }

    public SalaryDetails(LocalDate lastSalaryRevisedDate, BigDecimal currentWithdrawSalary, String group, String payband, Employee employee, String createdBy) {
        this.lastSalaryRevisedDate = lastSalaryRevisedDate;
        this.currentWithdrawSalary = currentWithdrawSalary;
        this.group = group;
        this.payband = payband;
        this.employee = employee;
        this.createdBy = createdBy;
    }

    public SalaryDetails(LocalDate lastSalaryRevisedDate, BigDecimal currentWithdrawSalary, String group, String payband, BigDecimal gradepay, Employee employee, String createdBy) {
        this.lastSalaryRevisedDate = lastSalaryRevisedDate;
        this.currentWithdrawSalary = currentWithdrawSalary;
        this.group = group;
        this.payband = payband;
        this.gradepay = gradepay;
        this.employee = employee;
        this.createdBy = createdBy;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getLastSalaryRevisedDate() {
        return lastSalaryRevisedDate;
    }

    public void setLastSalaryRevisedDate(LocalDate lastSalaryRevisedDate) {
        this.lastSalaryRevisedDate = lastSalaryRevisedDate;
    }

    public BigDecimal getCurrentWithdrawSalary() {
        return currentWithdrawSalary;
    }

    public void setCurrentWithdrawSalary(BigDecimal currentWithdrawSalary) {
        this.currentWithdrawSalary = currentWithdrawSalary;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getPayband() {
        return payband;
    }

    public void setPayband(String payband) {
        this.payband = payband;
    }

    public BigDecimal getGradepay() {
        return gradepay;
    }

    public void setGradepay(BigDecimal gradepay) {
        this.gradepay = gradepay;
    }

    public Employee getEmployee() {
        return employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    public String toString() {
        return "SalaryDetails{" +
                "id=" + id +
                ", lastSalaryRevisedDate=" + lastSalaryRevisedDate +
                ", currentWithdrawSalary=" + currentWithdrawSalary +
                ", group='" + group + '\'' +
                ", payband='" + payband + '\'' +
                ", gradepay=" + gradepay +
                ", createdBy='" + createdBy + '\'' +
                ", updatedBy='" + updatedBy + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
