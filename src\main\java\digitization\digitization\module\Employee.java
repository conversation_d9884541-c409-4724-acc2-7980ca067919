package digitization.digitization.module;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "employee")
public class Employee {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "Main_Employee")
    private String mainEmployeeType;

    private String seasonalCategory;
    private String loadManCategory;

    @NotBlank(message = "ECPF number is required")
//    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "ECPF number must be alphanumeric")
    private String ecpfNumber;

    private String empId;

    @NotBlank(message = "PAN number is required")
//    @Pattern(regexp = "^[A-Z]{5}[0-9]{4}[A-Z]{1}$", message = "PAN number must be valid")
    private String panNumber;

    @NotBlank(message = "Employee name is required")
    private String employeeName;

    @NotBlank(message = "Email is required")
//    @Email(message = "Email should be valid")
    private String email;

    @Column(name = "mobile_number")
    private String mobileNumber;

//    @NotBlank(message = "Register number is required")
    private String registerNumber;

//    @NotBlank(message = "Section is required")
    private String section;

    @NotBlank(message = "Father's name is required")
    private String fatherName;

    private String motherName;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    private String gender;

    //    @NotNull(message = "Date of birth is required")
//    @Past(message = "Date of birth must be in the past")
    private LocalDate dateOfBirth;

//    @NotBlank(message = "Religion is required")
    private String religion;

    private String community;

    private String caste;

    private String personalIdentificationmark1;

    private String personalIdentificationmark2;

//    @NotBlank(message = "Current designation is required")
    private String currentDesignation;

    @NotNull(message = "Date of entry is required")
//    @PastOrPresent(message = "Date of entry must be in the past or present")
    private LocalDate dateOfEntry;

    @NotBlank(message = "District is required")
    private String district;

    @NotBlank(message = "Native place and taluk is required")
    private String nativePlaceAndTaluk;

    @Column(name = "profile_photo")
    private String profilePhoto;

    @Column(name = "created_by")
    private String createdBy;

    @OneToMany(mappedBy = "employee", cascade = CascadeType.ALL)
    @JsonIgnore
    private List<Document> documents;

    @OneToMany(mappedBy = "employee", cascade = CascadeType.ALL)
    @JsonIgnore
    private List<ServiceHistory> serviceHistory;

    @OneToMany(mappedBy = "employee", cascade = CascadeType.ALL)
    @JsonIgnore
    private List<Leave> leaveBalances;

    @OneToMany(mappedBy = "employee", cascade = CascadeType.ALL)
    @JsonIgnore
    private List<SalaryDetails> salaryDetails;

    @OneToOne(mappedBy = "employee", cascade = CascadeType.ALL)
    @JsonIgnore
    private Profile profile;

    public Employee() {
    }

    public Employee(Long id, String mainEmployeeType, String seasonalCategory, String loadManCategory, String ecpfNumber, String empId, String panNumber, String employeeName, String email, String mobileNumber, String registerNumber, String section, String fatherName, String motherName, LocalDateTime createdAt, LocalDateTime updatedAt, String gender, LocalDate dateOfBirth, String religion, String community, String caste, String personalIdentificationmark1, String personalIdentificationmark2, String currentDesignation, LocalDate dateOfEntry, String district, String nativePlaceAndTaluk, String profilePhoto, String createdBy, List<Document> documents, List<ServiceHistory> serviceHistory, List<Leave> leaveBalances, List<SalaryDetails> salaryDetails, Profile profile) {
        this.id = id;
        this.mainEmployeeType = mainEmployeeType;
        this.seasonalCategory = seasonalCategory;
        this.loadManCategory = loadManCategory;
        this.ecpfNumber = ecpfNumber;
        this.empId = empId;
        this.panNumber = panNumber;
        this.employeeName = employeeName;
        this.email = email;
        this.mobileNumber = mobileNumber;
        this.registerNumber = registerNumber;
        this.section = section;
        this.fatherName = fatherName;
        this.motherName = motherName;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.gender = gender;
        this.dateOfBirth = dateOfBirth;
        this.religion = religion;
        this.community = community;
        this.caste = caste;
        this.personalIdentificationmark1 = personalIdentificationmark1;
        this.personalIdentificationmark2 = personalIdentificationmark2;
        this.currentDesignation = currentDesignation;
        this.dateOfEntry = dateOfEntry;
        this.district = district;
        this.nativePlaceAndTaluk = nativePlaceAndTaluk;
        this.profilePhoto = profilePhoto;
        this.createdBy = createdBy;
        this.documents = documents;
        this.serviceHistory = serviceHistory;
        this.leaveBalances = leaveBalances;
        this.salaryDetails = salaryDetails;
        this.profile = profile;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEcpfNumber() {
        return ecpfNumber;
    }

    public void setEcpfNumber(String ecpfNumber) {
        this.ecpfNumber = ecpfNumber;
    }

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmail() {
        return email;
    }

    public String getMainEmployeeType() {
        return mainEmployeeType;
    }

    public void setMainEmployeeType(String mainEmployeeType) {
        this.mainEmployeeType = mainEmployeeType;
    }

    public String getSeasonalCategory() {
        return seasonalCategory;
    }

    public void setSeasonalCategory(String seasonalCategory) {
        this.seasonalCategory = seasonalCategory;
    }

    public String getLoadManCategory() {
        return loadManCategory;
    }

    public void setLoadManCategory(String loadManCategory) {
        this.loadManCategory = loadManCategory;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getRegisterNumber() {
        return registerNumber;
    }

    public void setRegisterNumber(String registerNumber) {
        this.registerNumber = registerNumber;
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getFatherName() {
        return fatherName;
    }

    public void setFatherName(String fatherName) {
        this.fatherName = fatherName;
    }

    public String getMotherName() {
        return motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getReligion() {
        return religion;
    }

    public void setReligion(String religion) {
        this.religion = religion;
    }

    public String getCommunity() {
        return community;
    }

    public void setCommunity(String community) {
        this.community = community;
    }

    public String getCaste() {
        return caste;
    }

    public void setCaste(String caste) {
        this.caste = caste;
    }

    public String getPersonalIdentificationmark1() {
        return personalIdentificationmark1;
    }

    public void setPersonalIdentificationmark1(String personalIdentificationmark1) {
        this.personalIdentificationmark1 = personalIdentificationmark1;
    }

    public String getPersonalIdentificationmark2() {
        return personalIdentificationmark2;
    }

    public void setPersonalIdentificationmark2(String personalIdentificationmark2) {
        this.personalIdentificationmark2 = personalIdentificationmark2;
    }

    public String getCurrentDesignation() {
        return currentDesignation;
    }

    public void setCurrentDesignation(String currentDesignation) {
        this.currentDesignation = currentDesignation;
    }

    public LocalDate getDateOfEntry() {
        return dateOfEntry;
    }

    public void setDateOfEntry(LocalDate dateOfEntry) {
        this.dateOfEntry = dateOfEntry;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getNativePlaceAndTaluk() {
        return nativePlaceAndTaluk;
    }

    public void setNativePlaceAndTaluk(String nativePlaceAndTaluk) {
        this.nativePlaceAndTaluk = nativePlaceAndTaluk;
    }

    public String getProfilePhoto() {
        return profilePhoto;
    }

    public void setProfilePhoto(String profilePhoto) {
        this.profilePhoto = profilePhoto;
    }



    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public List<Document> getDocuments() {
        return documents;
    }

    public void setDocuments(List<Document> documents) {
        this.documents = documents;
    }

    public List<ServiceHistory> getServiceHistory() {
        return serviceHistory;
    }

    public void setServiceHistory(List<ServiceHistory> serviceHistory) {
        this.serviceHistory = serviceHistory;
    }

    public List<Leave> getLeaveBalances() {
        return leaveBalances;
    }

    public void setLeaveBalances(List<Leave> leaveBalances) {
        this.leaveBalances = leaveBalances;
    }

    public List<SalaryDetails> getSalaryDetails() {
        return salaryDetails;
    }

    public void setSalaryDetails(List<SalaryDetails> salaryDetails) {
        this.salaryDetails = salaryDetails;
    }

    public Profile getProfile() {
        return profile;
    }

    public void setProfile(Profile profile) {
        this.profile = profile;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }
}

