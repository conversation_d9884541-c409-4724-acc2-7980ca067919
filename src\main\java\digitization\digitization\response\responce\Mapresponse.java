package digitization.digitization.response.responce;

public class Mapresponse {

    private String type;
    private Object coordinates;
    private double coordinates1;
    private double coordinates2;

    private Object properties;

    public Mapresponse(String type, Object coordinates, double coordinates1, double coordinates2, Object properties) {
        this.type = type;
        this.coordinates = coordinates;
        this.coordinates1 = coordinates1;
        this.coordinates2 = coordinates2;
        this.properties = properties;
    }

    public Mapresponse(){

    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public double getCoordinates1() {
        return coordinates1;
    }

    public void setCoordinates1(double coordinates1) {
        this.coordinates1 = coordinates1;
    }

    public double getCoordinates2() {
        return coordinates2;
    }

    public void setCoordinates2(double coordinates2) {
        this.coordinates2 = coordinates2;
    }

    public Object getProperties() {
        return properties;
    }

    public void setProperties(Object properties) {
        this.properties = properties;
    }

    public Object getCoordinates() {
        return coordinates;
    }

    public void setCoordinates(Object coordinates) {
        this.coordinates = coordinates;
    }
}

