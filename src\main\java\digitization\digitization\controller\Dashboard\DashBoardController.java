package digitization.digitization.controller.Dashboard;

import digitization.digitization.dto.StatusCountDto;
import digitization.digitization.repository.DocumentRepository;
import digitization.digitization.repository.EmployeeRepo;
import digitization.digitization.repository.EmployeeStatusRepository;
import digitization.digitization.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("dashboard")
public class DashBoardController {

    @Autowired
    private DocumentRepository documentRepository;
    @Autowired
    private EmployeeRepo employeeRepository;
    @Autowired
    private EmployeeStatusRepository statusRepository;
    @Autowired
    private UserRepository userRepository;

    @GetMapping("employee-counts")
    public long getDigitizationCounts(){
        return employeeRepository.getEmployeeCount();
    }

    @GetMapping("document-counts")
    public long getDocumentCounts(){
        return documentRepository.getEmployeeCount();
    }

    @GetMapping("status-count")
    public List<StatusCountDto> getStatusCounts() {
        List<Object[]> rawData = statusRepository.getStatusCounts();
        return rawData.stream()
                .map(obj -> new StatusCountDto((String) obj[0], ((Number) obj[1]).longValue()))
                .collect(Collectors.toList());
    }

    @GetMapping("admin-counts")
    public long getAdminCount() {
        return userRepository.countAdmins();
    }

    @GetMapping("operator-counts")
    public long getOperatorCount() {
        return userRepository.countOperators();
    }
}
