package digitization.digitization.services;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.ServiceHistory;
import digitization.digitization.repository.ServiceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import digitization.digitization.repository.EmployeeRepo;
import digitization.digitization.module.Employee;

import java.util.List;

@Service
public class HistoryService {
    @Autowired
    private ServiceRepository serviceRepository;

    @Autowired
    private EmployeeRepo employeeRepository;

    public ServiceHistory addServiceHistory(Long employeeId, ServiceHistory serviceHistory) {
        System.out.println("DEBUG: addServiceHistory - involvedPersons string: " + serviceHistory.getInvolvedPersons());
        System.out.println("DEBUG: addServiceHistory - personsInvolved array: " + java.util.Arrays.toString(serviceHistory.getPersonsInvolved()));

        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + employeeId, null, null));

        serviceHistory.setEmployee(employee);
        ServiceHistory saved = serviceRepository.save(serviceHistory);

        System.out.println("DEBUG: addServiceHistory - saved involvedPersons string: " + saved.getInvolvedPersons());
        System.out.println("DEBUG: addServiceHistory - saved personsInvolved array: " + java.util.Arrays.toString(saved.getPersonsInvolved()));

        return saved;
    }

    public List<ServiceHistory> getServiceHistoryByEmployeeId(Long employeeId) {
        List<ServiceHistory> results = serviceRepository.findByEmployeeIdOrderByDateDesc(employeeId);
        System.out.println("DEBUG: getServiceHistoryByEmployeeId found " + results.size() + " records for employee " + employeeId);

        for (int i = 0; i < results.size(); i++) {
            ServiceHistory sh = results.get(i);
            System.out.println("DEBUG: ServiceHistory[" + i + "] - ID: " + sh.getId());
            System.out.println("DEBUG: ServiceHistory[" + i + "] - involvedPersons string: '" + sh.getInvolvedPersons() + "'");
            System.out.println("DEBUG: ServiceHistory[" + i + "] - personsInvolved array: " + java.util.Arrays.toString(sh.getPersonsInvolved()));
        }

        return results;
    }

    public java.util.Optional<ServiceHistory> getServiceHistoryById(Long id) {
        return serviceRepository.findById(id);
    }

    public ServiceHistory updateServiceHistory(Long id, ServiceHistory serviceHistoryDetails) {
        System.out.println("DEBUG: updateServiceHistory - incoming involvedPersons string: " + serviceHistoryDetails.getInvolvedPersons());
        System.out.println("DEBUG: updateServiceHistory - incoming personsInvolved array: " + java.util.Arrays.toString(serviceHistoryDetails.getPersonsInvolved()));

        ServiceHistory serviceHistory = serviceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Service history not found with id: " + id, null, null));

        serviceHistory.setDate(serviceHistoryDetails.getDate());
        serviceHistory.setType(serviceHistoryDetails.getType());
        serviceHistory.setStatus(serviceHistoryDetails.getStatus());
        serviceHistory.setAppointmenttype(serviceHistoryDetails.getAppointmenttype());
        serviceHistory.setModeofappointment(serviceHistoryDetails.getModeofappointment());
        serviceHistory.setDateofappointment(serviceHistoryDetails.getDateofappointment());
        serviceHistory.setProceedingorderdate(serviceHistoryDetails.getProceedingorderdate());
        serviceHistory.setProceedingorderno(serviceHistoryDetails.getProceedingorderno());
        serviceHistory.setJoiningdate(serviceHistoryDetails.getJoiningdate());
        serviceHistory.setPromoteddate(serviceHistoryDetails.getPromoteddate());
        serviceHistory.setFromdesignation(serviceHistoryDetails.getFromdesignation());
        serviceHistory.setTopromoted(serviceHistoryDetails.getTopromoted());
        serviceHistory.setFromdate(serviceHistoryDetails.getFromdate());
        serviceHistory.setTodate(serviceHistoryDetails.getTodate());
        serviceHistory.setTypeofincrement(serviceHistoryDetails.getTypeofincrement());
        serviceHistory.setFromplace(serviceHistoryDetails.getFromplace());
        serviceHistory.setToplace(serviceHistoryDetails.getToplace());
        serviceHistory.setDesignation(serviceHistoryDetails.getDesignation());
        serviceHistory.setOriginaldesignation(serviceHistoryDetails.getOriginaldesignation());
        serviceHistory.setParentdepartment(serviceHistoryDetails.getParentdepartment());
        serviceHistory.setPunishmenttype(serviceHistoryDetails.getPunishmenttype());
        serviceHistory.setCasedetails(serviceHistoryDetails.getCasedetails());
        serviceHistory.setCaseDate(serviceHistoryDetails.getCaseDate());
        serviceHistory.setCaseNumber(serviceHistoryDetails.getCaseNumber());
        serviceHistory.setDescription(serviceHistoryDetails.getDescription());

        // Handle involved persons - use the array method to ensure proper conversion
        Long[] personsInvolved = serviceHistoryDetails.getPersonsInvolved();
        if (personsInvolved != null && personsInvolved.length > 0) {
            serviceHistory.setPersonsInvolved(personsInvolved);
            System.out.println("HISTORY UPDATE - Set personsInvolved: " + java.util.Arrays.toString(personsInvolved) + " -> stored as: '" + serviceHistory.getInvolvedPersons() + "'");
        } else {
            serviceHistory.setInvolvedPersons(null);
            System.out.println("HISTORY UPDATE - No personsInvolved provided, set to null");
        }

        serviceHistory.setPresentStatus(serviceHistoryDetails.getPresentStatus());
        serviceHistory.setPunishmentdate(serviceHistoryDetails.getPunishmentdate());
        serviceHistory.setWithholdingFromDate(serviceHistoryDetails.getWithholdingFromDate());
        serviceHistory.setWithholdingToDate(serviceHistoryDetails.getWithholdingToDate());
        serviceHistory.setHasCaseDetails(serviceHistoryDetails.getHasCaseDetails());
        serviceHistory.setBasicPay(serviceHistoryDetails.getBasicPay());
        serviceHistory.setDa(serviceHistoryDetails.getDa());
        serviceHistory.setBasicPlusDA(serviceHistoryDetails.getBasicPlusDA());

        ServiceHistory saved = serviceRepository.save(serviceHistory);

        System.out.println("DEBUG: updateServiceHistory - saved involvedPersons string: " + saved.getInvolvedPersons());
        System.out.println("DEBUG: updateServiceHistory - saved personsInvolved array: " + java.util.Arrays.toString(saved.getPersonsInvolved()));

        return saved;
    }

    public void deleteServiceHistory(Long id) {
        serviceRepository.deleteById(id);
    }
}
