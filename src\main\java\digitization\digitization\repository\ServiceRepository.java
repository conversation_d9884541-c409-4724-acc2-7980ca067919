package digitization.digitization.repository;

import digitization.digitization.module.ServiceHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ServiceRepository extends JpaRepository<ServiceHistory,Long> {
    List<ServiceHistory> findByEmployeeIdOrderByDateDesc(Long employeeId);

}
