package digitization.digitization.controller;

import digitization.digitization.module.ServiceHistory;
import digitization.digitization.services.HistoryService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("service")
public class ServiceContro {
    @Autowired
    private HistoryService serviceHistoryService;

    @PostMapping("/addServ/{employeeId}")
    public ServiceHistory addServiceHistory(@PathVariable Long employeeId, @Valid @RequestBody ServiceHistory serviceHistory) {
        System.out.println("DEBUG: Controller received ServiceHistory:");
        System.out.println("DEBUG: - involvedPersons string: '" + serviceHistory.getInvolvedPersons() + "'");
        System.out.println("DEBUG: - personsInvolved array: " + java.util.Arrays.toString(serviceHistory.getPersonsInvolved()));

        ServiceHistory result = serviceHistoryService.addServiceHistory(employeeId, serviceHistory);

        System.out.println("DEBUG: Controller returning ServiceHistory:");
        System.out.println("DEBUG: - involvedPersons string: '" + result.getInvolvedPersons() + "'");
        System.out.println("DEBUG: - personsInvolved array: " + java.util.Arrays.toString(result.getPersonsInvolved()));

        return result;
    }

    @GetMapping("/employee/{employeeId}")
    public List<ServiceHistory> getServiceHistoryByEmployee(@PathVariable Long employeeId) {
        return serviceHistoryService.getServiceHistoryByEmployeeId(employeeId);
    }

    @PutMapping("/updateService/{id}")
    public ServiceHistory updateServiceHistory(@PathVariable Long id, @Valid @RequestBody ServiceHistory serviceHistoryDetails) {
        System.out.println("DEBUG: Controller received ServiceHistory for update:");
        System.out.println("DEBUG: - involvedPersons string: '" + serviceHistoryDetails.getInvolvedPersons() + "'");
        System.out.println("DEBUG: - personsInvolved array: " + java.util.Arrays.toString(serviceHistoryDetails.getPersonsInvolved()));

        ServiceHistory result = serviceHistoryService.updateServiceHistory(id, serviceHistoryDetails);

        System.out.println("DEBUG: Controller returning updated ServiceHistory:");
        System.out.println("DEBUG: - involvedPersons string: '" + result.getInvolvedPersons() + "'");
        System.out.println("DEBUG: - personsInvolved array: " + java.util.Arrays.toString(result.getPersonsInvolved()));

        return result;
    }

    @DeleteMapping("/deleteServ/{id}")
    public ResponseEntity<?> deleteServiceHistory(@PathVariable Long id) {
        serviceHistoryService.deleteServiceHistory(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/test-conversion")
    public ResponseEntity<String> testConversion() {
        try {
            // Create a test ServiceHistory
            digitization.digitization.module.ServiceHistory sh = new digitization.digitization.module.ServiceHistory();

            // Test setting array
            Long[] testArray = {25L, 10L};
            System.out.println("DEBUG: Testing setPersonsInvolved with: " + java.util.Arrays.toString(testArray));
            sh.setPersonsInvolved(testArray);

            System.out.println("DEBUG: After setting, involvedPersons string: '" + sh.getInvolvedPersons() + "'");

            // Test getting array back
            Long[] retrieved = sh.getPersonsInvolved();
            System.out.println("DEBUG: Retrieved array: " + java.util.Arrays.toString(retrieved));

            // Test setting string directly
            sh.setInvolvedPersons("123,456,789");
            Long[] fromString = sh.getPersonsInvolved();
            System.out.println("DEBUG: From string '123,456,789', got array: " + java.util.Arrays.toString(fromString));

            return ResponseEntity.ok("Test completed - check console logs");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.ok("Test failed: " + e.getMessage());
        }
    }

    @PostMapping("/test-json")
    public ResponseEntity<String> testJsonDeserialization(@RequestBody digitization.digitization.module.ServiceHistory serviceHistory) {
        try {
            System.out.println("=== JSON DESERIALIZATION TEST ===");
            System.out.println("DEBUG: - involvedPersons string: '" + serviceHistory.getInvolvedPersons() + "'");
            System.out.println("DEBUG: - personsInvolved array: " + java.util.Arrays.toString(serviceHistory.getPersonsInvolved()));

            // Test setting manually
            serviceHistory.setPersonsInvolved(new Long[]{25L, 10L});
            System.out.println("DEBUG: After manual set - involvedPersons string: '" + serviceHistory.getInvolvedPersons() + "'");
            System.out.println("DEBUG: After manual set - personsInvolved array: " + java.util.Arrays.toString(serviceHistory.getPersonsInvolved()));

            return ResponseEntity.ok("JSON test completed - check console logs. InvolvedPersons: '" +
                serviceHistory.getInvolvedPersons() + "', PersonsInvolved: " +
                java.util.Arrays.toString(serviceHistory.getPersonsInvolved()));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.ok("JSON test failed: " + e.getMessage());
        }
    }

    @PostMapping("/test-dto-json")
    public ResponseEntity<String> testDtoJsonDeserialization(@RequestBody digitization.digitization.dto.ServiceHistoryDto dto) {
        try {
            System.out.println("=== DTO JSON DESERIALIZATION TEST ===");
            System.out.println("DEBUG: - involvedPersons string: '" + dto.getInvolvedPersons() + "'");
            System.out.println("DEBUG: - personsInvolved array: " + java.util.Arrays.toString(dto.getPersonsInvolved()));
            System.out.println("DEBUG: - personsInvolvedArray field: " + java.util.Arrays.toString(dto.getPersonsInvolvedArray()));

            return ResponseEntity.ok("DTO JSON test completed - check console logs. InvolvedPersons: '" +
                dto.getInvolvedPersons() + "', PersonsInvolved: " +
                java.util.Arrays.toString(dto.getPersonsInvolved()));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.ok("DTO JSON test failed: " + e.getMessage());
        }
    }

    @GetMapping("/test-db/{id}")
    public ResponseEntity<String> testDatabaseRead(@PathVariable Long id) {
        try {
            java.util.Optional<digitization.digitization.module.ServiceHistory> optional =
                serviceHistoryService.getServiceHistoryById(id);

            if (optional.isPresent()) {
                digitization.digitization.module.ServiceHistory sh = optional.get();
                System.out.println("DEBUG: testDatabaseRead - found ServiceHistory ID: " + sh.getId());
                System.out.println("DEBUG: testDatabaseRead - involvedPersons string: '" + sh.getInvolvedPersons() + "'");
                System.out.println("DEBUG: testDatabaseRead - personsInvolved array: " + java.util.Arrays.toString(sh.getPersonsInvolved()));

                return ResponseEntity.ok("DB test completed - ID: " + sh.getId() +
                    ", InvolvedPersons: '" + sh.getInvolvedPersons() +
                    "', PersonsInvolved: " + java.util.Arrays.toString(sh.getPersonsInvolved()));
            } else {
                return ResponseEntity.ok("ServiceHistory not found with ID: " + id);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.ok("DB test failed: " + e.getMessage());
        }
    }

    @PostMapping("/test-manual-save/{employeeId}")
    public ResponseEntity<String> testManualSave(@PathVariable Long employeeId) {
        try {
            // Create a test ServiceHistory manually
            digitization.digitization.module.ServiceHistory sh = new digitization.digitization.module.ServiceHistory();
            sh.setType("Test");
            sh.setStatus("Test Status");
            sh.setDescription("Test Description");

            // Manually set the involved persons
            Long[] testArray = {25L, 10L};
            System.out.println("DEBUG: testManualSave - setting personsInvolved: " + java.util.Arrays.toString(testArray));
            sh.setPersonsInvolved(testArray);

            System.out.println("DEBUG: testManualSave - after setting, involvedPersons string: '" + sh.getInvolvedPersons() + "'");

            // Save it
            digitization.digitization.module.ServiceHistory saved = serviceHistoryService.addServiceHistory(employeeId, sh);

            System.out.println("DEBUG: testManualSave - saved with ID: " + saved.getId());
            System.out.println("DEBUG: testManualSave - saved involvedPersons string: '" + saved.getInvolvedPersons() + "'");
            System.out.println("DEBUG: testManualSave - saved personsInvolved array: " + java.util.Arrays.toString(saved.getPersonsInvolved()));

            return ResponseEntity.ok("Manual save test completed - ID: " + saved.getId() +
                ", InvolvedPersons: '" + saved.getInvolvedPersons() +
                "', PersonsInvolved: " + java.util.Arrays.toString(saved.getPersonsInvolved()));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.ok("Manual save test failed: " + e.getMessage());
        }
    }

    @PostMapping("/test-raw-json")
    public ResponseEntity<String> testRawJson(@RequestBody String rawJson) {
        try {
            System.out.println("=== RAW JSON RECEIVED ===");
            System.out.println(rawJson);
            System.out.println("=== END RAW JSON ===");

            return ResponseEntity.ok("Raw JSON logged - check console");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.ok("Raw JSON test failed: " + e.getMessage());
        }
    }
}
