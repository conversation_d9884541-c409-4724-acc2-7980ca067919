package digitization.digitization.common;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class HttpRequestExtractor {


    @Autowired
    private HttpServletRequest httpRequest;

    private  String clientIp;

    public HttpRequestExtractor(){

    }

    public String getClientIp(){
        this.clientIp =    httpRequest.getHeader("X-FORWARDED-FOR");
        if(this.clientIp == null || this.clientIp.isEmpty()){
            this.clientIp = httpRequest.getRemoteAddr();
        }
        return clientIp;
    }
}

