package digitization.digitization.repository;

import digitization.digitization.module.Document;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentRepository extends JpaRepository<Document,Long> {
    List<Document> findByEmployee_Id(Long employeeId);
    List<Document> findByFileType(String fileType);
    List<Document> findByEmployee_IdAndFileType(Long employeeId, String fileType);
    List<Document> findByFileNameContaining(String fileName);
    List<Document> findByEmployee_IdOrderByUploadDateDesc(Long employeeId);
    List<Document> findByUploadDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    @Query("SELECT d FROM Document d WHERE d.employee.id = :employeeId AND d.fileType = 'application/pdf'")
    List<Document> findPdfDocumentsByEmployeeId(@Param("employeeId") Long employeeId);

    @Query("SELECT d FROM Document d WHERE d.fileName LIKE %:keyword%")
    List<Document> searchDocuments(@Param("keyword") String keyword);

    @Query("SELECT d FROM Document d WHERE d.employee.id = :employeeId AND d.fileName = :fileName")
    Optional<Document> findByEmployeeIdAndFileName(@Param("employeeId") Long employeeId, @Param("fileName") String fileName);

    List<Document> findByEmployee_IdAndFileNameContaining(Long employeeId, String fileNamePart);

    @Query(value = "SELECT COUNT(*) FROM Document", nativeQuery = true)
    Long getEmployeeCount();

}
