package digitization.digitization.controller.roleCon;

import digitization.digitization.common.Validator;
import digitization.digitization.enums.RoleStatus;
import digitization.digitization.enums.Roles;
import digitization.digitization.module.Role;
import digitization.digitization.repository.RoleRepository;
import digitization.digitization.response.responce.ResponseBean;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("role")
public class RoleController {

    @Autowired
    private RoleRepository roleRepository;

    @PostMapping("roleCreate")
    public ResponseEntity save(@RequestBody Role role, BindingResult bindingResult, HttpServletRequest request) {
        try {
            ResponseBean responseBean = new ResponseBean();
            if (bindingResult.hasErrors()) {
                responseBean.setResponseType("VALIDATION");
                responseBean.setValidationErrors(Validator.create(bindingResult));
                return ResponseEntity.status(200).body(responseBean);
            }

            Role roles = roleRepository.save(role);
            role.setCreatedDate(LocalDateTime.now());
            if(roles!=null){

                responseBean.setResponseType("SUCCESS");
                responseBean.setResponseData(roles);
                return ResponseEntity.status(201).body(responseBean);
            }
            return ResponseEntity.status(400).body("Bad Request");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }

    @GetMapping("roleList")
    public List<Role> listByVehicleType() {
        try {
            List<Role> list = roleRepository.findByRoleNotAndStatus(Roles.ADMIN, RoleStatus.ACTIVE);
            return list;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("roleListAll")
    public List<Role> getRoles(){
        try {
            List<Role> list = roleRepository.findAll();
            return list;
        } catch (Exception e) {
            return null;
        }
    }
}
