package digitization.digitization.services.implementation;

import digitization.digitization.module.EducationQualification;

import java.util.List;

public interface EducationQualificationServ {
    List<EducationQualification> getEducationQualificationList();
    EducationQualification getEducationQualificationById(Long id);
    EducationQualification createEducationQualification(EducationQualification educationQualification);
    EducationQualification updateEducationQualification(Long id, EducationQualification educationQualificationDetails);
    boolean deleteEducationQualification(Long educationQualificationId);
    List<EducationQualification> getEducationQualificationsByQualification(String qualification);
    List<EducationQualification> getEducationQualificationsByCourseName(String coursename);
    List<EducationQualification> getEducationQualificationsBySchoolName(String schoolname);
    List<EducationQualification> getEducationQualificationsByCollegeName(String collegename);
    List<EducationQualification> getEducationQualificationsByUniversityName(String universityname);
}
