package digitization.digitization.converter.leave;

import digitization.digitization.dto.LeaveDto;
import digitization.digitization.module.Leave;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class DtotoLeave implements Converter<LeaveDto, Leave> {
    @Override
    public Leave convert(LeaveDto leaveDto) {
        Leave leave=new Leave();
        leave.setId(leaveDto.getId());
        leave.setLeaveType(leaveDto.getLeaveType());
        leave.setOpeningBalance(leaveDto.getOpeningBalance());
        leave.setClosingBalance(leaveDto.getClosingBalance());
        leave.setEntryDate(leaveDto.getEntryDate());
        leave.setCreatedAt(leaveDto.getCreatedAt());
        return leave;
    }
}
