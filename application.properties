spring.application.name=digitization
server.port=8082

# JWT Configuration
jwt.header = Authorization
#jwt.secret = QXNob2ttaWx0YW4sss
jwt.secret = Zk3QSv9u^E7x#P1@Rm8o%Bd2&Yw$Ls6*AqZc7Xt4GhMjKSLpNzVc5Ur9TeBjQfHw
jwt.expiration = 18000
jwt.authentication.path = auth
jwt.refresh.path = refresh


web.cors.allowed-methods: GET,POST,PUT,DELETE,OPTIONS
#web.cors.allowed-origins = http://localhost:4200,http://localhost:4400,http://localhost:4300,http://localhost:4500,http://*************:9107,http://***********:9002,http://***********:9003,http://***********:9004,http://***********:9001,http://***********:9002,http://***********:9003,http://***********:9004


spring.datasource.url = ***********************************************************************************************************************************************************************************
spring.datasource.username = tncscuser1
spring.datasource.password = TnCsC@123
spring.jackson.mapper.accept_case_insensitive_properties=true
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

#spring.datasource.url=*********************************************
#spring.datasource.username=postgres
#spring.datasource.password=root
#spring.jpa.hibernate.ddl-auto=update
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect



#logging.level.org.springframework.security=DEBUG
#logging.level.org.springframework.web=DEBUG

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# File Upload Configuration
file.upload-dir=uploads
file.max-file-size=10485760
file.max-request-size=10485760


