package digitization.digitization.dto;

public class EducationQualificationDto {

    private Long id;
    private String qualification;
    private String coursename;
    private String schoolname;
    private String collegename;
    private String universityname;
    private String specialization;

    // New fields added
    private String technicalType;
    private String remarks;
    private String grade;

    public EducationQualificationDto() {
    }

    public EducationQualificationDto(Long id, String qualification, String coursename, String schoolname, String collegename, String universityname, String specialization, String technicalType, String remarks, String grade) {
        this.id = id;
        this.qualification = qualification;
        this.coursename = coursename;
        this.schoolname = schoolname;
        this.collegename = collegename;
        this.universityname = universityname;
        this.specialization = specialization;
        this.technicalType = technicalType;
        this.remarks = remarks;
        this.grade = grade;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQualification() {
        return qualification;
    }

    public void setQualification(String qualification) {
        this.qualification = qualification;
    }

    public String getCoursename() {
        return coursename;
    }

    public void setCoursename(String coursename) {
        this.coursename = coursename;
    }

    public String getSchoolname() {
        return schoolname;
    }

    public void setSchoolname(String schoolname) {
        this.schoolname = schoolname;
    }

    public String getCollegename() {
        return collegename;
    }

    public void setCollegename(String collegename) {
        this.collegename = collegename;
    }

    public String getUniversityname() {
        return universityname;
    }

    public void setUniversityname(String universityname) {
        this.universityname = universityname;
    }

    public String getSpecialization() {
        return specialization;
    }

    public void setSpecialization(String specialization) {
        this.specialization = specialization;
    }

    public String getTechnicalType() {
        return technicalType;
    }

    public void setTechnicalType(String technicalType) {
        this.technicalType = technicalType;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }
}
