package digitization.digitization.module;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.time.LocalDate;

@Entity
@Table(name = "profile")
public class Profile {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

//    @NotBlank(message = "Father's name is required")
    private String fatherName;

    private String motherName;

    @NotNull(message = "Date of birth is required")
//    @Past(message = "Date of birth must be in the past")
    private LocalDate dateOfBirth;

    private String community;

    private String caste;

    @NotBlank(message = "District is required")
    private String district;

    @NotBlank(message = "Native place and taluk is required")
    private String nativeplaceandtaluk;

    // Present address fields
    @Column(name = "present_door_no")
    private String presentDoorNo;

    @Column(name = "present_building_name")
    private String presentBuildingName;

    @Column(name = "present_street_address")
    private String presentStreetAddress;

    @Column(name = "present_city")
    private String presentCity;

    @Column(name = "present_pincode")
    private String presentPincode;

    // Permanent address fields
    @Column(name = "permanent_door_no")
    private String permanentDoorNo;

    @Column(name = "permanent_building_name")
    private String permanentBuildingName;

    @Column(name = "permanent_street_address")
    private String permanentStreetAddress;

    @Column(name = "permanent_city")
    private String permanentCity;

    @Column(name = "permanent_pincode")
    private String permanentPincode;

    @Column(name = "profile_photo")
    private String profilephoto;

    @Column(name = "employee_type")
    private String employeeType;

    @NotBlank(message = "Email is required")
//    @Email(message = "Email should be valid")
    private String email;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "employee_id", nullable = false)
    private Employee employee;

    public Profile() {
    }

    public Profile(Long id, String fatherName, String motherName, LocalDate dateOfBirth, String community, String caste, String district, String nativeplaceandtaluk, String presentDoorNo, String presentBuildingName, String presentStreetAddress, String presentCity, String presentPincode, String permanentDoorNo, String permanentBuildingName, String permanentStreetAddress, String permanentCity, String permanentPincode, String profilephoto, String employeeType, String email, Employee employee) {
        this.id = id;
        this.fatherName = fatherName;
        this.motherName = motherName;
        this.dateOfBirth = dateOfBirth;
        this.community = community;
        this.caste = caste;
        this.district = district;
        this.nativeplaceandtaluk = nativeplaceandtaluk;
        this.presentDoorNo = presentDoorNo;
        this.presentBuildingName = presentBuildingName;
        this.presentStreetAddress = presentStreetAddress;
        this.presentCity = presentCity;
        this.presentPincode = presentPincode;
        this.permanentDoorNo = permanentDoorNo;
        this.permanentBuildingName = permanentBuildingName;
        this.permanentStreetAddress = permanentStreetAddress;
        this.permanentCity = permanentCity;
        this.permanentPincode = permanentPincode;
        this.profilephoto = profilephoto;
        this.employeeType = employeeType;
        this.email = email;
        this.employee = employee;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFatherName() {
        return fatherName;
    }

    public void setFatherName(String fatherName) {
        this.fatherName = fatherName;
    }

    public String getMotherName() {
        return motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getCommunity() {
        return community;
    }

    public void setCommunity(String community) {
        this.community = community;
    }

    public String getCaste() {
        return caste;
    }

    public void setCaste(String caste) {
        this.caste = caste;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getNativeplaceandtaluk() {
        return nativeplaceandtaluk;
    }

    public void setNativeplaceandtaluk(String nativeplaceandtaluk) {
        this.nativeplaceandtaluk = nativeplaceandtaluk;
    }

    // Present address getters and setters
    public String getPresentDoorNo() {
        return presentDoorNo;
    }

    public void setPresentDoorNo(String presentDoorNo) {
        this.presentDoorNo = presentDoorNo;
    }

    public String getPresentBuildingName() {
        return presentBuildingName;
    }

    public void setPresentBuildingName(String presentBuildingName) {
        this.presentBuildingName = presentBuildingName;
    }

    public String getPresentStreetAddress() {
        return presentStreetAddress;
    }

    public void setPresentStreetAddress(String presentStreetAddress) {
        this.presentStreetAddress = presentStreetAddress;
    }

    public String getPresentCity() {
        return presentCity;
    }

    public void setPresentCity(String presentCity) {
        this.presentCity = presentCity;
    }

    public String getPresentPincode() {
        return presentPincode;
    }

    public void setPresentPincode(String presentPincode) {
        this.presentPincode = presentPincode;
    }

    // Permanent address getters and setters
    public String getPermanentDoorNo() {
        return permanentDoorNo;
    }

    public void setPermanentDoorNo(String permanentDoorNo) {
        this.permanentDoorNo = permanentDoorNo;
    }

    public String getPermanentBuildingName() {
        return permanentBuildingName;
    }

    public void setPermanentBuildingName(String permanentBuildingName) {
        this.permanentBuildingName = permanentBuildingName;
    }

    public String getPermanentStreetAddress() {
        return permanentStreetAddress;
    }

    public void setPermanentStreetAddress(String permanentStreetAddress) {
        this.permanentStreetAddress = permanentStreetAddress;
    }

    public String getPermanentCity() {
        return permanentCity;
    }

    public void setPermanentCity(String permanentCity) {
        this.permanentCity = permanentCity;
    }

    public String getPermanentPincode() {
        return permanentPincode;
    }

    public void setPermanentPincode(String permanentPincode) {
        this.permanentPincode = permanentPincode;
    }

    public String getProfilephoto() {
        return profilephoto;
    }

    public void setProfilephoto(String profilephoto) {
        this.profilephoto = profilephoto;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }


    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Employee getEmployee() {
        return employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }
}
