package digitization.digitization.services;

import digitization.digitization.dto.FileResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

@Service
public class FileUploadService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private String fileFolder = "resources";


    @Autowired
    private RestTemplate restTemplate;

    public FileResponse upload(MultipartFile file){
        FileResponse fileResponse = new FileResponse();
        Path fileLocation = null;
        String fileName = null;
        fileName = StringUtils.cleanPath(file.getOriginalFilename());
        Path filePath = Paths.get(System.getProperty("user.home")+ File.separator+fileFolder, File.separator,fileName);
        if(!Files.exists(filePath)){
            fileLocation = filePath;
            System.out.println(fileLocation);
        }else{
            String time = LocalTime.now().format(DateTimeFormatter.ofPattern("HH_mm"));
            fileName = time+fileName;
            fileLocation = Paths.get(System.getProperty("user.home")+ File.separator+fileFolder, File.separator,fileName);
            System.out.println(fileLocation);
        }
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.reset();
            Files.copy(file.getInputStream(),fileLocation, StandardCopyOption.REPLACE_EXISTING);
            String hash =    DigestUtils.md5DigestAsHex(file.getBytes());
            Long fileSize = file.getSize();
            fileResponse.setFileName(fileName);
            fileResponse.setFileHash(hash);
            fileResponse.setFileSize(fileSize);

            System.out.println(hash);

        } catch (IOException | NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return  fileResponse;
    }

}
