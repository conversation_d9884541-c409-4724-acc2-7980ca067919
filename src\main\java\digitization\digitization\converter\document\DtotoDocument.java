package digitization.digitization.converter.document;

import digitization.digitization.dto.DocumentDto;
import digitization.digitization.module.Document;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class DtotoDocument implements Converter<DocumentDto, Document> {
    @Override
    public Document convert(DocumentDto documentDto) {
        Document document = new Document();
        document.setId(documentDto.getId());
        document.setFileName(documentDto.getFileName());
        document.setFileSize(documentDto.getFileSize());
        document.setFileType(documentDto.getFileType());
        document.setFilePath(documentDto.getFilePath());
        document.setFileUrl(documentDto.getFileUrl());
        document.setUploadDate(documentDto.getUploadDate());
        // Note: Employee relationship should be set separately if needed
        return document;
    }
}
