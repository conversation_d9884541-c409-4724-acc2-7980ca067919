package digitization.digitization.services.regionManagers;

import digitization.digitization.exception.AccessDeniedException;
import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.Employee;
import digitization.digitization.module.EmployeeStatus;
import digitization.digitization.module.User;
import digitization.digitization.repository.EmployeeRepo;
import digitization.digitization.repository.EmployeeStatusRepository;
import digitization.digitization.repository.UserRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EmployeeListBasedOnRegionServiceImpl implements EmployeeListBasedOnRegionService{

    @Autowired
    private EmployeeStatusRepository employeeStatusRepository;

    @Autowired
    private EmployeeRepo employeeRepo;

    @Autowired
    private UserRepository userRepository;

//    @Override
//    public List<EmployeeStatus> getPendingEmployeesByRegion(String username) {
//
//        User user = userRepository.findByUsername(username)
//               .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));
//
//        String role = user.getRole().get(0).getRole().name();
//        List<String> allowedRoles = Arrays.asList("AM", "DRM", "MA", "RM", "SUPERINTENDENT");
//        if (!allowedRoles.contains(role)) {
//            throw new AccessDeniedException(false,"Not valid user","User does not have permission to view this report");
//        }
//
//        String region = user.getRegion();
//        if (region == null || region.trim().isEmpty()) {
//            throw new IllegalStateException("User district is not configured");
//        }
//
//        return employeeStatusRepository.findByStatusAndEmployeeDistrict("pending", region);
//    }

    public List<EmployeeStatus> getPendingEmployeesByRegion(String status, String district) {
        if (status == null || status.trim().isEmpty() || district == null || district.trim().isEmpty()) {
            throw new IllegalArgumentException("Status and district cannot be null or empty");
        }
        return employeeStatusRepository.findByStatusAndEmployeeDistrict(status, district);
    }


    @Override
    public List<Employee> getPendingEmployeesByDistrict(String role, String district) {
        System.out.println("=== DEBUG: getPendingEmployeesByDistrict ===");
        System.out.println("Input role: " + role);
        System.out.println("Input district: " + district);

        if (!role.equals("SUPERINTENDENT") && !role.equals("AM")) {
            System.out.println("ERROR: Role check failed. Role '" + role + "' is not SUPERINTENDENT or AM");
            throw new AccessDeniedException(false,"Through","Only SUPERINTENDENT or AM can view pending employees");
        }

        System.out.println("Role check passed. Fetching pending employee statuses...");
        List<EmployeeStatus> pendingStatuses = employeeStatusRepository.findByStatus("pending");
        System.out.println("Found " + pendingStatuses.size() + " employee statuses with 'pending' status");

        if (pendingStatuses.isEmpty()) {
            System.out.println("No pending employee statuses found");
            return new ArrayList<>();
        }

        List<Long> empIds = pendingStatuses.stream()
                .map(EmployeeStatus::getEmpId)
                .collect(Collectors.toList());
        System.out.println("Employee IDs with pending status: " + empIds);

        // First try exact match
        List<Employee> employees = employeeRepo.findByIdInAndDistrict(empIds, district);
        System.out.println("Found " + employees.size() + " employees with exact district match");

        // If no exact match, try flexible matching (handles whitespace variations)
        if (employees.isEmpty()) {
            System.out.println("No exact district match found, trying flexible matching...");
            employees = employeeRepo.findByIdInAndDistrictFlexible(empIds, district);
            System.out.println("Found " + employees.size() + " employees with flexible district matching");
        }

        if (employees.isEmpty()) {
            System.out.println("No employees found in district '" + district + "' for the pending employee IDs");
            // Let's also check what districts the pending employees are in
            List<Employee> allPendingEmployees = employeeRepo.findAllById(empIds);
            System.out.println("All pending employees and their districts:");
            for (Employee emp : allPendingEmployees) {
                System.out.println("  Employee ID: " + emp.getId() + ", Name: " + emp.getEmployeeName() + ", District: '" + emp.getDistrict() + "'");
            }
        }

        return employees;
    }

    @Override
    public List<Employee> getSUPERINTENDENTandAMAPPROVEDEmployeesByDistrict(String role, String district) {
        System.out.println("=== DEBUG: getSUPERINTENDENTandAMAPPROVEDEmployeesByDistrict ===");
        System.out.println("Input role: " + role);
        System.out.println("Input district: " + district);

        if (!role.equals("DRM") && !role.equals("MA")) {
            System.out.println("ERROR: Role check failed. Role '" + role + "' is not DRM or MA");
            throw new AccessDeniedException(false,"Through","Only DRM or MA can view SUPERINTENDENT and AM approved employees");
        }

        System.out.println("Role check passed. Fetching ACCEPTED_BY_SUPERINTENDENT_AM employee statuses...");
        List<EmployeeStatus> approvedStatuses = employeeStatusRepository.findByStatus("ACCEPTED_BY_SUPERINTENDENT_AM");
        System.out.println("Found " + approvedStatuses.size() + " employee statuses with 'ACCEPTED_BY_SUPERINTENDENT_AM' status");

        if (approvedStatuses.isEmpty()) {
            System.out.println("No ACCEPTED_BY_SUPERINTENDENT_AM employee statuses found");
            return new ArrayList<>();
        }

        List<Long> empIds = approvedStatuses.stream()
                .map(EmployeeStatus::getEmpId)
                .collect(Collectors.toList());
        System.out.println("Employee IDs with ACCEPTED_BY_SUPERINTENDENT_AM status: " + empIds);

        // First try exact match
        List<Employee> employees = employeeRepo.findByIdInAndDistrict(empIds, district);
        System.out.println("Found " + employees.size() + " employees with exact district match");

        // If no exact match, try flexible matching (handles whitespace variations)
        if (employees.isEmpty()) {
            System.out.println("No exact district match found, trying flexible matching...");
            employees = employeeRepo.findByIdInAndDistrictFlexible(empIds, district);
            System.out.println("Found " + employees.size() + " employees with flexible district matching");
        }

        if (employees.isEmpty()) {
            System.out.println("No employees found in district '" + district + "' for the ACCEPTED_BY_SUPERINTENDENT_AM employee IDs");
            // Let's also check what districts the approved employees are in
            List<Employee> allApprovedEmployees = employeeRepo.findAllById(empIds);
            System.out.println("All ACCEPTED_BY_SUPERINTENDENT_AM employees and their districts:");
            for (Employee emp : allApprovedEmployees) {
                System.out.println("  Employee ID: " + emp.getId() + ", Name: " + emp.getEmployeeName() + ", District: '" + emp.getDistrict() + "'");
            }
        }

        return employees;
    }

    @Override
    public List<Employee> getACCEPTED_BY_DRM_MAEmployeesByDistrict(String role, String district) {
        System.out.println("=== DEBUG: getACCEPTED_BY_DRM_MAEmployeesByDistrict ===");
        System.out.println("Input role: " + role);
        System.out.println("Input district: " + district);

        if (!role.equals("RM")) {
            System.out.println("ERROR: Role check failed. Role '" + role + "' is not RM");
            throw new AccessDeniedException(false,"Through","Only RM can view DRM and MA approved employees");
        }

        System.out.println("Role check passed. Fetching ACCEPTED_BY_DRM_MA employee statuses...");
        List<EmployeeStatus> approvedStatuses = employeeStatusRepository.findByStatus("ACCEPTED_BY_DRM_MA");
        System.out.println("Found " + approvedStatuses.size() + " employee statuses with 'ACCEPTED_BY_DRM_MA' status");

        if (approvedStatuses.isEmpty()) {
            System.out.println("No ACCEPTED_BY_DRM_MA employee statuses found");
            return new ArrayList<>();
        }

        List<Long> empIds = approvedStatuses.stream()
                .map(EmployeeStatus::getEmpId)
                .collect(Collectors.toList());
        System.out.println("Employee IDs with ACCEPTED_BY_DRM_MA status: " + empIds);

        // First try exact match
        List<Employee> employees = employeeRepo.findByIdInAndDistrict(empIds, district);
        System.out.println("Found " + employees.size() + " employees with exact district match");

        // If no exact match, try flexible matching (handles whitespace variations)
        if (employees.isEmpty()) {
            System.out.println("No exact district match found, trying flexible matching...");
            employees = employeeRepo.findByIdInAndDistrictFlexible(empIds, district);
            System.out.println("Found " + employees.size() + " employees with flexible district matching");
        }

        if (employees.isEmpty()) {
            System.out.println("No employees found in district '" + district + "' for the ACCEPTED_BY_DRM_MA employee IDs");
            // Let's also check what districts the approved employees are in
            List<Employee> allApprovedEmployees = employeeRepo.findAllById(empIds);
            System.out.println("All ACCEPTED_BY_DRM_MA employees and their districts:");
            for (Employee emp : allApprovedEmployees) {
                System.out.println("  Employee ID: " + emp.getId() + ", Name: " + emp.getEmployeeName() + ", District: '" + emp.getDistrict() + "'");
            }
        }

        return employees;
    }

    @Override
    @Transactional
    public void updateEmployeeStatus(Long empId, String newStatus, String updatedBy, String remarks, String role) {
        EmployeeStatus status = employeeStatusRepository.findByEmpId(empId)
                .orElseThrow(() -> new ResourceNotFoundException("EmployeeStatus not found for empId: " + empId));

        String currentStatus = status.getStatus();
        String targetStatus = newStatus.toUpperCase();


//        if (!isValidStatusTransition(currentStatus, targetStatus, role)) {
//            throw new IllegalStateException("Invalid status transition or unauthorized role");
//        }

        status.setStatus(targetStatus);

        if ("REJECTED".equals(targetStatus)) {
            status.setRejectedBy(updatedBy);
            status.setRemarks(remarks);
        } else {
            status.setApprovedBy(updatedBy);
            status.setRemarks(remarks);
        }

        employeeStatusRepository.save(status);
    }


    private boolean isValidStatusTransition(String currentStatus, String targetStatus, String role) {
        switch (role) {
            case "SUPERINTENDENT":
            case "AM":
                return "pending".equals(currentStatus) &&
                        ("ACCEPTED_BY_SUPERINTENDENT_AM".equals(targetStatus) || "REJECTED".equals(targetStatus));
            case "DRM":
            case "MA":
                return "ACCEPTED_BY_SUPERINTENDENT_AM".equals(currentStatus) &&
                        ("ACCEPTED_BY_DRM_MA".equals(targetStatus) || "REJECTED".equals(targetStatus));
            case "RM":
                return "ACCEPTED_BY_DRM_MA".equals(currentStatus) &&
                        ("APPROVED".equals(targetStatus) || "REJECTED".equals(targetStatus));
            default:
                return false;
        }
    }


}
