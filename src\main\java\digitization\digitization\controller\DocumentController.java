package digitization.digitization.controller;

import digitization.digitization.dto.DocumentDto;
import digitization.digitization.services.DocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/documents")
@CrossOrigin(value = "*")
public class DocumentController {

    @Autowired
    private DocumentService documentService;

    @PostMapping("/upload/{employeeId}")
    public ResponseEntity<DocumentDto> uploadDocument(
            @PathVariable Long employeeId,
            @RequestParam("file") MultipartFile file) {

        DocumentDto uploadedDocument = documentService.uploadDocument(file, employeeId);
        return ResponseEntity.ok(uploadedDocument);
    }

    @GetMapping("/employee/{employeeId}")
    public ResponseEntity<List<DocumentDto>> getDocumentsByEmployeeId(@PathVariable Long employeeId) {
        List<DocumentDto> documents = documentService.getDocumentsByEmployeeId(employeeId);
        return ResponseEntity.ok(documents);
    }

    @GetMapping("/employee/{employeeId}/pdf")
    public ResponseEntity<List<DocumentDto>> getPdfDocumentsByEmployeeId(@PathVariable Long employeeId) {
        List<DocumentDto> documents = documentService.getPdfDocumentsByEmployeeId(employeeId);
        return ResponseEntity.ok(documents);
    }

    @GetMapping("/{documentId}")
    public ResponseEntity<DocumentDto> getDocumentById(@PathVariable Long documentId) {
        DocumentDto document = documentService.getDocumentById(documentId);
        return ResponseEntity.ok(document);
    }

    @GetMapping("/download/{documentId}")
    public ResponseEntity<Resource> downloadDocument(@PathVariable Long documentId, HttpServletRequest request) {
        Resource resource = documentService.downloadDocument(documentId);
        DocumentDto documentInfo = documentService.getDocumentById(documentId);

        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            // Fallback to default content type
        }

        if (contentType == null) {
            contentType = "application/octet-stream";
        }

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + documentInfo.getFileName() + "\"")
                .body(resource);
    }

    @GetMapping("/download/employee/{employeeId}/file/{fileName}")
    public ResponseEntity<Resource> downloadDocumentByEmployeeAndFileName(
            @PathVariable Long employeeId,
            @PathVariable String fileName,
            HttpServletRequest request) {

        Resource resource = documentService.downloadDocumentByEmployeeIdAndFileName(employeeId, fileName);

        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            // Fallback to default content type
        }

        if (contentType == null) {
            contentType = "application/octet-stream";
        }

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                .body(resource);
    }

    @DeleteMapping("/{documentId}")
    public ResponseEntity<String> deleteDocument(@PathVariable Long documentId) {
        boolean deleted = documentService.deleteDocument(documentId);
        if (deleted) {
            return ResponseEntity.ok("Document deleted successfully");
        } else {
            return ResponseEntity.badRequest().body("Failed to delete document");
        }
    }

    @GetMapping("/all")
    public ResponseEntity<List<DocumentDto>> getAllDocuments() {
        List<DocumentDto> documents = documentService.getAllDocuments();
        return ResponseEntity.ok(documents);
    }

    @GetMapping("/search")
    public ResponseEntity<List<DocumentDto>> searchDocuments(@RequestParam String keyword) {
        List<DocumentDto> documents = documentService.searchDocuments(keyword);
        return ResponseEntity.ok(documents);
    }

    @PostMapping("/uploadToPlaceholder/{documentId}")
    public ResponseEntity<DocumentDto> uploadFileToPlaceholder(
            @PathVariable Long documentId,
            @RequestParam("file") MultipartFile file) {

        DocumentDto updatedDocument = documentService.updateDocumentWithFile(documentId, file);
        return ResponseEntity.ok(updatedDocument);
    }

    @GetMapping("/placeholders/employee/{employeeId}")
    public ResponseEntity<List<DocumentDto>> getDocumentPlaceholders(@PathVariable Long employeeId) {
        List<DocumentDto> placeholders = documentService.getDocumentPlaceholdersByEmployeeId(employeeId);
        return ResponseEntity.ok(placeholders);
    }
}
