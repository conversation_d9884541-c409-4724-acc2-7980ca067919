package digitization.digitization.converter.employee;

import digitization.digitization.dto.EmployeeWithStatusDto;
import digitization.digitization.module.Employee;
import digitization.digitization.module.EmployeeStatus;
import digitization.digitization.services.EmployeeStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class EmployeeToEmployeeWithStatusDto implements Converter<Employee, EmployeeWithStatusDto> {

    @Autowired
    private EmployeeStatusService employeeStatusService;

    @Override
    public EmployeeWithStatusDto convert(Employee employee) {
        EmployeeWithStatusDto dto = new EmployeeWithStatusDto();

        // Set employee data
        dto.setId(employee.getId());
        dto.setEmployeeName(employee.getEmployeeName());
        dto.setFatherName(employee.getFatherName());
        dto.setDateOfBirth(employee.getDateOfBirth());
        dto.setReligion(employee.getReligion());
        dto.setCommunity(employee.getCommunity());
        dto.setDesignation(employee.getCurrentDesignation());
        dto.setDateOfEntry(employee.getDateOfEntry());
        dto.setDistrict(employee.getDistrict());


        // Get and set status information
        try {
            EmployeeStatus employeeStatus = employeeStatusService.getEmployeeStatusByEmpId(employee.getId());
            dto.setStatus(employeeStatus.getStatus());
            dto.setApprovedBy(employeeStatus.getApprovedBy());
            dto.setRejectedBy(employeeStatus.getRejectedBy());
            dto.setRemarks(employeeStatus.getRemarks());
            dto.setCreatedAt(employeeStatus.getCreatedAt());
            dto.setUpdatedAt(employeeStatus.getUpdatedAt());
        } catch (Exception e) {
            dto.setStatus("unknown"); // Default status if not found
            dto.setApprovedBy(null);
            dto.setRejectedBy(null);
            dto.setRemarks(null);
            dto.setCreatedAt(null);
            dto.setUpdatedAt(null);
        }

        return dto;
    }
}
