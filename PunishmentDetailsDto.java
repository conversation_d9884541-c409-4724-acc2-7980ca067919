package digitization.digitization.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDate;

public class PunishmentDetailsDto {

    private Long id;

    @JsonProperty("punishmenttype")
    private String punishmenttype;

    @JsonProperty("date")
    private LocalDate date;

    @JsonProperty("casedetails")
    private String casedetails;

    public PunishmentDetailsDto() {
    }

    public PunishmentDetailsDto(Long id, String punishmenttype, LocalDate date, String casedetails) {
        this.id = id;
        this.punishmenttype = punishmenttype;
        this.date = date;
        this.casedetails = casedetails;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPunishmenttype() {
        return punishmenttype;
    }

    public void setPunishmenttype(String punishmenttype) {
        this.punishmenttype = punishmenttype;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getCasedetails() {
        return casedetails;
    }

    public void setCasedetails(String casedetails) {
        this.casedetails = casedetails;
    }
}
