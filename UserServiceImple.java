package digitization.digitization.services.userService;

import digitization.digitization.common.DTRequestBean;
import digitization.digitization.common.DTResponseBean;
import digitization.digitization.common.Pageable;
import digitization.digitization.converter.user.UserDtoToUser;
import digitization.digitization.converter.user.UserToUserDto;
import digitization.digitization.dto.userDto.UserDto;
import digitization.digitization.enums.DeleteStatus;
import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.Role;
import digitization.digitization.module.User;
import digitization.digitization.repository.RoleRepository;
import digitization.digitization.repository.UserRepository;
import digitization.digitization.response.responce.ApiResponce;
import digitization.digitization.response.responce.SuccessResponce;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import digitization.digitization.services.userService.UsersService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class UserServiceImple implements UsersService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserDtoToUser userDtoToUser;

    @Autowired
    private UserToUserDto userToUserDto;

    @Autowired
    private RoleRepository roleRepository;

    private boolean accountActive = true;
    private boolean accountLocked = false;



@Override
public User save(UserDto userDto) {
    User user = userDtoToUser.convert(userDto);
    if(userDto.getRoleId()!=null){
        List<Role> roles = new ArrayList<>();
        Optional<Role>   optionalRole = roleRepository.findById(userDto.getRoleId());
        user.setRoleType(optionalRole.get().getRole().name());

        Optional<Role> optionalRoles = roleRepository.findById(userDto.getRoleId());
        if (!optionalRoles.isPresent()) {
            throw new ResourceNotFoundException("Error", "Role", "NOT FOUND");
        }
        roles.add(optionalRoles.get());

        user.setRole(roles);
    }

    user.setAccountLocked(accountLocked);
    user.setActive(accountActive);
    user.setCreatedAt(LocalDateTime.now());
    user.setStatus(DeleteStatus.ACTIVE);

    User save = userRepository.save(user);
//        if(save!=null){
//            UserAudit userAudit = userToUserAudit.convert(save);
//            UserAudit saveAudit = userAuditRepository.save(userAudit);
//        }


    return save;
}



    @Override
    public Boolean isUserNameExists(String userName) {
        Boolean optionalRole = userRepository.existsByusername(userName);
        if (optionalRole) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean isMobileExists(String mobile) {
        Boolean optionalRole = userRepository.existsBymobile(mobile);
        if (optionalRole) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean isEmailExists(String email) {
        Boolean optionalRole = userRepository.existsByemail(email);
        if (optionalRole) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public ApiResponce list() {
        ApiResponce apiResponce = new ApiResponce();
        Long count = userRepository.count();
        List<UserDto> userDtos = new ArrayList<>();
        userRepository.findAll().iterator().forEachRemaining(dataList->{
            userDtos.add(userToUserDto.convert(dataList));
        });
        apiResponce.setData(userDtos);
        apiResponce.setTotalRecords(count);
        return apiResponce;
    }

    @Override
    public ApiResponce listByRole() {
        ApiResponce apiResponce = new ApiResponce();
        Long count = userRepository.count();
        List<UserDto> userDtos = new ArrayList<>();
        userRepository.findAll().iterator().forEachRemaining(dataList->{
            if(dataList.getRole() != null && dataList.getRoleType().equalsIgnoreCase("SALES_MANAGER")){
                userDtos.add(userToUserDto.convert(dataList));
            }
        });
        apiResponce.setData(userDtos);
        apiResponce.setTotalRecords(count);
        return apiResponce;
    }

    @Override
    public ApiResponce listOfPage(Pageable pageable) {
        ApiResponce apiResponce = new ApiResponce();
        Long totalCount = userRepository.countByStatus(DeleteStatus.ACTIVE);
        List<UserDto> userList = new ArrayList<>();
        userRepository.findByStatus(DeleteStatus.ACTIVE, PageRequest.of(pageable.getStart(), pageable.getSize(), Sort.by("createdAt").descending())).iterator().forEachRemaining(
                device -> userList.add(userToUserDto.convert(device)));
        apiResponce.setStatus(true);
        apiResponce.setTotalRecords(totalCount);
        apiResponce.setData(userList);
        return apiResponce;
    }

//    @Override
//    public ApiResponce listOfPage(Pageable pageable) {
//        ApiResponce apiResponce = new ApiResponce();
//        Long totalCount = userRepository.countByStatus(DeleteStatus.ACTIVE);
//
//        Page<User> userPage = userRepository.findByStatus(
//                DeleteStatus.ACTIVE,
//                PageRequest.of(pageable.getStart(), pageable.getSize(), Sort.by("createdAt").descending())
//        );
//
//        List<UserDto> userList = userPage.getContent()
//                .stream()
//                .map(userToUserDto::convert)
//                .collect(Collectors.toList());
//
//        apiResponce.setStatus(true);
//        apiResponce.setTotalRecords(totalCount);
//        apiResponce.setData(userList);
//        return apiResponce;
//    }


    @Override
    public UserDto viewById(Long id) {
//        Optional<User> optionalDeath = userRepository.findById(id);
//        if (!optionalDeath.isPresent()) {
//            throw new ResourceNotFoundException("Death", "Id", id);
//        }
//        return userToUserDto.convert(optionalDeath.get());
        Optional<User> user = userRepository.findById(id);
        UserDto userDto = userToUserDto.convert(user.get());
        if(!user.isPresent()){
            throw new ResourceNotFoundException("Not Found User","id", id);
        }
        return userDto;
    }

    @Override
    public DTResponseBean listByDTRequest(DTRequestBean dtRequestBean) {
        DTResponseBean apiResponseBean = new DTResponseBean();
        int totalRecordsCount = 0;
        List<UserDto> linkDtoList = new ArrayList<>();

        if (dtRequestBean.getSearch().getValue().equals("")) {
            totalRecordsCount = (int) userRepository.count();

            userRepository.findAll(PageRequest.of(dtRequestBean.getStart(), dtRequestBean.getLength(), Sort.by("createdAt").descending()))
                    .forEach(device -> linkDtoList.add(userToUserDto.convert(device)));
        }

        apiResponseBean.setRecordsTotal(totalRecordsCount);
        apiResponseBean.setRecordsFiltered(totalRecordsCount);
        apiResponseBean.setData(linkDtoList);
        apiResponseBean.setDraw(dtRequestBean.getDraw());

        return apiResponseBean;
    }

    @Override
    public User userUpdate(UserDto userDto, Long id) {
        Optional<User> user = userRepository.findById(id);
        if (!user.isPresent()) {
            throw new ResourceNotFoundException("Not Found Driver", "id", id);
        } else {
            User oldDriver = user.get();
            User updated = userDtoToUser.convert(userDto);
            updated.setId(id);
            updated.setStatus(DeleteStatus.ACTIVE);
            updated.setConfirmPassword(oldDriver.getConfirmPassword());

            if (userDto.getRoleId() != null) {
                List<Role> roles = new ArrayList<>();
                Optional<Role> optionalRole = roleRepository.findById(userDto.getRoleId());
                updated.setRoleType(optionalRole.get().getRole().name());

                Optional<Role> optionalRoles = roleRepository.findById(userDto.getRoleId());
                if (!optionalRoles.isPresent()) {
                    throw new ResourceNotFoundException("Error", "Role", "NOT FOUND");
                }
                roles.add(optionalRoles.get());

                updated.setRole(roles);
            }

            User upadte = userRepository.save(updated);

            return upadte;
        }
    }

    @Override
    public SuccessResponce deleteById(long id) {
        SuccessResponce successResponce =  new SuccessResponce();
        Optional<User> user = userRepository.findById(id);
        if(user.isPresent()){
            user.get().setStatus(DeleteStatus.INACTIVE);
            User deletedUser =   userRepository.save(user.get());
            successResponce.setMessage("Deleted Succesfully");
            return successResponce;
        }
        else {
            successResponce.setMessage("Unable to delete");
            return successResponce;

        }
    }

}

