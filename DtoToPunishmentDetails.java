package digitization.digitization.converter.punishmentdetails;

import digitization.digitization.dto.PunishmentDetailsDto;
import digitization.digitization.module.PunishmentDetails;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class DtoToPunishmentDetails implements Converter<PunishmentDetailsDto, PunishmentDetails> {
    @Override
    public PunishmentDetails convert(PunishmentDetailsDto punishmentDetailsDto) {
        PunishmentDetails punishmentDetails = new PunishmentDetails();
        punishmentDetails.setId(punishmentDetailsDto.getId());
        punishmentDetails.setPunishmenttype(punishmentDetailsDto.getPunishmenttype());
        punishmentDetails.setDate(punishmentDetailsDto.getDate());
        punishmentDetails.setCasedetails(punishmentDetailsDto.getCasedetails());
        return punishmentDetails;
    }
}
