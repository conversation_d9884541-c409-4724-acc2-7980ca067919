package digitization.digitization.converter.service;

import digitization.digitization.dto.ServiceHistoryDto;
import digitization.digitization.module.ServiceHistory;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class ServicetoDto implements Converter<ServiceHistory, ServiceHistoryDto> {
    @Override
    public ServiceHistoryDto convert(ServiceHistory serviceHistory) {
        ServiceHistoryDto serv=new ServiceHistoryDto();
        serv.setId(serviceHistory.getId());
        serv.setDate(serviceHistory.getDate());
        serv.setStatus(serviceHistory.getStatus());
        serv.setType(serviceHistory.getType());
        serv.setAppointmenttype(serviceHistory.getAppointmenttype());
        serv.setModeofappointment(serviceHistory.getModeofappointment());
        serv.setDateofappointment(serviceHistory.getDateofappointment());
        serv.setProceedingorderdate(serviceHistory.getProceedingorderdate());
        serv.setJoiningdate(serviceHistory.getJoiningdate());
        serv.setPromoteddate(serviceHistory.getPromoteddate());
        serv.setFromdesignation(serviceHistory.getFromdesignation());
        serv.setTopromoted(serviceHistory.getTopromoted());
        serv.setFromdate(serviceHistory.getFromdate());
        serv.setTodate(serviceHistory.getTodate());
        serv.setTypeofincrement(serviceHistory.getTypeofincrement());
        serv.setFromplace(serviceHistory.getFromplace());
        serv.setToplace(serviceHistory.getToplace());
        return serv;
    }
}
