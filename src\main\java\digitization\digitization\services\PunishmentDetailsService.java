package digitization.digitization.services;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.PunishmentDetails;
import digitization.digitization.repository.PunishmentDetailsRepository;
import digitization.digitization.services.implementation.PunishmentDetailsServ;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
public class PunishmentDetailsService implements PunishmentDetailsServ {

    @Autowired
    PunishmentDetailsRepository punishmentDetailsRepository;

    @Override
    public List<PunishmentDetails> getPunishmentDetailsList() {
        List<PunishmentDetails> list = punishmentDetailsRepository.findAll();
        return list;
    }

    @Override
    public PunishmentDetails getPunishmentDetailsById(Long punishmentDetailsId) {
        Optional<PunishmentDetails> optionalPunishmentDetails = this.punishmentDetailsRepository.findById(punishmentDetailsId);
        if (!optionalPunishmentDetails.isPresent()) {
            throw new ResourceNotFoundException("Punishment details not found with id: " + punishmentDetailsId, null, null);
        }
        return optionalPunishmentDetails.get();
    }

    @Override
    public PunishmentDetails createPunishmentDetails(PunishmentDetails punishmentDetails) {
        PunishmentDetails punDetails = new PunishmentDetails();
        punDetails.setId(punishmentDetails.getId());
        punDetails.setPunishmenttype(punishmentDetails.getPunishmenttype());
        punDetails.setDate(punishmentDetails.getDate());
        punDetails.setCasedetails(punishmentDetails.getCasedetails());
        punDetails.setCaseDate(punishmentDetails.getCaseDate());
        punDetails.setCaseNumber(punishmentDetails.getCaseNumber());
        punDetails.setDescription(punishmentDetails.getDescription());
        punDetails.setPersonsInvolved(punishmentDetails.getPersonsInvolved());
        punDetails.setPresentStatus(punishmentDetails.getPresentStatus());
        punDetails.setWithHoldingFromDate(punishmentDetails.getWithHoldingFromDate());
        punDetails.setWithHoldingToDate(punishmentDetails.getWithHoldingToDate());

        punishmentDetailsRepository.save(punDetails);
        return punDetails;
    }

    @Override
    public PunishmentDetails updatePunishmentDetails(Long id, PunishmentDetails punishmentDetailsDetails) {
        PunishmentDetails punishmentDetails = punishmentDetailsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Punishment details not found with id: " + id, null, null));

        punishmentDetails.setPunishmenttype(punishmentDetailsDetails.getPunishmenttype());
        punishmentDetails.setDate(punishmentDetailsDetails.getDate());
        punishmentDetails.setCasedetails(punishmentDetailsDetails.getCasedetails());
        punishmentDetails.setCaseDate(punishmentDetailsDetails.getCaseDate());
        punishmentDetails.setCaseNumber(punishmentDetailsDetails.getCaseNumber());
        punishmentDetails.setDescription(punishmentDetailsDetails.getDescription());
        punishmentDetails.setPersonsInvolved(punishmentDetailsDetails.getPersonsInvolved());
        punishmentDetails.setPresentStatus(punishmentDetailsDetails.getPresentStatus());
        punishmentDetails.setWithHoldingFromDate(punishmentDetailsDetails.getWithHoldingFromDate());
        punishmentDetails.setWithHoldingToDate(punishmentDetailsDetails.getWithHoldingToDate());

        return punishmentDetailsRepository.save(punishmentDetails);
    }

    @Override
    public boolean deletePunishmentDetails(Long punishmentDetailsId) {
        try {
            Optional<PunishmentDetails> punishmentDetails = punishmentDetailsRepository.findById(punishmentDetailsId);
            if (punishmentDetails.isPresent()) {
                punishmentDetailsRepository.deleteById(punishmentDetailsId);
                return true;
            } else {
                System.out.println("Punishment details not found with ID: " + punishmentDetailsId);
                return false;
            }
        } catch (Exception e) {
            System.out.println("Error deleting punishment details: " + e.getMessage());
            return false;
        }
    }

    @Override
    public List<PunishmentDetails> getPunishmentDetailsByType(String punishmenttype) {
        return punishmentDetailsRepository.findByPunishmenttype(punishmenttype);
    }

    @Override
    public List<PunishmentDetails> getPunishmentDetailsByDate(LocalDate date) {
        return punishmentDetailsRepository.findByDate(date);
    }

    @Override
    public List<PunishmentDetails> getPunishmentDetailsByDateRange(LocalDate startDate, LocalDate endDate) {
        return punishmentDetailsRepository.findByDateBetween(startDate, endDate);
    }

    @Override
    public List<PunishmentDetails> searchPunishmentDetailsByCaseDetails(String keyword) {
        return punishmentDetailsRepository.findByCaseDetailsContaining(keyword);
    }

    @Override
    public List<PunishmentDetails> getAllPunishmentDetailsOrderByDate() {
        return punishmentDetailsRepository.findByOrderByDateDesc();
    }

    @Override
    public List<PunishmentDetails> getPunishmentDetailsByTypeOrderByDate(String punishmenttype) {
        return punishmentDetailsRepository.findByPunishmenttypeOrderByDateDesc(punishmenttype);
    }
}
