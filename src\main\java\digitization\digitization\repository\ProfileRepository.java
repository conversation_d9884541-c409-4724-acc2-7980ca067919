package digitization.digitization.repository;

import digitization.digitization.module.Profile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ProfileRepository extends JpaRepository<Profile, Long> {
    Optional<Profile> findById(Long id);
    Optional<Profile> findByEmail(String email);
    // Note: Profile entity needs employee relationship for this to work
     Optional<Profile> findByEmployeeId(Long employeeId);
}
