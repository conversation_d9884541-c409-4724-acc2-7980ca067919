package digitization.digitization.response.responce;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;

public class ServerRoot {
    @JsonProperty("result")
    public ArrayList<ServerResult> result;
    @JsonProperty("code")

    public int code;
    @JsonProperty("message")

    public String message;
    @JsonProperty("status")

    public int status;
    @JsonProperty("description")

    public String description;
    @JsonProperty("uri")

    public String uri;




    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public ArrayList<ServerResult> getResult() {
        return result;
    }

    public void setResult(ArrayList<ServerResult> result) {
        this.result = result;
    }
}

