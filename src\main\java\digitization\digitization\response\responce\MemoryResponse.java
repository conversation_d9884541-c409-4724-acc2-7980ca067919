package digitization.digitization.response.responce;

public class MemoryResponse {

    private Object memoryused;
    private Object total;

    private Object memoryfree;
    private Object percentused;
    private Object percentfree;

    public MemoryResponse() {
    }

    public MemoryResponse(Object memoryused, Object memoryfree, Object percentused, Object percentfree,
                          Object total) {
        this.memoryused = memoryused;
        this.memoryfree = memoryfree;
        this.percentused = percentused;
        this.percentfree = percentfree;
        this.total =total;
    }

    public Object getMemoryused() {
        return memoryused;
    }

    public void setMemoryused(Object memoryused) {
        this.memoryused = memoryused;
    }

    public Object getMemoryfree() {
        return memoryfree;
    }

    public void setMemoryfree(Object memoryfree) {
        this.memoryfree = memoryfree;
    }

    public Object getPercentused() {
        return percentused;
    }

    public void setPercentused(Object percentused) {
        this.percentused = percentused;
    }

    public Object getPercentfree() {
        return percentfree;
    }

    public void setPercentfree(Object percentfree) {
        this.percentfree = percentfree;
    }

    public Object getTotal() {
        return total;
    }

    public void setTotal(Object total) {
        this.total = total;
    }
}

