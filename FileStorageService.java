package digitization.digitization.services;

import digitization.digitization.dto.FileStorageResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.UUID;

@Service
public class FileStorageService {

    private final Path fileStorageLocation;

    public FileStorageService(@Value("${file.upload-dir:uploads}") String uploadDir) {
        this.fileStorageLocation = Paths.get(uploadDir).toAbsolutePath().normalize();
        try {
            Files.createDirectories(this.fileStorageLocation);
        } catch (Exception ex) {
            throw new RuntimeException("Could not create the directory where the uploaded files will be stored.", ex);
        }
    }

    public FileStorageResult storeFile(MultipartFile file, Long employeeId) {
        // Validate file
        if (file.isEmpty()) {
            throw new RuntimeException("Failed to store empty file.");
        }

        // Validate file type (only PDF)
        String contentType = file.getContentType();
//        if (!"application/pdf".equals(contentType)) {
//            throw new RuntimeException("Only PDF files are allowed.");
//        }

        // Normalize file name
        String fileName = StringUtils.cleanPath(file.getOriginalFilename());

        try {
            // Check if the file's name contains invalid characters
            if (fileName.contains("..")) {
                throw new RuntimeException("Sorry! Filename contains invalid path sequence " + fileName);
            }

            // Create employee-specific directory
            Path employeeDir = this.fileStorageLocation.resolve("employee_" + employeeId);
            Files.createDirectories(employeeDir);

            // Generate unique filename with timestamp
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String uniqueId = UUID.randomUUID().toString().substring(0, 8);
            String fileExtension = getFileExtension(fileName);
            String uniqueFileName = timestamp + "_" + uniqueId + fileExtension;

            // Copy file to the target location (Replacing existing file with the same name)
            Path targetLocation = employeeDir.resolve(uniqueFileName);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);

            // Create file storage result with both path and URL
            String relativePath = "employee_" + employeeId + "/" + uniqueFileName;
            String fileUrl = "/api/files/" + relativePath;
            String absolutePath = targetLocation.toString();

            return new FileStorageResult(relativePath, fileUrl, absolutePath);
        } catch (IOException ex) {
            throw new RuntimeException("Could not store file " + fileName + ". Please try again!", ex);
        }
    }

    public Resource loadFileAsResource(String fileName) {
        try {
            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            if (resource.exists()) {
                return resource;
            } else {
                throw new RuntimeException("File not found " + fileName);
            }
        } catch (MalformedURLException ex) {
            throw new RuntimeException("File not found " + fileName, ex);
        }
    }

    public boolean deleteFile(String fileName) {
        try {
            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
            return Files.deleteIfExists(filePath);
        } catch (IOException ex) {
            throw new RuntimeException("Could not delete file " + fileName, ex);
        }
    }

    public Path getFileStorageLocation() {
        return fileStorageLocation;
    }

    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf("."));
    }

    public String getFullFilePath(String relativePath) {
        return this.fileStorageLocation.resolve(relativePath).toString();
    }

    public FileStorageResult storeFileFromBase64(String base64Content, String fileName, String fileType, Long employeeId) {
        try {
            // Validate file type (only PDF)
            if (!"application/pdf".equals(fileType)) {
                throw new RuntimeException("Only PDF files are allowed.");
            }

            // Decode Base64 content
            byte[] fileBytes = Base64.getDecoder().decode(base64Content);

            // Validate file size (10MB limit)
            if (fileBytes.length > 10485760) {
                throw new RuntimeException("File size exceeds 10MB limit.");
            }

            // Normalize file name
            String cleanFileName = StringUtils.cleanPath(fileName);

            // Check if the file's name contains invalid characters
            if (cleanFileName.contains("..")) {
                throw new RuntimeException("Sorry! Filename contains invalid path sequence " + cleanFileName);
            }

            // Create employee-specific directory
            Path employeeDir = this.fileStorageLocation.resolve("employee_" + employeeId);
            Files.createDirectories(employeeDir);

            // Generate unique filename with timestamp
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String uniqueId = UUID.randomUUID().toString().substring(0, 8);
            String fileExtension = getFileExtension(cleanFileName);
            String uniqueFileName = timestamp + "_" + uniqueId + fileExtension;

            // Write file to the target location
            Path targetLocation = employeeDir.resolve(uniqueFileName);
            Files.write(targetLocation, fileBytes);

            // Create file storage result with both path and URL
            String relativePath = "employee_" + employeeId + "/" + uniqueFileName;
            String fileUrl = "/api/files/" + relativePath;
            String absolutePath = targetLocation.toString();

            return new FileStorageResult(relativePath, fileUrl, absolutePath);
        } catch (IOException ex) {
            throw new RuntimeException("Could not store file " + fileName + ". Please try again!", ex);
        }
    }
}
