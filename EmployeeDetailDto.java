package digitization.digitization.dto;

import java.time.LocalDate;
import java.util.List;

public class EmployeeDetailDto {

    // Employee basic details
    private Long id;
    private String ecpfNumber;
    private String panNumber;
    private String employeeName;
    private String email;
    private String mobileNumber;
    private String registerNumber;
    private String section;
    private String presentAddress;
    private String permanentAddress;
    private String fatherName;
    private String motherName;
    private LocalDate dateOfBirth;
    private String religion;
    private String community;
    private String caste;
    private String personalIdentificationmark1;
    private String personalIdentificationmark2;
    private String currentDesignation;
    private LocalDate dateOfEntry;
    private String district;
    private String nativePlaceAndTaluk;
    private String mainEmployeeType;
    private String profilePhoto;
    private String createdBy;

    private String remarks;

    private String approvedBy;

    private String rejectedBy;


    // Related data
    private ProfileDto profile;
    private AccountDetailsDto accountDetails;
    private List<ServiceHistoryDto> serviceHistory;
    private List<LeaveDto> leaveBalances;
    private List<EducationQualificationDto> educationQualifications;
    private List<PunishmentDetailsDto> punishmentDetails;
    private List<TrainingDetailsDto> trainingDetails;
    private List<NomineeDto> nominees;
    private List<DocumentDto> documents;
    private SalaryDetailsDto salaryDetails;

    public EmployeeDetailDto() {
    }

    // Getters and setters


    public EmployeeDetailDto(Long id, String ecpfNumber, String panNumber, String employeeName, String email, String mobileNumber, String registerNumber, String section, String presentAddress, String permanentAddress, String fatherName, String motherName, LocalDate dateOfBirth, String religion, String community, String caste, String personalIdentificationmark1, String personalIdentificationmark2, String currentDesignation, LocalDate dateOfEntry, String district, String nativePlaceAndTaluk, String profilePhoto, String createdBy, String remarks, String approvedBy, String rejectedBy, ProfileDto profile, AccountDetailsDto accountDetails, List<ServiceHistoryDto> serviceHistory, List<LeaveDto> leaveBalances, List<EducationQualificationDto> educationQualifications, List<PunishmentDetailsDto> punishmentDetails, List<TrainingDetailsDto> trainingDetails, List<NomineeDto> nominees, List<DocumentDto> documents, SalaryDetailsDto salaryDetails) {
        this.id = id;
        this.ecpfNumber = ecpfNumber;
        this.panNumber = panNumber;
        this.employeeName = employeeName;
        this.email = email;
        this.mobileNumber = mobileNumber;
        this.registerNumber = registerNumber;
        this.section = section;
        this.fatherName = fatherName;
        this.motherName = motherName;
        this.dateOfBirth = dateOfBirth;
        this.religion = religion;
        this.community = community;
        this.caste = caste;
        this.personalIdentificationmark1 = personalIdentificationmark1;
        this.personalIdentificationmark2 = personalIdentificationmark2;
        this.currentDesignation = currentDesignation;
        this.dateOfEntry = dateOfEntry;
        this.district = district;
        this.nativePlaceAndTaluk = nativePlaceAndTaluk;
        this.profilePhoto = profilePhoto;
        this.createdBy = createdBy;
        this.remarks = remarks;
        this.approvedBy = approvedBy;
        this.rejectedBy = rejectedBy;
        this.profile = profile;
        this.accountDetails = accountDetails;
        this.serviceHistory = serviceHistory;
        this.leaveBalances = leaveBalances;
        this.educationQualifications = educationQualifications;
        this.punishmentDetails = punishmentDetails;
        this.trainingDetails = trainingDetails;
        this.nominees = nominees;
        this.documents = documents;
        this.salaryDetails = salaryDetails;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEcpfNumber() {
        return ecpfNumber;
    }

    public void setEcpfNumber(String ecpfNumber) {
        this.ecpfNumber = ecpfNumber;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getRegisterNumber() {
        return registerNumber;
    }

    public void setRegisterNumber(String registerNumber) {
        this.registerNumber = registerNumber;
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getPresentAddress() {
        return presentAddress;
    }

    public void setPresentAddress(String presentAddress) {
        this.presentAddress = presentAddress;
    }

    public String getPermanentAddress() {
        return permanentAddress;
    }

    public void setPermanentAddress(String permanentAddress) {
        this.permanentAddress = permanentAddress;
    }



    public String getFatherName() {
        return fatherName;
    }

    public void setFatherName(String fatherName) {
        this.fatherName = fatherName;
    }

    public String getMotherName() {
        return motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getReligion() {
        return religion;
    }

    public void setReligion(String religion) {
        this.religion = religion;
    }

    public String getCommunity() {
        return community;
    }

    public void setCommunity(String community) {
        this.community = community;
    }

    public String getCaste() {
        return caste;
    }

    public void setCaste(String caste) {
        this.caste = caste;
    }

    public String getPersonalIdentificationmark1() {
        return personalIdentificationmark1;
    }

    public void setPersonalIdentificationmark1(String personalIdentificationmark1) {
        this.personalIdentificationmark1 = personalIdentificationmark1;
    }

    public String getPersonalIdentificationmark2() {
        return personalIdentificationmark2;
    }

    public void setPersonalIdentificationmark2(String personalIdentificationmark2) {
        this.personalIdentificationmark2 = personalIdentificationmark2;
    }

    public String getCurrentDesignation() {
        return currentDesignation;
    }

    public void setCurrentDesignation(String currentDesignation) {
        this.currentDesignation = currentDesignation;
    }

    public LocalDate getDateOfEntry() {
        return dateOfEntry;
    }

    public void setDateOfEntry(LocalDate dateOfEntry) {
        this.dateOfEntry = dateOfEntry;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getNativePlaceAndTaluk() {
        return nativePlaceAndTaluk;
    }

    public void setNativePlaceAndTaluk(String nativePlaceAndTaluk) {
        this.nativePlaceAndTaluk = nativePlaceAndTaluk;
    }

    public String getMainEmployeeType() {
        return mainEmployeeType;
    }

    public void setMainEmployeeType(String mainEmployeeType) {
        this.mainEmployeeType = mainEmployeeType;
    }

    public String getProfilePhoto() {
        return profilePhoto;
    }

    public void setProfilePhoto(String profilePhoto) {
        this.profilePhoto = profilePhoto;
    }


    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public String getRejectedBy() {
        return rejectedBy;
    }

    public void setRejectedBy(String rejectedBy) {
        this.rejectedBy = rejectedBy;
    }

    public ProfileDto getProfile() {
        return profile;
    }

    public void setProfile(ProfileDto profile) {
        this.profile = profile;
    }

    public AccountDetailsDto getAccountDetails() {
        return accountDetails;
    }

    public void setAccountDetails(AccountDetailsDto accountDetails) {
        this.accountDetails = accountDetails;
    }

    public List<ServiceHistoryDto> getServiceHistory() {
        return serviceHistory;
    }

    public void setServiceHistory(List<ServiceHistoryDto> serviceHistory) {
        this.serviceHistory = serviceHistory;
    }

    public List<LeaveDto> getLeaveBalances() {
        return leaveBalances;
    }

    public void setLeaveBalances(List<LeaveDto> leaveBalances) {
        this.leaveBalances = leaveBalances;
    }

    public List<EducationQualificationDto> getEducationQualifications() {
        return educationQualifications;
    }

    public void setEducationQualifications(List<EducationQualificationDto> educationQualifications) {
        this.educationQualifications = educationQualifications;
    }

    public List<PunishmentDetailsDto> getPunishmentDetails() {
        return punishmentDetails;
    }

    public void setPunishmentDetails(List<PunishmentDetailsDto> punishmentDetails) {
        this.punishmentDetails = punishmentDetails;
    }

    public List<TrainingDetailsDto> getTrainingDetails() {
        return trainingDetails;
    }

    public void setTrainingDetails(List<TrainingDetailsDto> trainingDetails) {
        this.trainingDetails = trainingDetails;
    }

    public List<NomineeDto> getNominees() {
        return nominees;
    }

    public void setNominees(List<NomineeDto> nominees) {
        this.nominees = nominees;
    }

    public List<DocumentDto> getDocuments() {
        return documents;
    }

    public void setDocuments(List<DocumentDto> documents) {
        this.documents = documents;
    }

    public SalaryDetailsDto getSalaryDetails() {
        return salaryDetails;
    }

    public void setSalaryDetails(SalaryDetailsDto salaryDetails) {
        this.salaryDetails = salaryDetails;
    }
}
