package digitization.digitization.controller;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.EducationQualification;
import digitization.digitization.services.EducationQualificationService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("educationqualification")
public class EducationQualificationController {
    
    @Autowired
    EducationQualificationService educationQualificationService;

    @GetMapping("/getlist")
    public List<EducationQualification> getEducationQualificationList() {
        return educationQualificationService.getEducationQualificationList();
    }

    @GetMapping("/getEducationQualification/{id}")
    public ResponseEntity<EducationQualification> getEducationQualificationById(@PathVariable Long id) throws Exception {
        EducationQualification educationQualification = educationQualificationService.getEducationQualificationById(id);

        if (educationQualification == null) {
            throw new ResourceNotFoundException("Education qualification not found with id: " + id, null, null);
        }

        return ResponseEntity.ok(educationQualification);
    }

    @GetMapping("/getByQualification/{qualification}")
    public List<EducationQualification> getEducationQualificationsByQualification(@PathVariable String qualification) {
        return educationQualificationService.getEducationQualificationsByQualification(qualification);
    }

    @GetMapping("/getByCourse/{coursename}")
    public List<EducationQualification> getEducationQualificationsByCourseName(@PathVariable String coursename) {
        return educationQualificationService.getEducationQualificationsByCourseName(coursename);
    }

    @GetMapping("/getBySchool/{schoolname}")
    public List<EducationQualification> getEducationQualificationsBySchoolName(@PathVariable String schoolname) {
        return educationQualificationService.getEducationQualificationsBySchoolName(schoolname);
    }

    @GetMapping("/getByCollege/{collegename}")
    public List<EducationQualification> getEducationQualificationsByCollegeName(@PathVariable String collegename) {
        return educationQualificationService.getEducationQualificationsByCollegeName(collegename);
    }

    @GetMapping("/getByUniversity/{universityname}")
    public List<EducationQualification> getEducationQualificationsByUniversityName(@PathVariable String universityname) {
        return educationQualificationService.getEducationQualificationsByUniversityName(universityname);
    }

    @PostMapping("/createEducationQualification")
    public EducationQualification createEducationQualification(@Valid @RequestBody EducationQualification educationQualification) {
        return educationQualificationService.createEducationQualification(educationQualification);
    }

    @PutMapping("/updateEducationQualification/{id}")
    public EducationQualification updateEducationQualification(@PathVariable Long id, @Valid @RequestBody EducationQualification educationQualificationDetails) {
        return educationQualificationService.updateEducationQualification(id, educationQualificationDetails);
    }

    @DeleteMapping("/deleteEducationQualification/{id}")
    public ResponseEntity<?> deleteEducationQualification(@PathVariable Long id) {
        boolean deleted = educationQualificationService.deleteEducationQualification(id);
        if (deleted) {
            return ResponseEntity.ok().build();
        } else {
            throw new ResourceNotFoundException("Education qualification not found with id: " + id, null, null);
        }
    }
}
