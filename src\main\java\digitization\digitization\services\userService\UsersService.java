package digitization.digitization.services.userService;

import digitization.digitization.common.DTRequestBean;
import digitization.digitization.common.DTResponseBean;
import digitization.digitization.common.Pageable;
import digitization.digitization.dto.userDto.UserDto;
import digitization.digitization.module.User;
import digitization.digitization.response.responce.ApiResponce;
import digitization.digitization.response.responce.SuccessResponce;
import org.springframework.stereotype.Service;

@Service
public interface UsersService {
    User save(UserDto userDto);

    Boolean isUserNameExists(String userName);

    Boolean isMobileExists(String mobile);

    Boolean isEmailExists(String email);

    ApiResponce list();

    ApiResponce listByRole();

    ApiResponce listOfPage(Pageable pageable);

    UserDto viewById(Long id);

    DTResponseBean listByDTRequest(DTRequestBean dtRequestBean);

    User userUpdate(UserDto userDto, Long id);

    SuccessResponce deleteById(long id);

}
