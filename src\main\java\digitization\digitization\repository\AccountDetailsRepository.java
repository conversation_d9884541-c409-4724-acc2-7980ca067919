package digitization.digitization.repository;

import digitization.digitization.module.AccountDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface AccountDetailsRepository extends JpaRepository<AccountDetails, Long> {
    Optional<AccountDetails> findById(Long id);
    Optional<AccountDetails> findByBankaccountnumber(String bankaccountnumber);
    Optional<AccountDetails> findByAadharnumber(String aadharnumber);
    Optional<AccountDetails> findByUannumber(String uannumber);
    Optional<AccountDetails> findByEmployeeId(Long employeeId);
}
