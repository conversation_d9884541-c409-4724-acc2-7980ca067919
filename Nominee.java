package digitization.digitization.module;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

@Entity
@Table(name = "nominee")
public class Nominee {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Nominee name is required")
    private String nomineename;

    @NotBlank(message = "Address is required")
    @Column(columnDefinition = "TEXT")
    private String address;

    @NotBlank(message = "Relationship is required")
    private String relationship;

    @NotNull(message = "Age is required")
    @Min(value = 0, message = "Age must be a positive number")
    @Max(value = 150, message = "Age must be realistic")
    private Integer age;

    @NotNull(message = "Percentage of share is required")
    @DecimalMin(value = "0.0", message = "Percentage of share must be positive")
    @DecimalMax(value = "100.0", message = "Percentage of share cannot exceed 100%")
    private Double percentageofshare;

    @NotBlank(message = "Gender is required")
    @Pattern(regexp = "^(Male|Female|Other)$", message = "Gender must be Male, Female, or Other")
    private String gender;

    @Column(name = "nominee_photo")
    private String nomineePhoto;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "employee_id")
    private Employee employee;

    public Nominee() {
    }

    public Nominee(Long id, String nomineename, String address, String relationship, Integer age, Double percentageofshare, String gender) {
        this.id = id;
        this.nomineename = nomineename;
        this.address = address;
        this.relationship = relationship;
        this.age = age;
        this.percentageofshare = percentageofshare;
        this.gender = gender;
    }

    public Nominee(Long id, String nomineename, String address, String relationship, Integer age, Double percentageofshare, String gender, String nomineePhoto) {
        this.id = id;
        this.nomineename = nomineename;
        this.address = address;
        this.relationship = relationship;
        this.age = age;
        this.percentageofshare = percentageofshare;
        this.gender = gender;
        this.nomineePhoto = nomineePhoto;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNomineename() {
        return nomineename;
    }

    public void setNomineename(String nomineename) {
        this.nomineename = nomineename;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Double getPercentageofshare() {
        return percentageofshare;
    }

    public void setPercentageofshare(Double percentageofshare) {
        this.percentageofshare = percentageofshare;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getNomineePhoto() {
        return nomineePhoto;
    }

    public void setNomineePhoto(String nomineePhoto) {
        this.nomineePhoto = nomineePhoto;
    }

    public Employee getEmployee() {
        return employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }
}
