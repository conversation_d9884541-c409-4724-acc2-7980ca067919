package digitization.digitization.services.implementation;

import digitization.digitization.module.AccountDetails;

import java.util.List;

public interface AccountDetailsServ {
    List<AccountDetails> getAccountDetailsList();
    AccountDetails getAccountDetailsById(Long id);
    AccountDetails createAccountDetails(AccountDetails accountDetails);
    AccountDetails updateAccountDetails(Long id, AccountDetails accountDetailsDetails);
    boolean deleteAccountDetails(Long accountDetailsId);
    AccountDetails getAccountDetailsByBankAccountNumber(String bankaccountnumber);
    AccountDetails getAccountDetailsByAadharNumber(String aadharnumber);
    AccountDetails getAccountDetailsByUanNumber(String uannumber);
}
