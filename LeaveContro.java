package digitization.digitization.controller;


import digitization.digitization.module.Leave;
import digitization.digitization.services.LeaveService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("leave")
public class LeaveContro {
    @Autowired
    private LeaveService leaveBalanceService;

    @PostMapping("/addLeave/{employeeId}")
    public Leave addLeaveBalance(@PathVariable Long employeeId, @Valid @RequestBody Leave leaveBalance) {
        return leaveBalanceService.addLeaveBalance(employeeId, leaveBalance);
    }

    @GetMapping("/employee/{employeeId}")
    public List<Leave> getLeaveBalancesByEmployee(@PathVariable Long employeeId) {
        return leaveBalanceService.getLeaveBalancesByEmployeeId(employeeId);
    }

    @GetMapping("/getlist")
    public List<Leave> getAllLeaveBalances() {
        return leaveBalanceService.getAllLeaveBalances();
    }

    @GetMapping("/getLeave/{id}")
    public ResponseEntity<Leave> getLeaveBalanceById(@PathVariable Long id) {
        Leave leave = leaveBalanceService.getLeaveBalanceById(id);
        return ResponseEntity.ok(leave);
    }

    @PutMapping("/updateLeave/{id}")
    public Leave updateLeaveBalance(@PathVariable Long id, @Valid @RequestBody Leave leaveBalanceDetails) {
        return leaveBalanceService.updateLeaveBalance(id, leaveBalanceDetails);
    }

    @DeleteMapping("/delLeave/{id}")
    public ResponseEntity<?> deleteLeaveBalance(@PathVariable Long id) {
        leaveBalanceService.deleteLeaveBalance(id);
        return ResponseEntity.ok().build();
    }

}
