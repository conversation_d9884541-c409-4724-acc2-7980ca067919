package digitization.digitization.repository;

import digitization.digitization.module.Employee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EmployeeRepo extends JpaRepository<Employee,Long> {
    Optional<Employee> findById(Long id);
    List<Employee> findAllByOrderByIdDesc();
    @Query("SELECT COUNT(e) FROM Employee e")
    Long getEmployeeCount();


    @Query(value = "SELECT e.* FROM employee e INNER JOIN employee_status es ON e.id = es.emp_id WHERE es.status = :status ORDER BY es.updated_at DESC", nativeQuery = true)
    List<Employee> findEmployeesByStatus(@Param("status") String status);

    Optional<Employee> findByEmpIdAndPanNumber(String empId, String panNumber);
    Optional<Employee> findByPanNumber(String panNumber);

    List<Employee> findByCreatedByOrderByIdDesc(String createdBy);

}
