package digitization.digitization.common;

import java.util.Map;
import java.util.Set;

public class ApiResponseBean {
    private String responseType;
    private Object responseData;
    private Object userData;
    private Object role;
    private Map<String, Set<String>> validationErrors;
    private String errorMessage;


    public String getResponseType() {
        return responseType;
    }

    public void setResponseType(String responseType) {
        this.responseType = responseType;
    }

    public Object getResponseData() {
        return responseData;
    }

    public void setResponseData(Object responseData) {
        this.responseData = responseData;
    }

    public Map<String, Set<String>> getValidationErrors() {
        return validationErrors;
    }

    public void setValidationErrors(Map<String, Set<String>> validationErrors) {
        this.validationErrors = validationErrors;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Object getUserData() {
        return userData;
    }

    public void setUserData(Object userData) {
        this.userData = userData;
    }

    public Object getRole() {
        return role;
    }

    public void setRole(Object role) {
        this.role = role;
    }
}
