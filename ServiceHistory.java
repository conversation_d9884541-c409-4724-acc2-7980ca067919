package digitization.digitization.module;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;

@Entity
@Table(name="service_history")
public class ServiceHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

//    @NotNull(message = "Date is required")
    private LocalDate date;

//    @NotBlank(message = "Type is required")
    private String type;

//    @NotBlank(message = "Status is required")
    private String status;

    private String appointmenttype;

    private String modeofappointment;

    private LocalDate dateofappointment;

    private String proceedingorderno;

    private LocalDate proceedingorderdate;

    private LocalDate joiningdate;

    private LocalDate promoteddate;

    private String fromdesignation;

    private String topromoted;

    private LocalDate fromdate;

    private LocalDate todate;

    private String typeofincrement;

    private String fromplace;

    private String toplace;

    private String designation;

    private String originaldesignation;

    private String parentdepartment;

    private String punishmenttype;

    private String casedetails;

    private LocalDate punishmentdate;

    @ManyToOne
    @JoinColumn(name = "employee_id")
    private Employee employee;

    public ServiceHistory() {
    }

    public ServiceHistory(Long id, LocalDate date, String type, String status, String appointmenttype, String modeofappointment, LocalDate dateofappointment, LocalDate proceedingorderdate, LocalDate joiningdate, LocalDate promoteddate, String fromdesignation, String topromoted, LocalDate fromdate, LocalDate todate, String typeofincrement, String fromplace, String toplace, String designation, String originaldesignation, String parentdepartment, Employee employee, String proceedingorderno) {
        this.id = id;
        this.date = date;
        this.type = type;
        this.status = status;
        this.appointmenttype = appointmenttype;
        this.modeofappointment = modeofappointment;
        this.dateofappointment = dateofappointment;
        this.proceedingorderdate = proceedingorderdate;
        this.joiningdate = joiningdate;
        this.promoteddate = promoteddate;
        this.fromdesignation = fromdesignation;
        this.topromoted = topromoted;
        this.fromdate = fromdate;
        this.todate = todate;
        this.typeofincrement = typeofincrement;
        this.fromplace = fromplace;
        this.toplace = toplace;
        this.designation = designation;
        this.originaldesignation = originaldesignation;
        this.parentdepartment = parentdepartment;
        this.employee = employee;
        this.proceedingorderno = proceedingorderno;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAppointmenttype() {
        return appointmenttype;
    }

    public void setAppointmenttype(String appointmenttype) {
        this.appointmenttype = appointmenttype;
    }

    public String getModeofappointment() {
        return modeofappointment;
    }

    public void setModeofappointment(String modeofappointment) {
        this.modeofappointment = modeofappointment;
    }

    public LocalDate getDateofappointment() {
        return dateofappointment;
    }

    public void setDateofappointment(LocalDate dateofappointment) {
        this.dateofappointment = dateofappointment;
    }

    public LocalDate getProceedingorderdate() {
        return proceedingorderdate;
    }

    public void setProceedingorderdate(LocalDate proceedingorderdate) {
        this.proceedingorderdate = proceedingorderdate;
    }

    public LocalDate getJoiningdate() {
        return joiningdate;
    }

    public void setJoiningdate(LocalDate joiningdate) {
        this.joiningdate = joiningdate;
    }

    public LocalDate getPromoteddate() {
        return promoteddate;
    }

    public void setPromoteddate(LocalDate promoteddate) {
        this.promoteddate = promoteddate;
    }

    public String getFromdesignation() {
        return fromdesignation;
    }

    public void setFromdesignation(String fromdesignation) {
        this.fromdesignation = fromdesignation;
    }

    public String getTopromoted() {
        return topromoted;
    }

    public void setTopromoted(String topromoted) {
        this.topromoted = topromoted;
    }

    public LocalDate getFromdate() {
        return fromdate;
    }

    public void setFromdate(LocalDate fromdate) {
        this.fromdate = fromdate;
    }

    public LocalDate getTodate() {
        return todate;
    }

    public void setTodate(LocalDate todate) {
        this.todate = todate;
    }

    public String getTypeofincrement() {
        return typeofincrement;
    }

    public void setTypeofincrement(String typeofincrement) {
        this.typeofincrement = typeofincrement;
    }

    public String getFromplace() {
        return fromplace;
    }

    public void setFromplace(String fromplace) {
        this.fromplace = fromplace;
    }

    public String getToplace() {
        return toplace;
    }

    public void setToplace(String toplace) {
        this.toplace = toplace;
    }

    public String getDesignation() {
        return designation;
    }

    public void setDesignation(String designation) {
        this.designation = designation;
    }

    public String getOriginaldesignation() {
        return originaldesignation;
    }

    public void setOriginaldesignation(String originaldesignation) {
        this.originaldesignation = originaldesignation;
    }

    public String getParentdepartment() {
        return parentdepartment;
    }

    public void setParentdepartment(String parentdepartment) {
        this.parentdepartment = parentdepartment;
    }

    public Employee getEmployee() {
        return employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }

    public String getProceedingorderno() {
        return proceedingorderno;
    }

    public void setProceedingorderno(String proceedingorderno) {
        this.proceedingorderno = proceedingorderno;
    }

    public String getPunishmenttype() {
        return punishmenttype;
    }

    public void setPunishmenttype(String punishmenttype) {
        this.punishmenttype = punishmenttype;
    }

    public String getCasedetails() {
        return casedetails;
    }

    public void setCasedetails(String casedetails) {
        this.casedetails = casedetails;
    }

    public LocalDate getPunishmentdate() {
        return punishmentdate;
    }

    public void setPunishmentdate(LocalDate punishmentdate) {
        this.punishmentdate = punishmentdate;
    }
}
