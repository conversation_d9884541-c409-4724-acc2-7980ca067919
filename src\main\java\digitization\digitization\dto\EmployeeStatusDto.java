package digitization.digitization.dto;

import java.time.LocalDateTime;

public class EmployeeStatusDto {

    private Long id;
    private Long empId;
    private String status;
    private String approvedBy;
    private String rejectedBy;
    private String remarks;
    private String createdBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public EmployeeStatusDto() {
    }

    public EmployeeStatusDto(Long id, Long empId, String status) {
        this.id = id;
        this.empId = empId;
        this.status = status;
    }

    public EmployeeStatusDto(Long id, Long empId, String status, String approvedBy, String rejectedBy, String remarks, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.empId = empId;
        this.status = status;
        this.approvedBy = approvedBy;
        this.rejectedBy = rejectedBy;
        this.remarks = remarks;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public EmployeeStatusDto(Long id, Long empId, String status, String approvedBy, String rejectedBy, String remarks, String createdBy, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.empId = empId;
        this.status = status;
        this.approvedBy = approvedBy;
        this.rejectedBy = rejectedBy;
        this.remarks = remarks;
        this.createdBy = createdBy;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEmpId() {
        return empId;
    }

    public void setEmpId(Long empId) {
        this.empId = empId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public String getRejectedBy() {
        return rejectedBy;
    }

    public void setRejectedBy(String rejectedBy) {
        this.rejectedBy = rejectedBy;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "EmployeeStatusDto{" +
                "id=" + id +
                ", empId='" + empId + '\'' +
                ", status='" + status + '\'' +
                ", approvedBy='" + approvedBy + '\'' +
                ", rejectedBy='" + rejectedBy + '\'' +
                ", remarks='" + remarks + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
