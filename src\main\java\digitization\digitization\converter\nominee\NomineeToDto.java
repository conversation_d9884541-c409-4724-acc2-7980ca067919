package digitization.digitization.converter.nominee;

import digitization.digitization.dto.NomineeDto;
import digitization.digitization.module.Nominee;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class NomineeToDto implements Converter<Nominee, NomineeDto> {
    @Override
    public NomineeDto convert(Nominee nominee) {
        NomineeDto nomineeDto = new NomineeDto();
        nomineeDto.setId(nominee.getId());
        nomineeDto.setNomineename(nominee.getNomineename());
        nomineeDto.setAddress(nominee.getAddress());
        nomineeDto.setRelationship(nominee.getRelationship());
        nomineeDto.setAge(nominee.getAge());
        nomineeDto.setPercentageofshare(nominee.getPercentageofshare());
        nomineeDto.setGender(nominee.getGender());
        return nomineeDto;
    }
}
