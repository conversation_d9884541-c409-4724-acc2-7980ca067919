package digitization.digitization.exception;

public class UnAuthException extends RuntimeException{

    private boolean status;
    private String message;
    private String info;


    public UnAuthException(boolean status, String message, String info){
        super();
        this.status = status;
        this.message = message;
        this.info = info;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
