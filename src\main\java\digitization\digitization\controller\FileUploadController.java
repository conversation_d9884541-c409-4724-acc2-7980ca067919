package digitization.digitization.controller;

import digitization.digitization.dto.ApiResponce;
import digitization.digitization.dto.FileResponse;
import digitization.digitization.services.FileUploadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
public class FileUploadController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private FileUploadService fileUploadService;

    @PostMapping(value="/file/upload")
    public ApiResponce uploadFile(@ModelAttribute MultipartFile file){
        ApiResponce responce = new ApiResponce();
        logger.info("File Upload "+file.getOriginalFilename());
        String filename = file.getOriginalFilename();
        String extension = filename.substring(filename.lastIndexOf(".") + 1, filename.length());

        String excel = "xls";

        String jpeg = "jpeg";
        String jpg = "jpg";
        String png = "png";
        String mp4 = "mp4";
        String mp3 = "mp3";
        String mpeg = "mpeg";

        if (extension.equalsIgnoreCase(excel)
                || extension.equalsIgnoreCase(jpeg)
                || extension.equalsIgnoreCase(jpg)
                || extension.equalsIgnoreCase(png)
                || extension.equalsIgnoreCase(mp4)
                || extension.equalsIgnoreCase(mp3)
                || extension.equalsIgnoreCase(mpeg)) {
            FileResponse result = null;
            if (!file.isEmpty()) {
                result = fileUploadService.upload(file);
            }

            responce.setStatus(true);
            responce.setData(result);
            return responce;
        }
        else {
            responce.setStatus(false);
            responce.setData("You  only allow excel file format csv,xls,xlsx");
            return responce;


        }
    }

}
