package digitization.digitization.auth;

import digitization.digitization.common.ApiResponseBean;
import digitization.digitization.common.HttpRequestExtractor;
import digitization.digitization.dto.authDto.AuthDto;
import digitization.digitization.dto.authDto.EmployeeAuthDto;
import digitization.digitization.enums.Roles;
import digitization.digitization.module.Employee;
import digitization.digitization.module.Role;
import digitization.digitization.module.User;
import digitization.digitization.repository.EmployeeRepo;
import digitization.digitization.repository.UserRepository;
import digitization.digitization.security.TokenUtilityService;
import digitization.digitization.services.EmployeeService;
import digitization.digitization.services.EmployeeStatusService;
import digitization.digitization.module.EmployeeStatus;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.security.core.userdetails.UserDetails;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class AuthServiceImpl implements AuthService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmployeeRepo employeeRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private HttpRequestExtractor httpRequestExtractor;
    @Autowired
    private TokenUtilityService tokenUtilityService;
    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private EmployeeStatusService employeeStatusService;



    @Override
    public ApiResponseBean authenticate(AuthDto authDto, HttpServletRequest request) {

        ApiResponseBean  apiResponseBean = new ApiResponseBean();
        Optional<User> userOptional = userRepository.findByUsername(authDto.getUsername());

        if (!userOptional.isPresent()) {
            apiResponseBean.setResponseType("INVALID_USERNAME");
            return apiResponseBean;
        }
        User user = userOptional.get();

        if (!passwordEncoder.matches(authDto.getPassword(), user.getPassword())) {

            apiResponseBean.setResponseType("INVALID_PASSWORD");
            String ip = httpRequestExtractor.getClientIp();
            User updatedUser =   userRepository.save(user);
            return apiResponseBean;
        }

        List<Roles> roleNames = new ArrayList<>();
        List<String> roleIdList = new ArrayList<>();


        Boolean isAdmin =Boolean.FALSE;
        Boolean isSalesManager = Boolean.FALSE;

        int count = 0;
        for (Role role : user.getRole()) {
            roleNames.add(role.getRole());
            roleIdList.add(role.getId().toString());
            if (role.getRole().equals(0)) {
                isAdmin = Boolean.TRUE;
            }
            if (role.getRole().equals(1)) {
                isSalesManager = Boolean.TRUE;
            }

            count++;
        }

//    if(isAdmin == null && isSalesManager == null) {
//        Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(authDto.getUsername(), authDto.getPassword()));
//        SecurityContextHolder.getContext().setAuthentication(authentication);
//        String userAgent = request.getHeader("user-agent");
//        String jwtToken = tokenUtilityService.genarateAuthToken(authentication, userAgent);
//        String ip = httpRequestExtractor.getClientIp();
//        LocalDateTime lastLogin = LocalDateTime.now();
//
//        User updatedUser = userRepository.save(user);
//        apiResponseBean.setResponseType("SUCCESS");
//        apiResponseBean.setResponseData(jwtToken);
//            apiResponseBean.setUserData(updatedUser.getUserId());
//        apiResponseBean.setUserData(updatedUser);
//            apiResponseBean.setRole(updatedUser.getRole().get(0).getRole());
//
//    }

        Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(authDto.getUsername(), authDto.getPassword()));
        SecurityContextHolder.getContext().setAuthentication(authentication);
        String userAgent = request.getHeader("user-agent");
        String jwtToken = tokenUtilityService.genarateAuthToken(authentication, userAgent);
        String ip = httpRequestExtractor.getClientIp();
        LocalDateTime lastLogin = LocalDateTime.now();
//            loginHandler.success(ip);

        User updatedUser = userRepository.save(user);
        apiResponseBean.setResponseType("SUCCESS");
        apiResponseBean.setResponseData(jwtToken);
        apiResponseBean.setUserData(updatedUser.getUserId());
        apiResponseBean.setRole(updatedUser.getRole().get(0).getRole());
        System.out.println(updatedUser.getRole().get(0).getRole());



//        if(isSalesManager){
//            Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(authDto.getUsername(), authDto.getPassword()));
//            SecurityContextHolder.getContext().setAuthentication(authentication);
//            String userAgent = request.getHeader("user-agent");
//            String jwtToken = tokenUtilityService.genarateAuthToken(authentication, userAgent);
//            String ip = httpRequestExtractor.getClientIp();
//            LocalDateTime lastLogin = LocalDateTime.now();
////            loginHandler.success(ip);
//
//            User updatedUser = userRepository.save(user);
//            apiResponseBean.setResponseType("SUCCESS");
//            apiResponseBean.setResponseData(jwtToken);
//            apiResponseBean.setUserData(updatedUser.getUserId());
//            apiResponseBean.setRole(updatedUser.getRole().get(0).getRole());
//        }

        return apiResponseBean;
    }

    @Override
    public ApiResponseBean employeeLogin(EmployeeAuthDto token) {

        ApiResponseBean apiResponseBean = new ApiResponseBean();
        Optional<Employee> request = employeeRepository.findByEcpfNumberAndPanNumber(
                token.getEmpId(),
                token.getPanNumber()
        );

        if (request.isPresent()) {
            Employee employee = request.get();

            // Check employee status - only allow login if status is ACCEPTED_BY_DRM_MA
            try {
                EmployeeStatus employeeStatus = employeeStatusService.getEmployeeStatusByEmpId(employee.getId());

                if("ACCEPTED_BY_RM".equals(employeeStatus.getStatus())) {
//                    if(employeeStatus.getRejectedBy() == null) {
//                        apiResponseBean.setResponseType("ACCESS_DENIED");
//                        apiResponseBean.setErrorMessage("Employee status is under progress");
//                        return apiResponseBean;
//
//                    }
                    digitization.digitization.dto.EmployeeDetailDto employeeDetails =
                            employeeService.getEmployeeWithAllDetails(employee.getId());

                    apiResponseBean.setResponseType("LOGIN SUCCESS");
                    apiResponseBean.setResponseData(employeeDetails);
                    return  apiResponseBean;

                }

               else if (Objects.equals(employeeStatus.getStatus(), "APPROVED") || Objects.equals(employeeStatus.getStatus(), "approved")) {
                    apiResponseBean.setErrorMessage("Employee is already approved");
                    return apiResponseBean;
                }

                else {
                                            apiResponseBean.setResponseType("ACCESS_DENIED");
                        apiResponseBean.setErrorMessage("Employee status is under progress");
                        return apiResponseBean;

                }
                // Status is ACCEPTED_BY_DRM_MA, proceed with login
//                try {
//                    // Get complete employee data with all related information
//                    digitization.digitization.dto.EmployeeDetailDto employeeDetails =
//                            employeeService.getEmployeeWithAllDetails(employee.getId());
//
//                    apiResponseBean.setResponseType("LOGIN SUCCESS");
//                    apiResponseBean.setResponseData(employeeDetails); // return complete Employee data with all related info
//                } catch (Exception e) {
//                    // Fallback to basic employee data if detailed fetch fails
//                    System.out.println("Warning: Could not fetch complete employee details, returning basic data: " + e.getMessage());
//                    apiResponseBean.setResponseType("LOGIN SUCCESS");
//                    apiResponseBean.setResponseData(employee); // return basic Employee data as fallback
//                }

            } catch (Exception e) {
                // If employee status not found or error occurred

                System.out.println("Error checking employee status: " + e.getMessage());
                apiResponseBean.setResponseType("ACCESS_DENIED");
                apiResponseBean.setResponseData("Employee status could not be verified");
                return apiResponseBean;
            }
        } else {
            apiResponseBean.setResponseType("LOGIN FAILED");
            apiResponseBean.setResponseData("Invalid credentials");
        }

        return apiResponseBean;
    }

    public User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new RuntimeException("No authenticated user found");
        }

        String username;
        Object principal = authentication.getPrincipal();

        if (principal instanceof UserDetails) {
            username = ((UserDetails) principal).getUsername();
        } else {
            username = principal.toString();
        }

        return userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found: " + username));
    }

    /**
     * Get the username of the currently logged-in user
     * @return Username as a String
     */
    public String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            Object principal = authentication.getPrincipal();
            if (principal instanceof UserDetails) {
                return ((UserDetails) principal).getUsername();
            }
            return principal.toString();
        }
        return null;
    }


    public String getCurrentUserRole() {
        User currentUser = getCurrentUser();
        return currentUser.getRoleType();
    }


    public String getCurrentUserDistrict() {
        User currentUser = getCurrentUser();
        return currentUser.getRegion();
    }



}
