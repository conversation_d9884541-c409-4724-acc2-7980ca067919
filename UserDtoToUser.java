package digitization.digitization.converter.user;

import digitization.digitization.dto.userDto.UserDto;
import digitization.digitization.module.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
public class UserDtoToUser implements Converter<UserDto, User> {

    @Autowired
    private PasswordEncoder passwordEncoder;
    @Override
    public User convert(UserDto userDto) {
        User user = new User();
        user.setId(userDto.getId());
        user.setPassword(passwordEncoder.encode(userDto.getPassword()));
        user.setConfirmPassword(passwordEncoder.encode(userDto.getConfirmPassword()));
        user.setName(userDto.getName());
        user.setUsername(userDto.getUsername());
        user.setEmail(userDto.getEmail().trim());
        user.setMobile(userDto.getMobile().trim());
        user.setActive(userDto.isActive());
        user.setUserId(userDto.getUserId());
        user.setCreatedAt(user.getCreatedAt());
        return user;
    }
}

