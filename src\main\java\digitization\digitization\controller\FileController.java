package digitization.digitization.controller;

import digitization.digitization.services.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@CrossOrigin(value = "*")
@RequestMapping("/api/files")
public class FileController {

    @Autowired
    private FileStorageService fileStorageService;

    @GetMapping("/employee_{employeeId}/{fileName:.+}")
    public ResponseEntity<Resource> downloadFile(
            @PathVariable Long employeeId,
            @PathVariable String fileName,
            HttpServletRequest request) {

        // Check if this is a virtual nominee photo URL
        if (fileName.startsWith("nominee_")) {
            System.out.println("Handling virtual nominee photo URL: employee_" + employeeId + "/" + fileName);
            return handleVirtualNomineePhoto(fileName, request);
        }

        // Construct the file path for regular files
        String filePath = "employee_" + employeeId + "/" + fileName;

        // Load file as Resource
        Resource resource = fileStorageService.loadFileAsResource(filePath);

        // Try to determine file's content type
        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            // Could not determine file type
        }

        // Fallback to the default content type if type could not be determined
        if (contentType == null) {
            contentType = "application/octet-stream";
        }

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }

    /**
     * Handle virtual nominee photo URLs by mapping them to actual files
     */
    private ResponseEntity<Resource> handleVirtualNomineePhoto(String virtualFileName, HttpServletRequest request) {
        try {
            // Extract the hash ID from the virtual filename
            // Format: nominee_12345678.png -> 12345678
            String hashId = virtualFileName.substring("nominee_".length());
            int dotIndex = hashId.lastIndexOf('.');
            if (dotIndex > 0) {
                hashId = hashId.substring(0, dotIndex);
            }

            System.out.println("Looking for file with hash ID: " + hashId);

            // For now, we'll try to find the original file by checking common patterns
            // In a production system, you'd store this mapping in a database
            String[] possibleFiles = {
                "pi-cognitive-behavioral-assessment-practice-test (1).png",
                "echarts.png",
                "sample2.png",
                "dudu.jpg",
                "Screenshot_2018-11-12 Apply Leave.png"
            };

            for (String possibleFile : possibleFiles) {
                String testHashId = generateConsistentId(possibleFile);
                if (testHashId.equals(hashId)) {
                    System.out.println("Found matching file: " + possibleFile + " for hash: " + hashId);

                    // Load the actual file
                    Resource resource = fileStorageService.loadFileAsResource(possibleFile);

                    // Try to determine file's content type
                    String contentType = null;
                    try {
                        contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
                    } catch (IOException ex) {
                        // Could not determine file type
                    }

                    // Fallback to the default content type if type could not be determined
                    if (contentType == null) {
                        contentType = "application/octet-stream";
                    }

                    return ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(contentType))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                            .body(resource);
                }
            }

            System.out.println("No matching file found for hash ID: " + hashId);
            // If no match found, try to load the virtual file directly (might exist)
            Resource resource = fileStorageService.loadFileAsResource(virtualFileName);
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType("application/octet-stream"))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                    .body(resource);

        } catch (Exception e) {
            System.out.println("Error handling virtual nominee photo: " + e.getMessage());
            throw new RuntimeException("File not found " + virtualFileName);
        }
    }

    /**
     * Generate a consistent ID from filename (same logic as in EmployeeService)
     */
    private String generateConsistentId(String filename) {
        try {
            // Use hashCode to generate consistent ID from filename
            int hash = filename.hashCode();
            // Convert to positive hex string
            String hashStr = Integer.toHexString(Math.abs(hash));
            // Ensure it's 8 characters like UUID format
            return String.format("%8s", hashStr).replace(' ', '0').substring(0, 8);
        } catch (Exception e) {
            // Fallback to simple timestamp
            return String.valueOf(System.currentTimeMillis()).substring(8);
        }
    }

    @GetMapping("/{filePath:.+}")
    public ResponseEntity<Resource> downloadFileByPath(
            @PathVariable String filePath,
            HttpServletRequest request) {

        // Load file as Resource
        Resource resource = fileStorageService.loadFileAsResource(filePath);

        // Try to determine file's content type
        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            // Could not determine file type
        }

        // Fallback to the default content type if type could not be determined
        if (contentType == null) {
            contentType = "application/octet-stream";
        }

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }

    @GetMapping("/view/employee_{employeeId}/{fileName:.+}")
    public ResponseEntity<Resource> viewFile(
            @PathVariable Long employeeId,
            @PathVariable String fileName,
            HttpServletRequest request) {

        // Construct the file path
        String filePath = "employee_" + employeeId + "/" + fileName;

        // Load file as Resource
        Resource resource = fileStorageService.loadFileAsResource(filePath);

        // For PDF files, set content type to display in browser
        String contentType = "application/pdf";

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }
}
