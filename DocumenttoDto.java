package digitization.digitization.converter.document;

import digitization.digitization.dto.DocumentDto;
import digitization.digitization.module.Document;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class DocumenttoDto implements Converter<Document, DocumentDto> {
    @Override
    public DocumentDto convert(Document document) {
        DocumentDto documentDto = new DocumentDto();
        documentDto.setId(document.getId());
        documentDto.setFileName(document.getFileName());
        documentDto.setFileType(document.getFileType());
        documentDto.setFileSize(document.getFileSize());
        documentDto.setFilePath(document.getFilePath());
        documentDto.setFileUrl(document.getFileUrl());
        documentDto.setUploadDate(document.getUploadDate());

        // Set employee information if available
        if (document.getEmployee() != null) {
            documentDto.setEmployeeId(document.getEmployee().getId());
            documentDto.setEmployeeName(document.getEmployee().getEmployeeName());
        }

        return documentDto;
    }
}
