package digitization.digitization.controller;

import digitization.digitization.auth.AuthService;
import digitization.digitization.common.ApiResponseBean;
import digitization.digitization.common.HttpRequestExtractor;
import digitization.digitization.common.SuccessHandler;
import digitization.digitization.dto.authDto.AuthDto;
import digitization.digitization.dto.authDto.EmployeeAuthDto;
import digitization.digitization.dto.userDto.UserDto;
import digitization.digitization.enums.Roles;
import digitization.digitization.module.Employee;
import digitization.digitization.module.Role;
import digitization.digitization.module.User;
import digitization.digitization.repository.EmployeeRepo;
import digitization.digitization.repository.RoleRepository;
import digitization.digitization.repository.UserRepository;
import digitization.digitization.security.TokenUtilityService;
import digitization.digitization.security.UserDetailsServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;

@RestController
@RequestMapping("auth")
public class LoginController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;


    @Autowired
    private TokenUtilityService tokenUtilityService;

    @Autowired
    private HttpRequestExtractor httpRequestExtractor;

    @Autowired
    private SuccessHandler successHandlerService;

    @Autowired
    private AuthService authService;

    @Autowired
    private EmployeeRepo employeeRepo;

    @PostMapping(value = "/login")
    public ResponseEntity adminLoginToken( @RequestBody AuthDto tokenRequestDto, HttpServletRequest request){
        ApiResponseBean apiResponseBean = new ApiResponseBean();

        ApiResponseBean token =  authService.authenticate(tokenRequestDto,request);
        apiResponseBean.setResponseType("SUCCESS");
        return ResponseEntity.status(201).body(token);

    }

    @PostMapping(value = "/login/user")
    public ResponseEntity userLogin( @RequestBody AuthDto tokenRequestDto, HttpServletRequest request){
        ApiResponseBean apiResponseBean = new ApiResponseBean();

        ApiResponseBean token =  authService.authenticate(tokenRequestDto,request);
        Optional<Employee> employee = employeeRepo.findByPanNumber(tokenRequestDto.getPassword());

        apiResponseBean.setResponseType("SUCCESS");
        return ResponseEntity.status(201).body(token);

    }

    @PostMapping(value = "/login/employee")
    public ResponseEntity<ApiResponseBean> login(@RequestBody EmployeeAuthDto token) {
        ApiResponseBean response = authService.employeeLogin(token);
        return ResponseEntity.ok(response);
    }

}
