package digitization.digitization.controller;

import digitization.digitization.auth.AuthServiceImpl;
import digitization.digitization.dto.UpdateStatusDto;
import digitization.digitization.module.Employee;
import digitization.digitization.module.EmployeeStatus;
import digitization.digitization.module.User;
import digitization.digitization.services.EmployeeStatusService;
import digitization.digitization.services.regionManagers.EmployeeListBasedOnRegionService;
import jakarta.validation.constraints.NotBlank;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("employee-list-based-on-region")
public class EmployeeListBasedOnRegionController {

    @Autowired
    private EmployeeListBasedOnRegionService employeeListBasedOnRegionService;

    @Autowired
    private EmployeeStatusService employeeStatusService;

    @Autowired
    private AuthServiceImpl authService;

//    @GetMapping("/region/pending")
//    public ResponseEntity<List<EmployeeStatus>> getPendingEmployees(
//            @RequestParam @NotBlank(message = "Status is required") String status,
//            @RequestParam @NotBlank(message = "District is required") String district) {
//        List<EmployeeStatus> pendingEmployees = employeeListBasedOnRegionService.getPendingEmployeesByRegion(status, district);
//        if (pendingEmployees.isEmpty()) {
//            return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
//        }
//        return ResponseEntity.ok(pendingEmployees);
//    }

    @GetMapping("/region/pending")
    public ResponseEntity<List<Employee>> getPendingEmployees() {
        try {
            System.out.println("=== DEBUG: Getting pending employees by region ===");

            User currentUser = authService.getCurrentUser();
            String role = currentUser.getRoleType();
            String district = currentUser.getRegion();

            System.out.println("Current user: " + currentUser.getUsername());
            System.out.println("User role: " + role);
            System.out.println("User district/region: " + district);

            if (role == null || role.trim().isEmpty()) {
                System.out.println("ERROR: User role is null or empty");
                return ResponseEntity.badRequest().body(null);
            }

            if (district == null || district.trim().isEmpty()) {
                System.out.println("ERROR: User district is null or empty");
                return ResponseEntity.badRequest().body(null);
            }

            List<Employee> pendingEmployees = employeeListBasedOnRegionService.getPendingEmployeesByDistrict(role, district);
            System.out.println("Found " + pendingEmployees.size() + " pending employees for role: " + role + ", district: " + district);

            return ResponseEntity.ok(pendingEmployees);
        } catch (Exception e) {
            System.err.println("Error in getPendingEmployees: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(null);
        }
    }

    @GetMapping("/region/approvedFromSuperandAM")
    public ResponseEntity<List<Employee>> getSuperAndAMEmployees() {
        try {
            System.out.println("=== DEBUG: Getting SUPERINTENDENT and AM approved employees ===");

            User currentUser = authService.getCurrentUser();
            String role = currentUser.getRoleType();
            String district = currentUser.getRegion();

            System.out.println("Current user: " + currentUser.getUsername());
            System.out.println("User role: " + role);
            System.out.println("User district/region: " + district);

            if (role == null || role.trim().isEmpty()) {
                System.out.println("ERROR: User role is null or empty");
                return ResponseEntity.badRequest().body(null);
            }

            if (district == null || district.trim().isEmpty()) {
                System.out.println("ERROR: User district is null or empty");
                return ResponseEntity.badRequest().body(null);
            }

            List<Employee> approvedEmployees = employeeListBasedOnRegionService.getSUPERINTENDENTandAMAPPROVEDEmployeesByDistrict(role, district);
            System.out.println("Found " + approvedEmployees.size() + " SUPERINTENDENT and AM approved employees for role: " + role + ", district: " + district);

            return ResponseEntity.ok(approvedEmployees);
        } catch (Exception e) {
            System.err.println("Error in getSuperAndAMEmployees: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(null);
        }
    }

    @GetMapping("/region/approvedDrmAndMa")
    public ResponseEntity<List<Employee>> getDRMAndMAEmployees() {
        try {
            System.out.println("=== DEBUG: Getting DRM and MA approved employees ===");

            User currentUser = authService.getCurrentUser();
            String role = currentUser.getRoleType();
            String district = currentUser.getRegion();

            System.out.println("Current user: " + currentUser.getUsername());
            System.out.println("User role: " + role);
            System.out.println("User district/region: " + district);

            if (role == null || role.trim().isEmpty()) {
                System.out.println("ERROR: User role is null or empty");
                return ResponseEntity.badRequest().body(null);
            }

            if (district == null || district.trim().isEmpty()) {
                System.out.println("ERROR: User district is null or empty");
                return ResponseEntity.badRequest().body(null);
            }

            List<Employee> approvedEmployees = employeeListBasedOnRegionService.getACCEPTED_BY_DRM_MAEmployeesByDistrict(role, district);
            System.out.println("Found " + approvedEmployees.size() + " DRM and MA approved employees for role: " + role + ", district: " + district);

            return ResponseEntity.ok(approvedEmployees);
        } catch (Exception e) {
            System.err.println("Error in getDRMAndMAEmployees: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(null);
        }
    }


    @GetMapping("/debug/user-info")
    public ResponseEntity<String> debugUserInfo() {
        try {
            User currentUser = authService.getCurrentUser();
            String role = currentUser.getRoleType();
            String district = currentUser.getRegion();

            StringBuilder debug = new StringBuilder();
            debug.append("=== USER DEBUG INFO ===\n");
            debug.append("Username: ").append(currentUser.getUsername()).append("\n");
            debug.append("Role Type: ").append(role).append("\n");
            debug.append("Region/District: ").append(district).append("\n");
            debug.append("User ID: ").append(currentUser.getId()).append("\n");

            // Check pending employee statuses
            List<EmployeeStatus> allPendingStatuses = employeeStatusService.getEmployeesByStatus("pending");
            debug.append("Total pending employee statuses: ").append(allPendingStatuses.size()).append("\n");

            if (!allPendingStatuses.isEmpty()) {
                debug.append("Sample pending employee IDs: ");
                for (int i = 0; i < Math.min(5, allPendingStatuses.size()); i++) {
                    debug.append(allPendingStatuses.get(i).getEmpId()).append(" ");
                }
                debug.append("\n");
            }

            // Check ACCEPTED_BY_SUPERINTENDENT_AM employee statuses
            List<EmployeeStatus> allAcceptedStatuses = employeeStatusService.getEmployeesByStatus("ACCEPTED_BY_SUPERINTENDENT_AM");
            debug.append("Total ACCEPTED_BY_SUPERINTENDENT_AM employee statuses: ").append(allAcceptedStatuses.size()).append("\n");

            if (!allAcceptedStatuses.isEmpty()) {
                debug.append("Sample ACCEPTED_BY_SUPERINTENDENT_AM employee IDs: ");
                for (int i = 0; i < Math.min(5, allAcceptedStatuses.size()); i++) {
                    debug.append(allAcceptedStatuses.get(i).getEmpId()).append(" ");
                }
                debug.append("\n");
            }

            // Check ACCEPTED_BY_DRM_MA employee statuses
            List<EmployeeStatus> allDrmMaAcceptedStatuses = employeeStatusService.getEmployeesByStatus("ACCEPTED_BY_DRM_MA");
            debug.append("Total ACCEPTED_BY_DRM_MA employee statuses: ").append(allDrmMaAcceptedStatuses.size()).append("\n");

            if (!allDrmMaAcceptedStatuses.isEmpty()) {
                debug.append("Sample ACCEPTED_BY_DRM_MA employee IDs: ");
                for (int i = 0; i < Math.min(5, allDrmMaAcceptedStatuses.size()); i++) {
                    debug.append(allDrmMaAcceptedStatuses.get(i).getEmpId()).append(" ");
                }
                debug.append("\n");
            }

            return ResponseEntity.ok(debug.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error: " + e.getMessage());
        }
    }

    @GetMapping("/debug/all-statuses")
    public ResponseEntity<String> debugAllStatuses() {
        try {
            StringBuilder debug = new StringBuilder();
            debug.append("=== ALL EMPLOYEE STATUSES DEBUG ===\n");

            // Get all possible statuses
            String[] statuses = {"pending", "ACCEPTED_BY_SUPERINTENDENT_AM", "ACCEPTED_BY_DRM_MA", "APPROVED", "REJECTED"};

            for (String status : statuses) {
                List<EmployeeStatus> statusList = employeeStatusService.getEmployeesByStatus(status);
                debug.append("Status '").append(status).append("': ").append(statusList.size()).append(" employees\n");

                if (!statusList.isEmpty()) {
                    debug.append("  Employee IDs: ");
                    for (int i = 0; i < Math.min(10, statusList.size()); i++) {
                        debug.append(statusList.get(i).getEmpId()).append(" ");
                    }
                    if (statusList.size() > 10) {
                        debug.append("... (").append(statusList.size() - 10).append(" more)");
                    }
                    debug.append("\n");
                }
            }

            return ResponseEntity.ok(debug.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error: " + e.getMessage());
        }
    }

    @PutMapping("/status/{empId}")
    public ResponseEntity<String> updateStatus(
            @PathVariable Long empId,
            @RequestBody UpdateStatusDto updateStatusDto) {
        User currentUser = authService.getCurrentUser();
        String role = currentUser.getRoleType();
        String updatedBy = currentUser.getUsername();

        employeeListBasedOnRegionService.updateEmployeeStatus(
                empId,
                updateStatusDto.getStatus(),
                updatedBy,
                updateStatusDto.getRemarks(),
                role
        );
        return ResponseEntity.ok("Status updated successfully");
    }
}
