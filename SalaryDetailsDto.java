package digitization.digitization.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class SalaryDetailsDto {

    private Long id;
    private LocalDate lastSalaryRevisedDate;
    private BigDecimal currentWithdrawSalary;
    private Long employeeId;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public SalaryDetailsDto() {
    }

    public SalaryDetailsDto(Long id, LocalDate lastSalaryRevisedDate, BigDecimal currentWithdrawSalary, Long employeeId) {
        this.id = id;
        this.lastSalaryRevisedDate = lastSalaryRevisedDate;
        this.currentWithdrawSalary = currentWithdrawSalary;
        this.employeeId = employeeId;
    }

    public SalaryDetailsDto(Long id, LocalDate lastSalaryRevisedDate, BigDecimal currentWithdrawSalary, 
                           Long employeeId, String createdBy, String updatedBy, 
                           LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.lastSalaryRevisedDate = lastSalaryRevisedDate;
        this.currentWithdrawSalary = currentWithdrawSalary;
        this.employeeId = employeeId;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getLastSalaryRevisedDate() {
        return lastSalaryRevisedDate;
    }

    public void setLastSalaryRevisedDate(LocalDate lastSalaryRevisedDate) {
        this.lastSalaryRevisedDate = lastSalaryRevisedDate;
    }

    public BigDecimal getCurrentWithdrawSalary() {
        return currentWithdrawSalary;
    }

    public void setCurrentWithdrawSalary(BigDecimal currentWithdrawSalary) {
        this.currentWithdrawSalary = currentWithdrawSalary;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "SalaryDetailsDto{" +
                "id=" + id +
                ", lastSalaryRevisedDate=" + lastSalaryRevisedDate +
                ", currentWithdrawSalary=" + currentWithdrawSalary +
                ", employeeId=" + employeeId +
                ", createdBy='" + createdBy + '\'' +
                ", updatedBy='" + updatedBy + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
