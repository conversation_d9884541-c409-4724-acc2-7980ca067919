package digitization.digitization.services.implementation;

import digitization.digitization.module.Nominee;

import java.util.List;

public interface NomineeServ {
    List<Nominee> getNomineeList();
    Nominee getNomineeById(Long id);
    Nominee createNominee(Nominee nominee);
    Nominee updateNominee(Long id, Nominee nomineeDetails);
    boolean deleteNominee(Long nomineeId);
    List<Nominee> getNomineesByName(String nomineename);
    List<Nominee> getNomineesByRelationship(String relationship);
    List<Nominee> getNomineesByGender(String gender);
    List<Nominee> getNomineesByAge(Integer age);
    List<Nominee> getNomineesByAgeRange(Integer minAge, Integer maxAge);
    List<Nominee> getNomineesByPercentageShare(Double percentageofshare);
    List<Nominee> getNomineesWithShareGreaterThan(Double percentage);
    List<Nominee> searchNomineesByName(String name);
    List<Nominee> searchNomineesByAddress(String keyword);
    List<Nominee> getNomineesByRelationshipAndGender(String relationship, String gender);
    List<Nominee> getAllNomineesOrderByShare();
    List<Nominee> getAllNomineesOrderByName();
}
