package digitization.digitization.converter.accountdetails;

import digitization.digitization.dto.AccountDetailsDto;
import digitization.digitization.module.AccountDetails;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class DtoToAccountDetails implements Converter<AccountDetailsDto, AccountDetails> {
    @Override
    public AccountDetails convert(AccountDetailsDto accountDetailsDto) {
        AccountDetails accountDetails = new AccountDetails();
        accountDetails.setId(accountDetailsDto.getId());
        accountDetails.setBankaccountnumber(accountDetailsDto.getBankaccountnumber());
        accountDetails.setIfsccode(accountDetailsDto.getIfsccode());
        accountDetails.setBankname(accountDetailsDto.getBankname());
        accountDetails.setUannumber(accountDetailsDto.getUannumber());
        accountDetails.setAadharnumber(accountDetailsDto.getAadharnumber());
        return accountDetails;
    }
}
