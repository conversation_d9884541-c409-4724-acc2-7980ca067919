package digitization.digitization.services;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.EmployeeStatus;
import digitization.digitization.repository.EmployeeStatusRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EmployeeStatusService {

    @Autowired
    private EmployeeStatusRepository employeeStatusRepository;

    /**
     * Create employee status with default 'pending' status
     */
    public EmployeeStatus createEmployeeStatus(Long empId) {
        // Check if employee status already exists
        if (employeeStatusRepository.existsByEmpId(empId)) {
            throw new RuntimeException("Employee status already exists for empId: " + empId);
        }

        EmployeeStatus employeeStatus = new EmployeeStatus(empId);
        return employeeStatusRepository.save(employeeStatus);
    }

    /**
     * Create employee status with specific status
     */
    public EmployeeStatus createEmployeeStatus(Long empId, String status) {
        // Check if employee status already exists
        if (employeeStatusRepository.existsByEmpId(empId)) {
            throw new RuntimeException("Employee status already exists for empId: " + empId);
        }

        EmployeeStatus employeeStatus = new EmployeeStatus(empId, status);
        return employeeStatusRepository.save(employeeStatus);
    }

    /**
     * Create employee status with specific status and createdBy
     */
    public EmployeeStatus createEmployeeStatus(Long empId, String status, String createdBy) {
        // Check if employee status already exists
        if (employeeStatusRepository.existsByEmpId(empId)) {
            throw new RuntimeException("Employee status already exists for empId: " + empId);
        }

        EmployeeStatus employeeStatus = new EmployeeStatus(empId, status, createdBy);
        return employeeStatusRepository.save(employeeStatus);
    }

    /**
     * Get all employees by status (approved, pending, rejected) ordered by updatedAt DESC
     */
    public List<EmployeeStatus> getEmployeesByStatus(String status) {
        return employeeStatusRepository.findByStatusOrderByUpdatedAtDesc(status);
    }

    /**
     * Get employee status by empId
     */
    public EmployeeStatus getEmployeeStatusByEmpId(Long empId) {
        return employeeStatusRepository.findByEmpId(empId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee status not found for empId: " + empId, null, null));
    }

    public EmployeeStatus getEmployeeStatusById(Long id) {
        return employeeStatusRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Employee status not found for empId: ", null, null));
    }

    /**
     * Update employee status
     */
    public EmployeeStatus updateEmployeeStatus(Long empId, String newStatus) {
        EmployeeStatus employeeStatus = getEmployeeStatusByEmpId(empId);
        employeeStatus.setStatus(newStatus);
        return employeeStatusRepository.save(employeeStatus);
    }

    /**
     * Update employee status with additional information
     */
    public EmployeeStatus updateEmployeeStatus(Long empId, String newStatus, String approvedBy, String rejectedBy, String remarks) {
        EmployeeStatus employeeStatus = getEmployeeStatusByEmpId(empId);
        employeeStatus.setStatus(newStatus);
        employeeStatus.setApprovedBy(approvedBy);
        employeeStatus.setRejectedBy(rejectedBy);
        employeeStatus.setRemarks(remarks);
        return employeeStatusRepository.save(employeeStatus);
    }

    /**
     * Approve employee status
     */
    public EmployeeStatus approveEmployee(Long empId, String approvedBy, String remarks) {
        EmployeeStatus employeeStatus = getEmployeeStatusByEmpId(empId);
        employeeStatus.setStatus("approved");
        employeeStatus.setApprovedBy(approvedBy);
        employeeStatus.setRejectedBy(null); // Clear rejected by if previously rejected
        employeeStatus.setRemarks(remarks);
        return employeeStatusRepository.save(employeeStatus);
    }

    /**
     * Reject employee status
     */
    public EmployeeStatus rejectEmployee(Long empId, String rejectedBy, String remarks) {
        EmployeeStatus employeeStatus = getEmployeeStatusByEmpId(empId);
        employeeStatus.setStatus("rejected");
        employeeStatus.setRejectedBy(rejectedBy);
        employeeStatus.setApprovedBy(null); // Clear approved by if previously approved
        employeeStatus.setRemarks(remarks);
        return employeeStatusRepository.save(employeeStatus);
    }

    public EmployeeStatus pendingEmployee(Long empId, String rejectedBy, String remarks) {
        EmployeeStatus employeeStatus = getEmployeeStatusByEmpId(empId);
        employeeStatus.setStatus("pending");
        employeeStatus.setRejectedBy(rejectedBy);
        employeeStatus.setApprovedBy(null); // Clear approved by if previously approved
        employeeStatus.setRemarks(remarks);
        return employeeStatusRepository.save(employeeStatus);
    }
    /**
     * Get all employee statuses ordered by updatedAt DESC
     */
    public List<EmployeeStatus> getAllEmployeeStatuses() {
        return employeeStatusRepository.findAll().stream()
                .sorted((a, b) -> b.getUpdatedAt().compareTo(a.getUpdatedAt()))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Get employee statuses by createdBy ordered by updatedAt DESC
     */
    public List<EmployeeStatus> getEmployeeStatusByCreatedBy(String createdBy) {
        return employeeStatusRepository.findByCreatedByOrderByUpdatedAtDesc(createdBy);
    }

    /**
     * Get all employee statuses without ordering (original method)
     */
    public List<EmployeeStatus> getAllEmployeeStatusesUnordered() {
        return employeeStatusRepository.findAll();
    }

    /**
     * Delete employee status
     */
    public void deleteEmployeeStatus(Long empId) {
        EmployeeStatus employeeStatus = getEmployeeStatusByEmpId(empId);
        employeeStatusRepository.delete(employeeStatus);
    }

    /**
     * Check if employee status exists
     */
    public boolean existsByEmpId(Long empId) {
        return employeeStatusRepository.existsByEmpId(empId);
    }

    /**
     * Send employee for user approval - Updates status to "user approval"
     */
    public EmployeeStatus sendForUserApproval(Long empId, String sentBy, String remarks) {
        EmployeeStatus employeeStatus = getEmployeeStatusByEmpId(empId);
        employeeStatus.setStatus("user approval");
        employeeStatus.setApprovedBy(sentBy); // Track who sent it for approval
        employeeStatus.setRejectedBy(null); // Clear any previous rejection
        employeeStatus.setRemarks(remarks != null ? remarks : "Sent for user approval");
        return employeeStatusRepository.save(employeeStatus);
    }

    /**
     * Send employee for user approval - Simple version
     */
    public EmployeeStatus sendForUserApproval(Long empId) {
        return sendForUserApproval(empId, null, "Sent for user approval");
    }
}
