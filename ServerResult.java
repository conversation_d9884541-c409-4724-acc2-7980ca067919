package digitization.digitization.response.responce;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;

public class ServerResult {
    @JsonProperty("servertype")

    public String servertype;
    @JsonProperty("servername")

    public String servername;
    @JsonProperty("primaryip")

    public String primaryip;
    @JsonProperty("eventdisserver")

    public ArrayList<String> eventdisserver;
    @JsonProperty("_id")

    public int _id;
    @JsonProperty("serverid")

    public int serverid;
    @JsonProperty("auxiliaryip")

    public String auxiliaryip;


    public String getServertype() {
        return servertype;
    }

    public void setServertype(String servertype) {
        this.servertype = servertype;
    }

    public String getServername() {
        return servername;
    }

    public void setServername(String servername) {
        this.servername = servername;
    }

    public String getPrimaryip() {
        return primaryip;
    }

    public void setPrimaryip(String primaryip) {
        this.primaryip = primaryip;
    }

    public ArrayList<String> getEventdisserver() {
        return eventdisserver;
    }

    public void setEventdisserver(ArrayList<String> eventdisserver) {
        this.eventdisserver = eventdisserver;
    }

    public int get_id() {
        return _id;
    }

    public void set_id(int _id) {
        this._id = _id;
    }

    public int getServerid() {
        return serverid;
    }

    public void setServerid(int serverid) {
        this.serverid = serverid;
    }

    public String getAuxiliaryip() {
        return auxiliaryip;
    }

    public void setAuxiliaryip(String auxiliaryip) {
        this.auxiliaryip = auxiliaryip;
    }
}


