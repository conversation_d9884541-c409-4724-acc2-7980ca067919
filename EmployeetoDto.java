package digitization.digitization.converter.employee;

import digitization.digitization.dto.EmployeeDto;
import digitization.digitization.module.Employee;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class EmployeetoDto implements Converter<Employee, EmployeeDto> {
    @Override
    public EmployeeDto convert(Employee employee) {
        EmployeeDto employeeDto=new EmployeeDto();
        employeeDto.setId(employee.getId());
        employeeDto.setEcpfNumber(employee.getEcpfNumber());
        employeeDto.setPanNumber(employee.getPanNumber());
        employeeDto.setEmployeeName(employee.getEmployeeName());
        employeeDto.setFatherName(employee.getFatherName());
        employeeDto.setMotherName(employee.getMotherName());
        employeeDto.setCommunity(employee.getCommunity());
        employeeDto.setCaste(employee.getCaste());
        employeeDto.setDateOfBirth(employee.getDateOfBirth());
        employeeDto.setDateOfEntry(employee.getDateOfEntry());
        employeeDto.setCurrentDesignation(employee.getCurrentDesignation());
        employeeDto.setDistrict(employee.getDistrict());
        employeeDto.setPersonalIdentificationmark1(employee.getPersonalIdentificationmark1());
        employeeDto.setPersonalIdentificationmark2(employee.getPersonalIdentificationmark2());
        employeeDto.setNativePlaceAndTaluk(employee.getNativePlaceAndTaluk());
        employeeDto.setReligion(employee.getReligion());
        employeeDto.setMainEmployeeType(employee.getMainEmployeeType());
        return employeeDto;
    }
}
