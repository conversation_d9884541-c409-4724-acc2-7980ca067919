package digitization.digitization.security;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;


import java.io.IOException;
import java.io.Serializable;

@Component
public class AuthTokenEntryPoint implements AuthenticationEntryPoint, Serializable {

    private static final Long serialVersionUID = -8970718410458077606L;
    private static final Logger logger = LoggerFactory.getLogger(AuthTokenEntryPoint.class);

    @Override
    public void commence(HttpServletRequest Request, HttpServletResponse Response, AuthenticationException authenticationException) throws IOException, ServletException {
        logger.error("Unauthorized error: {}", authenticationException.getMessage());
        Response.sendError(HttpServletResponse.SC_UNAUTHORIZED,authenticationException.getMessage());
    }
}
