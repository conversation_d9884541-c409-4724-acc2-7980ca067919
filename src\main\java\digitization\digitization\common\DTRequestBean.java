package digitization.digitization.common;

import java.util.List;

public class DTRequestBean {
    private Integer draw;
    private Integer length;
    private Integer start;
    private List<DTColumn> columns;
    private DTOrderBean order;
    private DTSearchBean search;


    public Integer getDraw() {
        return draw;
    }

    public void setDraw(Integer draw) {
        this.draw = draw;
    }

    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }


    public DTOrderBean getOrder() {
        return order;
    }

    public void setOrder(DTOrderBean order) {
        this.order = order;
    }

    public List<DTColumn> getColumns() {
        return columns;
    }

    public void setColumns(List<DTColumn> columns) {
        this.columns = columns;
    }

    public DTSearchBean getSearch() {
        return search;
    }

    public void setSearch(DTSearchBean search) {
        this.search = search;
    }

    @Override
    public String toString() {
        return "DTRequestBean{" +
                "draw=" + draw +
                ", length=" + length +
                ", start=" + start +
                ", columns='" + columns + '\'' +
                ", order='" + order + '\'' +
                ", search='" + search + '\'' +
                '}';
    }
}
