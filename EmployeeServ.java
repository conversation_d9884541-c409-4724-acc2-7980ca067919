package digitization.digitization.services.implementation;

import digitization.digitization.dto.EmployeeCreationDto;
import digitization.digitization.dto.EmployeeListDto;
import digitization.digitization.module.Employee;

import java.util.List;
import java.util.Optional;

public interface EmployeeServ {
    List<Employee> getEmployeeList();
    List<EmployeeListDto> getEmployeeListDto();
    Employee getEmployeeById(Long id);
    Employee createEmployee(Employee employee);
    Employee createEmployeeWithRelatedRecords(EmployeeCreationDto employeeCreationDto);
    Employee updateEmployee(Long id, Employee employeeDetails);
    Employee updateEmployeeWithRelatedRecords(Long id, EmployeeCreationDto employeeUpdateDto);
    boolean deleteEmployee(Long empId);

    // Get employees by createdBy
    List<digitization.digitization.dto.EmployeeDetailDto> getEmployeesByCreatedBy(String createdBy);
    List<EmployeeListDto> getEmployeesBasicByCreatedBy(String createdBy);
    List<digitization.digitization.dto.EmployeeDetailDto> getEmployeeDetailsByCreatedBy(String createdBy);

}
