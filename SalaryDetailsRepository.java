package digitization.digitization.repository;

import digitization.digitization.module.SalaryDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SalaryDetailsRepository extends JpaRepository<SalaryDetails, Long> {

    // Find salary details by employee ID
    Optional<SalaryDetails> findByEmployeeId(Long employeeId);

    // Find all salary details by employee ID (in case of multiple records)
    List<SalaryDetails> findAllByEmployeeIdOrderByUpdatedAtDesc(Long employeeId);

    // Check if salary details exist for an employee
    boolean existsByEmployeeId(Long employeeId);

    // Find salary details by created by
    List<SalaryDetails> findByCreatedByOrderByUpdatedAtDesc(String createdBy);

    // Find latest salary details for an employee
    @Query("SELECT s FROM SalaryDetails s WHERE s.employee.id = :employeeId ORDER BY s.updatedAt DESC")
    Optional<SalaryDetails> findLatestByEmployeeId(@Param("employeeId") Long employeeId);

    // Find all salary details ordered by updated date
    List<SalaryDetails> findAllByOrderByUpdatedAtDesc();

    // Custom query to get salary details with employee information
    @Query("SELECT s FROM SalaryDetails s JOIN FETCH s.employee e WHERE s.employee.id = :employeeId")
    Optional<SalaryDetails> findByEmployeeIdWithEmployee(@Param("employeeId") Long employeeId);

    // Delete salary details by employee ID
    void deleteByEmployeeId(Long employeeId);
}
