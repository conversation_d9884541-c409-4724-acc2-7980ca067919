package digitization.digitization.security;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TokenUtilityService implements Serializable {

    private static final long serialVersionUID = -3301605591157050415L;
    private static final Logger logger = LoggerFactory.getLogger(TokenUtilityService.class);

    private static final String CLAIM_KEY_USERNAME = "sub";
    private static final String CLAIM_KEY_AUDIENCE = "audience";
    private static final String CLAIM_KEY_CREATED = "created";
    private static final String CLAIM_KEY_ROLE = "role";
    private static final String CLAIM_USER_AGENT = "user-agent";
    private static final String CLAIM_KEY_TOKEN_TYPE = "JWT";
    private static final String USER_ID = "user_id";

    @Value("${jwt.secret}")
    private String tokenSecret;

    @Value("${jwt.expiration}")
    private Long tokenExpiration;

    // Use hmacShaKeyFor with a proper byte array
    private SecretKey getSecretKey() {
        return Keys.hmacShaKeyFor(tokenSecret.getBytes(StandardCharsets.UTF_8));
    }

    private Claims getClaimsFromAuthToken(String authToken) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSecretKey())
                    .build()
                    .parseClaimsJws(authToken)
                    .getBody();
        } catch (Exception e) {
            logger.error("Cannot parse claims from authToken: ", e);
            throw e;
        }
    }

    public String getUseragentFormToken(String token) {
        Claims claims = getClaimsFromAuthToken(token);
        return (String) claims.get(CLAIM_USER_AGENT);
    }

    public String getUserNameFromAuthToken(String authToken) {
        final Claims claims = getClaimsFromAuthToken(authToken);
        return claims.getSubject();
    }

    public boolean isTokenExpired(String authToken) {
        Date expiration = getExpirationDateFromAuthToken(authToken);
        return expiration.before(new Date());
    }

    public Date getExpirationDateFromAuthToken(String authToken) {
        return getClaimsFromAuthToken(authToken).getExpiration();
    }

    public Date getCreatedDateFromAuthToken(String authToken) {
        return getClaimsFromAuthToken(authToken).getIssuedAt();
    }

    private Date generateTokenExpirationDate() {
        return new Date(System.currentTimeMillis() + tokenExpiration * 1000);
    }

    public String genarateAuthToken(Authentication authentication, String useragent) {
        UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
        return tokenBuilder(userPrincipal, useragent);
    }

    private String tokenBuilder(UserDetailsImpl userPrincipal, String userAgent) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_KEY_USERNAME, userPrincipal.getUsername());
        claims.put(USER_ID, userPrincipal.getUserId());
        claims.put(CLAIM_KEY_CREATED, new Date());
        claims.put(CLAIM_USER_AGENT, userAgent);
        claims.put(CLAIM_KEY_ROLE, userPrincipal.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.joining(",")));

        return Jwts.builder()
                .setHeaderParam("typ", CLAIM_KEY_TOKEN_TYPE)
                .setClaims(claims)
                .setIssuedAt(new Date())
                .setExpiration(generateTokenExpirationDate())
                .signWith(getSecretKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    public boolean validateAuthToken(String authToken) {
        try {
            Jwts.parserBuilder()
                    .setSigningKey(getSecretKey())
                    .build()
                    .parseClaimsJws(authToken);
            return true;
        } catch (SignatureException e) {
            logger.error("Invalid JWT signature: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            logger.error("Invalid JWT token: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            logger.error("JWT token is expired: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            logger.error("JWT token is unsupported: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            logger.error("JWT claims string is empty: {}", e.getMessage());
        }

        return false;
    }
}
