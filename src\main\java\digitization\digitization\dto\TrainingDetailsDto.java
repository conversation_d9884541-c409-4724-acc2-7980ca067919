package digitization.digitization.dto;

import java.time.LocalDate;

public class TrainingDetailsDto {

    private Long id;
    private String trainingtype;
    private LocalDate date;

    public TrainingDetailsDto() {
    }

    public TrainingDetailsDto(Long id, String trainingtype, LocalDate date) {
        this.id = id;
        this.trainingtype = trainingtype;
        this.date = date;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTrainingtype() {
        return trainingtype;
    }

    public void setTrainingtype(String trainingtype) {
        this.trainingtype = trainingtype;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }
}
