package digitization.digitization.module;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name="leave")
public class Leave {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Leave type is required")
    private String leaveType;

    @NotNull(message = "Opening Balance is required")
    private Integer openingBalance;
    @NotNull(message = "Closing Balance is required")
    private Integer closingBalance;
    @NotNull(message = "Entry date is required")
    private LocalDate entryDate;
    @CreatedDate
    private LocalDateTime createdAt;


    @ManyToOne
    @JoinColumn(name = "employee_id")
    private Employee employee;
    public Leave() {
    }
    public Leave(Long id, String leaveType, Integer openingBalance, Integer closingBalance, LocalDate entryDate, LocalDateTime createdAt, Employee employee) {
        this.id = id;
        this.leaveType = leaveType;
        this.openingBalance = openingBalance;
        this.closingBalance = closingBalance;
        this.entryDate = entryDate;
        this.createdAt = createdAt;
        this.employee = employee;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(String leaveType) {
        this.leaveType = leaveType;
    }

    public Integer getOpeningBalance() {
        return openingBalance;
    }

    public void setOpeningBalance(Integer openingBalance) {
        this.openingBalance = openingBalance;
    }

    public Integer getClosingBalance() {
        return closingBalance;
    }

    public void setClosingBalance(Integer closingBalance) {
        this.closingBalance = closingBalance;
    }

    public LocalDate getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(LocalDate entryDate) {
        this.entryDate = entryDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public Employee getEmployee() {
        return employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }





}
