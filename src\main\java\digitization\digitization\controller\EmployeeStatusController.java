package digitization.digitization.controller;

import digitization.digitization.converter.employee.EmployeeToEmployeeWithStatusDto;
import digitization.digitization.dto.EmployeeNameDto;
import digitization.digitization.dto.EmployeeStatusDto;
import digitization.digitization.dto.EmployeeWithStatusDto;
import digitization.digitization.module.Employee;
import digitization.digitization.module.EmployeeStatus;
import digitization.digitization.services.EmployeeService;
import digitization.digitization.services.EmployeeStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@CrossOrigin(value = "*")
@RequestMapping("employee-status")
public class EmployeeStatusController {

    @Autowired
    private EmployeeStatusService employeeStatusService;

    @Autowired
    private EmployeeService employeeService;

//    @Autowired
//    private EmployeeStatusToDto employeeStatusToDto;
//
    @Autowired
    private EmployeeToEmployeeWithStatusDto employeeToEmployeeWithStatusDto;

    /**
     * Get all employees by status (approved, pending, rejected) - Returns complete employee data ordered by updatedAt DESC
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<List<EmployeeWithStatusDto>> getEmployeesByStatus(@PathVariable String status) {
        try {
            System.out.println("=== DEBUG: Getting employees by status: " + status + " (ordered by updatedAt DESC) ===");

            List<Employee> employees = employeeService.getEmployeesByStatus(status);
            System.out.println("Found " + employees.size() + " employees with status: " + status);

            List<EmployeeWithStatusDto> dtos = employees.stream()
                    .map(employeeToEmployeeWithStatusDto::convert)
                    .collect(Collectors.toList());

            System.out.println("Converted to " + dtos.size() + " DTOs (ordered by updatedAt DESC)");
            return ResponseEntity.ok(dtos);
        } catch (Exception e) {
            System.out.println("Error in getEmployeesByStatus: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.ok(new ArrayList<>());
        }
    }

    /**
     * Get approved employee names - Returns only names of employees with approved status
     * Handles both 'approved' and 'APPROVED' status values
     */
    @GetMapping("/approved/names")
    public ResponseEntity<List<EmployeeNameDto>> getApprovedEmployeeNames() {
        try {
            System.out.println("=== DEBUG: Getting approved employee names (handles both 'approved' and 'APPROVED') ===");

            List<EmployeeNameDto> approvedEmployeeNames = employeeService.getApprovedEmployeeNames();
            System.out.println("Found " + approvedEmployeeNames.size() + " approved employees");

            return ResponseEntity.ok(approvedEmployeeNames);
        } catch (Exception e) {
            System.out.println("Error in getApprovedEmployeeNames: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.ok(new ArrayList<>());
        }
    }

    /**
     * Get employee names by status - Generic endpoint that accepts any status value
     * Supports case-insensitive status matching
     */
    @GetMapping("/names/status/{status}")
    public ResponseEntity<List<EmployeeNameDto>> getEmployeeNamesByStatus(@PathVariable String status) {
        try {
            System.out.println("=== DEBUG: Getting employee names by status: " + status + " (case-insensitive) ===");

            List<EmployeeNameDto> employeeNames = getEmployeeNamesByStatusInternal(status);
            System.out.println("Found " + employeeNames.size() + " employees with status: " + status);

            return ResponseEntity.ok(employeeNames);
        } catch (Exception e) {
            System.out.println("Error in getEmployeeNamesByStatus: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.ok(new ArrayList<>());
        }
    }

    /**
     * Internal method to get employee names by status
     */
    private List<EmployeeNameDto> getEmployeeNamesByStatusInternal(String status) {
        return employeeService.getEmployeeNamesByStatus(status);
    }

    /**
     * Get employee status by empId
     */
    @GetMapping("/emp/{empId}")
    public ResponseEntity<EmployeeStatus> getEmployeeStatusByEmpId(@PathVariable Long empId) {
        EmployeeStatus employeeStatus = employeeStatusService.getEmployeeStatusByEmpId(empId);
//        EmployeeStatusDto dto = employeeStatusToDto.convert(employeeStatus);
        return ResponseEntity.ok(employeeStatus);
    }

    /**
     * Create employee status
     */
//    @PostMapping("/create")
//    public ResponseEntity<EmployeeStatus> createEmployeeStatus(@RequestBody EmployeeStatus employeeStatusDto) {
//        EmployeeStatus employeeStatus;
//        if (employeeStatusDto.getStatus() != null && !employeeStatusDto.getStatus().isEmpty()) {
//            employeeStatus = employeeStatusService.createEmployeeStatus(employeeStatusDto.getEmpId(), employeeStatusDto.getStatus());
//        } else {
//            employeeStatus = employeeStatusService.createEmployeeStatus(employeeStatusDto.getEmpId());
//        }
////        EmployeeStatusDto dto = employeeStatusToDto.convert(employeeStatus);
//        return ResponseEntity.ok(EmployeeStatus);
//    }

    /**
     * Update employee status
     */
    @PutMapping("/update/{empId}")
    public ResponseEntity<EmployeeStatus> updateEmployeeStatus(@PathVariable Long empId, @RequestBody EmployeeStatusDto employeeStatusDto) {
        EmployeeStatus employeeStatus;
        if (employeeStatusDto.getApprovedBy() != null || employeeStatusDto.getRejectedBy() != null || employeeStatusDto.getRemarks() != null) {
            employeeStatus = employeeStatusService.updateEmployeeStatus(empId, employeeStatusDto.getStatus(),
                    employeeStatusDto.getApprovedBy(), employeeStatusDto.getRejectedBy(), employeeStatusDto.getRemarks());
        } else {
            employeeStatus = employeeStatusService.updateEmployeeStatus(empId, employeeStatusDto.getStatus());
        }
//        EmployeeStatusDto dto = employeeStatusToDto.convert(employeeStatus);
        return ResponseEntity.ok(employeeStatus);
    }

    /**
     * Approve employee
     */
    @PutMapping("/approve/{empId}")
    public ResponseEntity<EmployeeStatus> approveEmployee(@PathVariable Long empId, @RequestBody EmployeeStatusDto request) {
        EmployeeStatus employeeStatus = employeeStatusService.approveEmployee(empId, request.getApprovedBy(), request.getRemarks());
//        EmployeeStatusDto dto = employeeStatusToDto.convert(employeeStatus);
        return ResponseEntity.ok(employeeStatus);
    }

    /**
     * Reject employee
     */
    @PutMapping("/reject/{empId}")
    public ResponseEntity<EmployeeStatus> rejectEmployee(@PathVariable Long empId, @RequestBody EmployeeStatusDto request) {
        EmployeeStatus employeeStatus = employeeStatusService.rejectEmployee(empId, request.getRejectedBy(), request.getRemarks());
//        EmployeeStatusDto dto = employeeStatusToDto.convert(employeeStatus);
        return ResponseEntity.ok(employeeStatus);
    }

    @PutMapping("/pending/{empId}")
    public ResponseEntity<EmployeeStatus> pendingEmployee(@PathVariable Long empId, @RequestBody EmployeeStatusDto request) {
        EmployeeStatus employeeStatus = employeeStatusService.pendingEmployee(empId, request.getRejectedBy(), request.getRemarks());
//        EmployeeStatusDto dto = employeeStatusToDto.convert(employeeStatus);
        return ResponseEntity.ok(employeeStatus);
    }

    /**
     * Get all employee statuses ordered by updatedAt DESC
     */
//    @GetMapping("/all")
//    public ResponseEntity<List<EmployeeStatusDto>> getAllEmployeeStatuses() {
//        List<EmployeeStatus> employeeStatuses = employeeStatusService.getAllEmployeeStatuses();
//        List<EmployeeStatusDto> dtos = employeeStatuses.stream()
//                .map(employeeStatusToDto::convert)
//                .collect(Collectors.toList());
//        return ResponseEntity.ok(dtos);
//    }

    /**
     * Get employees by status with custom ordering
     */
//    @GetMapping("/status/{status}/ordered")
//    public ResponseEntity<List<EmployeeWithStatusDto>> getEmployeesByStatusOrdered(
//            @PathVariable String status,
//            @RequestParam(defaultValue = "updatedAt") String orderBy,
//            @RequestParam(defaultValue = "desc") String direction) {
//        try {
//            System.out.println("=== DEBUG: Getting employees by status: " + status + " ordered by " + orderBy + " " + direction + " ===");
//
//            List<Employee> employees = employeeService.getEmployeesByStatus(status);
//            List<EmployeeWithStatusDto> dtos = employees.stream()
//                    .map(employeeToEmployeeWithStatusDto::convert)
//                    .collect(Collectors.toList());
//
//            // Apply custom sorting if needed
//            if ("createdAt".equals(orderBy)) {
//                if ("desc".equals(direction)) {
//                    dtos.sort((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()));
//                } else {
//                    dtos.sort((a, b) -> a.getCreatedAt().compareTo(b.getCreatedAt()));
//                }
//            } else if ("employeeName".equals(orderBy)) {
//                if ("desc".equals(direction)) {
//                    dtos.sort((a, b) -> b.getEmployeeName().compareTo(a.getEmployeeName()));
//                } else {
//                    dtos.sort((a, b) -> a.getEmployeeName().compareTo(b.getEmployeeName()));
//                }
//            }
//            // Default is already updatedAt DESC from database query
//
//            System.out.println("Returned " + dtos.size() + " DTOs ordered by " + orderBy + " " + direction);
//            return ResponseEntity.ok(dtos);
//        } catch (Exception e) {
//            System.out.println("Error in getEmployeesByStatusOrdered: " + e.getMessage());
//            e.printStackTrace();
//            return ResponseEntity.ok(new ArrayList<>());
//        }
//    }

    /**
     * Delete employee status
     */
    @DeleteMapping("/delete/{empId}")
    public ResponseEntity<String> deleteEmployeeStatus(@PathVariable Long empId) {
        employeeStatusService.deleteEmployeeStatus(empId);
        return ResponseEntity.ok("Employee status deleted successfully for empId: " + empId);
    }


    /**
     * Get employee details with status by employee ID
     */
//    @GetMapping("/employee/{empId}")
//    public ResponseEntity<EmployeeWithStatusDto> getEmployeeWithStatusById(@PathVariable Long empId) {
//        try {
//            EmployeeWithStatusDto employeeWithStatus = employeeService.getEmployeeWithStatusById(empId);
//            return ResponseEntity.ok(employeeWithStatus);
//        } catch (Exception e) {
//            return ResponseEntity.status(HttpStatus.NOT_FOUND)
//                    .body(null);
//        }
//    }
//


    /**
     * Send employee for user approval - Updates status to "user approval"
     */
    @PostMapping("/send-user")
    public ResponseEntity<digitization.digitization.dto.EmployeeDetailDto> sendEmployeeForUserApproval(@RequestBody digitization.digitization.dto.EmployeeDetailDto employeeData) {
        try {
            Long empId = employeeData.getId();

            // Validate that employee exists
            Employee employee = employeeService.getEmployeeById(empId);
            if (employee == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
            }

            // Update employee status to "user approval"
            EmployeeStatus updatedStatus = employeeStatusService.sendForUserApproval(empId);

            // Get the complete employee data with updated status
            digitization.digitization.dto.EmployeeDetailDto result = employeeService.getEmployeeWithAllDetails(empId);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            System.out.println("Error in sendEmployeeForUserApproval: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/createdBy/{createdBy}")
    public ResponseEntity<List<EmployeeWithStatusDto>> getEmployeeStatusByCreatedBy(@PathVariable String createdBy) {
        try {
            List<EmployeeStatus> employeeStatuses = employeeStatusService.getEmployeeStatusByCreatedBy(createdBy);
            List<EmployeeWithStatusDto> result = new ArrayList<>();

            for (EmployeeStatus empStatus : employeeStatuses) {
                Employee employee = employeeService.getEmployeeById(empStatus.getEmpId());
                if (employee != null) {
                    EmployeeWithStatusDto dto = employeeToEmployeeWithStatusDto.convert(employee);
                    dto.setStatus(empStatus.getStatus());
                    dto.setApprovedBy(empStatus.getApprovedBy());
                    dto.setRejectedBy(empStatus.getRejectedBy());
                    dto.setRemarks(empStatus.getRemarks());
                    dto.setCreatedBy(empStatus.getCreatedBy());
                    dto.setStatusCreatedAt(empStatus.getCreatedAt());
                    dto.setStatusUpdatedAt(empStatus.getUpdatedAt());
                    result.add(dto);
                }
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/createdBy/{createdBy}/count")
    public ResponseEntity<Map<String, Object>> getEmployeeCountByCreatedBy(@PathVariable String createdBy) {
        try {
            List<EmployeeStatus> employeeStatuses = employeeStatusService.getEmployeeStatusByCreatedBy(createdBy);

            Map<String, Object> result = new HashMap<>();
            result.put("createdBy", createdBy);
            result.put("totalEmployees", employeeStatuses.size());

            // Count by status
            Map<String, Long> statusCounts = employeeStatuses.stream()
                    .collect(java.util.stream.Collectors.groupingBy(EmployeeStatus::getStatus, java.util.stream.Collectors.counting()));
            result.put("statusCounts", statusCounts);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }


}


