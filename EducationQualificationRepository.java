package digitization.digitization.repository;

import digitization.digitization.module.EducationQualification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EducationQualificationRepository extends JpaRepository<EducationQualification, Long> {
    Optional<EducationQualification> findById(Long id);
    List<EducationQualification> findByQualification(String qualification);
    List<EducationQualification> findByCoursename(String coursename);
    List<EducationQualification> findBySchoolname(String schoolname);
    List<EducationQualification> findByCollegename(String collegename);
    List<EducationQualification> findByUniversityname(String universityname);
    List<EducationQualification> findByEmployeeId(Long employeeId);
}
