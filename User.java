package digitization.digitization.module;

import digitization.digitization.enums.DeleteStatus;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;


import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "userTable")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column
    private String username;
    @Column
    private String name;
    @Column
    private String email;
    @Column
    private String mobile;
    @Column
    private String password;

    @Column
    private boolean isActive;
    @Column
    private boolean isAccountLocked;
    @Column
    private String forgottenPasswordCode;
    @Column
    private LocalDateTime forgottenPasswordTime;

    private int failedCount;
    private boolean isLoggedin;

    private String confirmPassword;
    private String userId;

    @ManyToMany()
    private List<Role> role;

    private String roleType;

    private DeleteStatus status;


    @CreatedDate
    private LocalDateTime createdAt;

    public User() {
    }

    public User(Long id, String username, String name, String email, String mobile, String password, boolean isActive, boolean isAccountLocked, String forgottenPasswordCode, LocalDateTime forgottenPasswordTime, int failedCount, boolean isLoggedin, String confirmPassword, String userId, List<Role> role, String roleType, LocalDateTime createdAt) {
        this.id = id;
        this.username = username;
        this.name = name;
        this.email = email;
        this.mobile = mobile;
        this.password = password;
        this.isActive = isActive;
        this.isAccountLocked = isAccountLocked;
        this.forgottenPasswordCode = forgottenPasswordCode;
        this.forgottenPasswordTime = forgottenPasswordTime;
        this.failedCount = failedCount;
        this.isLoggedin = isLoggedin;
        this.confirmPassword = confirmPassword;
        this.userId = userId;
        this.role = role;
        this.roleType = roleType;
        this.createdAt = createdAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public boolean isAccountLocked() {
        return isAccountLocked;
    }

    public void setAccountLocked(boolean accountLocked) {
        isAccountLocked = accountLocked;
    }

    public String getForgottenPasswordCode() {
        return forgottenPasswordCode;
    }

    public void setForgottenPasswordCode(String forgottenPasswordCode) {
        this.forgottenPasswordCode = forgottenPasswordCode;
    }

    public int getFailedCount() {
        return failedCount;
    }

    public void setFailedCount(int failedCount) {
        this.failedCount = failedCount;
    }

    public boolean isLoggedin() {
        return isLoggedin;
    }

    public void setLoggedin(boolean loggedin) {
        isLoggedin = loggedin;
    }

    public DeleteStatus getStatus() {
        return status;
    }

    public void setStatus(DeleteStatus status) {
        this.status = status;
    }

    public LocalDateTime getForgottenPasswordTime() {
        return forgottenPasswordTime;
    }

    public void setForgottenPasswordTime(LocalDateTime forgottenPasswordTime) {
        this.forgottenPasswordTime = forgottenPasswordTime;
    }

    public String getConfirmPassword() {
        return confirmPassword;
    }

    public void setConfirmPassword(String confirmPassword) {
        this.confirmPassword = confirmPassword;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }


    public List<Role> getRole() {
        return role;
    }

    public void setRole(List<Role> role) {
        this.role = role;
    }

    public String getRoleType() {
        return roleType;
    }

    public void setRoleType(String roleType) {
        this.roleType = roleType;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}

