package digitization.digitization.module;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

@Entity
@Table(name = "account_details")
public class AccountDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

//    @NotBlank(message = "Bank account number is required")
//    @Pattern(regexp = "^[0-9]{9,18}$", message = "Bank account number must be 9-18 digits")
    private String bankaccountnumber;

//    @NotBlank(message = "IFSC code is required")
//    @Pattern(regexp = "^[A-Z]{4}0[A-Z0-9]{6}$", message = "IFSC code must be valid format")
    private String ifsccode;

//    @NotBlank(message = "Bank name is required")
    private String bankname;

//    @Pattern(regexp = "^[0-9]{12}$", message = "UAN number must be 12 digits")
    private String uannumber;

//    @NotBlank(message = "Aadhar number is required")
//    @Pattern(regexp = "^[0-9]{12}$", message = "Aadhar number must be 12 digits")
    private String aadharnumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "employee_id")
    private Employee employee;

    public AccountDetails() {
    }

    public AccountDetails(Long id, String bankaccountnumber, String ifsccode, String bankname, String uannumber, String aadharnumber) {
        this.id = id;
        this.bankaccountnumber = bankaccountnumber;
        this.ifsccode = ifsccode;
        this.bankname = bankname;
        this.uannumber = uannumber;
        this.aadharnumber = aadharnumber;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBankaccountnumber() {
        return bankaccountnumber;
    }

    public void setBankaccountnumber(String bankaccountnumber) {
        this.bankaccountnumber = bankaccountnumber;
    }

    public String getIfsccode() {
        return ifsccode;
    }

    public void setIfsccode(String ifsccode) {
        this.ifsccode = ifsccode;
    }

    public String getBankname() {
        return bankname;
    }

    public void setBankname(String bankname) {
        this.bankname = bankname;
    }

    public String getUannumber() {
        return uannumber;
    }

    public void setUannumber(String uannumber) {
        this.uannumber = uannumber;
    }

    public String getAadharnumber() {
        return aadharnumber;
    }

    public void setAadharnumber(String aadharnumber) {
        this.aadharnumber = aadharnumber;
    }

    public Employee getEmployee() {
        return employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }
}
