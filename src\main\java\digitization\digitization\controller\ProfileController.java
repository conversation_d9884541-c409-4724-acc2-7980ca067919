package digitization.digitization.controller;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.Profile;
import digitization.digitization.services.ProfileService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("profile")
public class ProfileController {
    
    @Autowired
    ProfileService profileService;

    @GetMapping("/getlist")
    public List<Profile> getProfileList() {
        return profileService.getProfileList();
    }

    @GetMapping("/getProfile/{id}")
    public ResponseEntity<Profile> getProfileById(@PathVariable Long id) throws Exception {
        Profile profile = profileService.getProfileById(id);

        if (profile == null) {
            throw new ResourceNotFoundException("Profile not found with id: " + id, null, null);
        }

        return ResponseEntity.ok(profile);
    }

    @GetMapping("/getProfileByEmail/{email}")
    public ResponseEntity<Profile> getProfileByEmail(@PathVariable String email) throws Exception {
        Profile profile = profileService.getProfileByEmail(email);

        if (profile == null) {
            throw new ResourceNotFoundException("Profile not found with email: " + email, null, null);
        }

        return ResponseEntity.ok(profile);
    }

    @PostMapping("/createProfile")
    public Profile createProfile(@Valid @RequestBody Profile profile) {
        return profileService.createProfile(profile);
    }

    @PutMapping("/updateProfile/{id}")
    public Profile updateProfile(@PathVariable Long id, @Valid @RequestBody Profile profileDetails) {
        return profileService.updateProfile(id, profileDetails);
    }

    @DeleteMapping("/deleteProfile/{id}")
    public ResponseEntity<?> deleteProfile(@PathVariable Long id) {
        boolean deleted = profileService.deleteProfile(id);
        if (deleted) {
            return ResponseEntity.ok().build();
        } else {
            throw new ResourceNotFoundException("Profile not found with id: " + id, null, null);
        }
    }
}
