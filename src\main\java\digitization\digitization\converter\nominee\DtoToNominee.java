package digitization.digitization.converter.nominee;

import digitization.digitization.dto.NomineeDto;
import digitization.digitization.module.Nominee;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class DtoToNominee implements Converter<NomineeDto, Nominee> {
    @Override
    public Nominee convert(NomineeDto nomineeDto) {
        Nominee nominee = new Nominee();
        nominee.setId(nomineeDto.getId());
        nominee.setNomineename(nomineeDto.getNomineename());
        nominee.setAddress(nomineeDto.getAddress());
        nominee.setRelationship(nomineeDto.getRelationship());
        nominee.setAge(nomineeDto.getAge());
        nominee.setPercentageofshare(nomineeDto.getPercentageofshare());
        nominee.setGender(nomineeDto.getGender());
        return nominee;
    }
}
