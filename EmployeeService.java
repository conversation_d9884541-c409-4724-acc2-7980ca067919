package digitization.digitization.services;

import digitization.digitization.dto.*;
import digitization.digitization.dto.userDto.UserDto;
import digitization.digitization.enums.DeleteStatus;
import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.*;
import digitization.digitization.repository.*;
import digitization.digitization.services.implementation.EmployeeServ;
import digitization.digitization.services.userService.UsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class EmployeeService implements EmployeeServ {
    @Autowired
    EmployeeRepo employeeRepo;

    @Autowired
    ProfileRepository profileRepository;

    @Autowired
    AccountDetailsRepository accountDetailsRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    LeaveRepository leaveRepository;

    @Autowired
    EducationQualificationRepository educationQualificationRepository;

    @Autowired
    PunishmentDetailsRepository punishmentDetailsRepository;

    @Autowired
    TrainingDetailsRepository trainingDetailsRepository;

    @Autowired
    NomineeRepository nomineeRepository;

    @Autowired
    DocumentService documentService;

    @Autowired
    EmployeeStatusRepository employeeStatusRepository;

    @Autowired
    EmployeeStatusService employeeStatusService;

    @Autowired
    SalaryDetailsService salaryDetailsService;

    @Autowired
    UserRepository userRepository;

    @Autowired
    RoleRepository roleRepository;

    @Autowired
    UsersService usersService;

//    @Autowired
//    EmployeeStatusService employeeStatusService;

    @Override
    public List<Employee> getEmployeeList() {
        List<Employee> list = employeeRepo.findAllByOrderByIdDesc();
        return list;
    }

    @Override
    public List<EmployeeListDto> getEmployeeListDto() {
        List<Employee> employees = employeeRepo.findAllByOrderByIdDesc();
        return employees.stream()
                .map(this::convertToListDto)
                .collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Employee getEmployeeById(Long empId){
        Optional<Employee> optionalEmployee = this.employeeRepo.findById(empId);
        if (!optionalEmployee.isPresent()) {
            throw new ResourceNotFoundException("Employee not found with id: " + empId, null, null);
        }
        return optionalEmployee.get();

    }
//    public List<Employee> getEmployeesByStatus(String status) {
//        return employeeRepo.findByStatusOrderByUpdatedAtDesc(status);
//    }

    public List<Employee> getEmployeesByStatus(String status) {
        try {
            return employeeRepo.findEmployeesByStatus(status);
        } catch (Exception e) {
            // Fallback approach if the join query fails
            System.out.println("Join query failed, using fallback approach: " + e.getMessage());
            return getEmployeesByStatusFallback(status);
        }
    }
    private List<Employee> getEmployeesByStatusFallback(String status) {
        // Get all employee statuses with the given status
        List<EmployeeStatus> employeeStatuses = employeeStatusService.getEmployeesByStatus(status);

        // Get employee IDs (now they are already Long)
        List<Long> employeeIds = employeeStatuses.stream()
                .map(EmployeeStatus::getEmpId)
                .filter(id -> id != null)
                .collect(Collectors.toList());

        // Get employees by IDs
        return employeeIds.stream()
                .map(id -> {
                    try {
                        return getEmployeeById(id);
                    } catch (Exception e) {
                        System.out.println("Employee not found for ID: " + id);
                        return null;
                    }
                })
                .filter(emp -> emp != null)
                .collect(Collectors.toList());
    }

    public digitization.digitization.dto.EmployeeDetailDto getEmployeeWithAllDetails(Long empId) {
        // Get employee basic details
        Employee employee = getEmployeeById(empId);

        // Create detailed DTO
        digitization.digitization.dto.EmployeeDetailDto detailDto = new digitization.digitization.dto.EmployeeDetailDto();

        // Map employee basic details
        detailDto.setId(employee.getId());
        detailDto.setEcpfNumber(employee.getEcpfNumber());
        detailDto.setPanNumber(employee.getPanNumber());
        detailDto.setEmployeeName(employee.getEmployeeName());
        detailDto.setEmail(employee.getEmail());
        detailDto.setMobileNumber(employee.getMobileNumber());
        detailDto.setRegisterNumber(employee.getRegisterNumber());
        detailDto.setSection(employee.getSection());
        detailDto.setFatherName(employee.getFatherName());
        detailDto.setMotherName(employee.getMotherName());
        detailDto.setDateOfBirth(employee.getDateOfBirth());
        detailDto.setReligion(employee.getReligion());
        detailDto.setCommunity(employee.getCommunity());
        detailDto.setCaste(employee.getCaste());
        detailDto.setPersonalIdentificationmark1(employee.getPersonalIdentificationmark1());
        detailDto.setPersonalIdentificationmark2(employee.getPersonalIdentificationmark2());
        detailDto.setCurrentDesignation(employee.getCurrentDesignation());
        detailDto.setDateOfEntry(employee.getDateOfEntry());
        detailDto.setDistrict(employee.getDistrict());
        detailDto.setNativePlaceAndTaluk(employee.getNativePlaceAndTaluk());
        detailDto.setMainEmployeeType(employee.getMainEmployeeType());
        detailDto.setProfilePhoto(employee.getProfilePhoto());
        detailDto.setCreatedBy(employee.getCreatedBy());

        // Get related data
        detailDto.setProfile(getProfileByEmployeeId(empId));
        detailDto.setAccountDetails(getAccountDetailsByEmployeeId(empId));
        detailDto.setServiceHistory(getServiceHistoryByEmployeeId(empId));
        detailDto.setLeaveBalances(getLeaveBalancesByEmployeeId(empId));
        detailDto.setEducationQualifications(getEducationQualificationsByEmployeeId(empId));
        detailDto.setPunishmentDetails(getPunishmentDetailsByEmployeeId(empId));
        detailDto.setTrainingDetails(getTrainingDetailsByEmployeeId(empId));
        detailDto.setNominees(getNomineesByEmployeeId(empId));
        detailDto.setDocuments(documentService.getDocumentsByEmployeeId(empId));
        detailDto.setSalaryDetails(getSalaryDetailsByEmployeeId(empId));
        System.out.println("get value" + detailDto.getServiceHistory());
        return detailDto;
    }

    @Override
    public Employee createEmployee(Employee employee) {
        Employee emp=new Employee();
        emp.setId(employee.getId());
        emp.setEcpfNumber(employee.getEcpfNumber());
        emp.setPanNumber(employee.getPanNumber());
        emp.setEmployeeName(employee.getEmployeeName());
        emp.setEmail(employee.getEmail());
        emp.setMobileNumber(employee.getMobileNumber());
        emp.setFatherName(employee.getFatherName());
        emp.setMotherName(employee.getMotherName());
        emp.setCommunity(employee.getCommunity());
        emp.setCaste(employee.getCaste());
        emp.setCurrentDesignation(employee.getCurrentDesignation());
        emp.setDateOfBirth(employee.getDateOfBirth());
        emp.setDateOfEntry(employee.getDateOfEntry());
        emp.setPersonalIdentificationmark1(employee.getPersonalIdentificationmark1());
        emp.setPersonalIdentificationmark2(employee.getPersonalIdentificationmark2());
        emp.setNativePlaceAndTaluk(employee.getNativePlaceAndTaluk());
        emp.setReligion(employee.getReligion());
        emp.setDistrict(employee.getDistrict());
        emp.setMainEmployeeType(employee.getMainEmployeeType());
        emp.setCreatedBy(employee.getCreatedBy());

        // Save employee first
        Employee savedEmployee = employeeRepo.save(emp);

        // Create employee status record
        try {
            employeeStatusService.createEmployeeStatus(savedEmployee.getId(), "pending");
        } catch (Exception e) {
            // Log error but don't fail the employee creation
            System.out.println("Warning: Could not create employee status record: " + e.getMessage());
        }

        return savedEmployee;
    }

    @Override
    @Transactional
    public Employee createEmployeeWithRelatedRecords(EmployeeCreationDto employeeCreationDto) {
        // 1. Create Employee
        Employee employee = new Employee();
        employee.setEcpfNumber(employeeCreationDto.getEcpfNumber());
        employee.setPanNumber(employeeCreationDto.getPanNumber());
        employee.setEmployeeName(employeeCreationDto.getEmployeeName());
        employee.setEmail(employeeCreationDto.getEmail());
        employee.setMobileNumber(employeeCreationDto.getMobileNumber());
        employee.setRegisterNumber(employeeCreationDto.getRegisterNumber());
        employee.setSection(employeeCreationDto.getSection());
        employee.setFatherName(employeeCreationDto.getFatherName());
        employee.setMotherName(employeeCreationDto.getMotherName());
        employee.setDateOfBirth(employeeCreationDto.getDateOfBirth());
        employee.setReligion(employeeCreationDto.getReligion());
        employee.setCommunity(employeeCreationDto.getCommunity());
        employee.setCaste(employeeCreationDto.getCaste());
        employee.setPersonalIdentificationmark1(employeeCreationDto.getPersonalIdentificationmark1());
        employee.setPersonalIdentificationmark2(employeeCreationDto.getPersonalIdentificationmark2());
        employee.setCurrentDesignation(employeeCreationDto.getCurrentDesignation());
        employee.setDateOfEntry(employeeCreationDto.getDateOfEntry());
        employee.setDistrict(employeeCreationDto.getDistrict());
        employee.setNativePlaceAndTaluk(employeeCreationDto.getNativePlaceAndTaluk());
        employee.setMainEmployeeType(employeeCreationDto.getMainEmployeeType());
        employee.setCreatedBy(employeeCreationDto.getCreatedBy());

        // Save employee first to get the ID
        Employee savedEmployee = employeeRepo.save(employee);
        Long employeeId = savedEmployee.getId();

        // 2. Create Employee Status record with default "pending" status
        try {
            String createdBy = employeeCreationDto.getCreatedBy();
            if (createdBy != null && !createdBy.trim().isEmpty()) {
                employeeStatusService.createEmployeeStatus(employeeId, "pending", createdBy);
            } else {
                employeeStatusService.createEmployeeStatus(employeeId, "pending");
            }
        } catch (Exception e) {
            // Log error but don't fail the employee creation
            System.out.println("Warning: Could not create employee status record: " + e.getMessage());
        }

        // 3. Create Profile (if provided or use employee data)
        createProfile(employeeCreationDto, employeeId);

        // 4. Create Account Details (if provided)
        if (employeeCreationDto.getAccountDetails() != null) {
            createAccountDetails(employeeCreationDto.getAccountDetails(), employeeId);
        }

        // 5. Create Service History records (if provided)
        if (employeeCreationDto.getServiceHistory() != null && !employeeCreationDto.getServiceHistory().isEmpty()) {
            createServiceHistory(employeeCreationDto.getServiceHistory(), savedEmployee);
        }

        // 6. Create Leave balances (if provided)
        if (employeeCreationDto.getLeaveBalances() != null && !employeeCreationDto.getLeaveBalances().isEmpty()) {
            createLeaveBalances(employeeCreationDto.getLeaveBalances(), savedEmployee);
        }

        // 7. Create Education Qualifications (if provided)
        if (employeeCreationDto.getEducationQualifications() != null && !employeeCreationDto.getEducationQualifications().isEmpty()) {
            createEducationQualifications(employeeCreationDto.getEducationQualifications(), employeeId);
        }

        // 8. Create Punishment Details (if provided)
        if (employeeCreationDto.getPunishmentDetails() != null && !employeeCreationDto.getPunishmentDetails().isEmpty()) {
            createPunishmentDetails(employeeCreationDto.getPunishmentDetails(), employeeId);
        }

        // 9. Create Training Details (if provided)
        if (employeeCreationDto.getTrainingDetails() != null && !employeeCreationDto.getTrainingDetails().isEmpty()) {
            createTrainingDetails(employeeCreationDto.getTrainingDetails(), employeeId);
        }

        // 10. Create Nominees (if provided)
        if (employeeCreationDto.getNominees() != null && !employeeCreationDto.getNominees().isEmpty()) {
            createNominees(employeeCreationDto.getNominees(), employeeId);
        }

        // 11. Create Documents (if provided)
        if (employeeCreationDto.getDocuments() != null && !employeeCreationDto.getDocuments().isEmpty()) {
            createDocuments(employeeCreationDto.getDocuments(), employeeId);
        }

        // 12. Create Salary Details (if provided)
        if (employeeCreationDto.getSalaryDetails() != null) {
            createSalaryDetails(employeeCreationDto.getSalaryDetails(), employeeId);
        }

        UserDto user = new UserDto();
        user.setCreatedAt(LocalDateTime.now());
        user.setEmail(employeeCreationDto.getEmail());
        user.setUsername(employeeCreationDto.getEcpfNumber());
        user.setPassword(employeeCreationDto.getPanNumber());
        user.setActive(true);
        user.setConfirmPassword(employeeCreationDto.getPanNumber());
        user.setFailedCount(0);
        user.setLoggedin(false);
        user.setMobile("9878451245");
        if(user.getRoleId()!=null){
            List<Role> roles = new ArrayList<>();
            Optional<Role>   optionalRole = roleRepository.findById(user.getRoleId());
            user.setRoleType(optionalRole.get().getRole().name());

            Optional<Role> optionalRoles = roleRepository.findById(user.getRoleId());
            if (!optionalRoles.isPresent()) {
                throw new ResourceNotFoundException("Error", "Role", "NOT FOUND");
            }
            roles.add(optionalRoles.get());

            user.setRole(roles.get(2));
        }

//        user.setAccountLocked(accountLocked);
//        user.setActive(accountActive);
        user.setCreatedAt(LocalDateTime.now());
        user.setStatus(DeleteStatus.ACTIVE);


//        usersService.save(user);

        return savedEmployee;
    }

    @Override
    public Employee updateEmployee(Long id, Employee employeeDetails) {
        Employee employee = employeeRepo.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + id, null, null));

        employee.setEmployeeName(employeeDetails.getEmployeeName());
        employee.setEcpfNumber(employeeDetails.getEcpfNumber());
        employee.setPanNumber(employeeDetails.getPanNumber());
        employee.setEmail(employeeDetails.getEmail());
        employee.setMobileNumber(employeeDetails.getMobileNumber());
        employee.setFatherName(employeeDetails.getFatherName());
        employee.setMotherName(employeeDetails.getMotherName());
        employee.setCommunity(employeeDetails.getCommunity());
        employee.setDateOfBirth(employeeDetails.getDateOfBirth());
        employee.setDateOfEntry(employeeDetails.getDateOfEntry());
        employee.setCurrentDesignation(employeeDetails.getCurrentDesignation());
        employee.setCaste(employeeDetails.getCaste());
        employee.setPersonalIdentificationmark1(employeeDetails.getPersonalIdentificationmark1());
        employee.setPersonalIdentificationmark2(employeeDetails.getPersonalIdentificationmark2());
        employee.setNativePlaceAndTaluk(employeeDetails.getNativePlaceAndTaluk());
        employee.setDistrict(employeeDetails.getDistrict());
        employee.setReligion(employeeDetails.getReligion());
        employee.setMainEmployeeType(employeeDetails.getMainEmployeeType());
        // Note: createdBy should not be updated, only set during creation
        // employee.setCreatedBy(employeeDetails.getCreatedBy());

        return employeeRepo.save(employee);
    }

    @Override
    @Transactional
    public Employee updateEmployeeWithRelatedRecords(Long id, EmployeeCreationDto employeeUpdateDto) {
        // 1. Get existing employee
        Employee employee = employeeRepo.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found with id: " + id, null, null));

        // 2. Update employee basic details
        employee.setEcpfNumber(employeeUpdateDto.getEcpfNumber());
        employee.setPanNumber(employeeUpdateDto.getPanNumber());
        employee.setEmployeeName(employeeUpdateDto.getEmployeeName());
        employee.setEmail(employeeUpdateDto.getEmail());
        employee.setMobileNumber(employeeUpdateDto.getMobileNumber());
        employee.setRegisterNumber(employeeUpdateDto.getRegisterNumber());
        employee.setSection(employeeUpdateDto.getSection());
        employee.setFatherName(employeeUpdateDto.getFatherName());
        employee.setMotherName(employeeUpdateDto.getMotherName());
        employee.setDateOfBirth(employeeUpdateDto.getDateOfBirth());
        employee.setReligion(employeeUpdateDto.getReligion());
        employee.setCommunity(employeeUpdateDto.getCommunity());
        employee.setCaste(employeeUpdateDto.getCaste());
        employee.setPersonalIdentificationmark1(employeeUpdateDto.getPersonalIdentificationmark1());
        employee.setPersonalIdentificationmark2(employeeUpdateDto.getPersonalIdentificationmark2());
        employee.setCurrentDesignation(employeeUpdateDto.getCurrentDesignation());
        employee.setDateOfEntry(employeeUpdateDto.getDateOfEntry());
        employee.setDistrict(employeeUpdateDto.getDistrict());
        employee.setNativePlaceAndTaluk(employeeUpdateDto.getNativePlaceAndTaluk());
        employee.setMainEmployeeType(employeeUpdateDto.getMainEmployeeType());
        // Note: createdBy should not be updated, only set during creation
        // employee.setCreatedBy(employeeUpdateDto.getCreatedBy());

        // Save updated employee
        Employee updatedEmployee = employeeRepo.save(employee);

        // 3. Update Profile
        updateProfile(employeeUpdateDto, id);

        // 4. Update Account Details
        if (employeeUpdateDto.getAccountDetails() != null) {
            updateAccountDetails(employeeUpdateDto.getAccountDetails(), id);
        }

        // 5. Update Service History records
        if (employeeUpdateDto.getServiceHistory() != null && !employeeUpdateDto.getServiceHistory().isEmpty()) {
            updateServiceHistory(employeeUpdateDto.getServiceHistory(), updatedEmployee);
        }

        // 6. Update Leave balances
        if (employeeUpdateDto.getLeaveBalances() != null && !employeeUpdateDto.getLeaveBalances().isEmpty()) {
            updateLeaveBalances(employeeUpdateDto.getLeaveBalances(), updatedEmployee);
        }

        // 7. Update Education Qualifications
        if (employeeUpdateDto.getEducationQualifications() != null && !employeeUpdateDto.getEducationQualifications().isEmpty()) {
            updateEducationQualifications(employeeUpdateDto.getEducationQualifications(), id);
        }

        // 8. Update Punishment Details
        if (employeeUpdateDto.getPunishmentDetails() != null && !employeeUpdateDto.getPunishmentDetails().isEmpty()) {
            updatePunishmentDetails(employeeUpdateDto.getPunishmentDetails(), id);
        }

        // 9. Update Training Details
        if (employeeUpdateDto.getTrainingDetails() != null && !employeeUpdateDto.getTrainingDetails().isEmpty()) {
            updateTrainingDetails(employeeUpdateDto.getTrainingDetails(), id);
        }

        // 10. Update Nominees
        if (employeeUpdateDto.getNominees() != null && !employeeUpdateDto.getNominees().isEmpty()) {
            updateNominees(employeeUpdateDto.getNominees(), id);
        }

        // 11. Update Documents (create new placeholders if provided)
        if (employeeUpdateDto.getDocuments() != null && !employeeUpdateDto.getDocuments().isEmpty()) {
            createDocuments(employeeUpdateDto.getDocuments(), id);
        }

        // 12. Update Salary Details
        if (employeeUpdateDto.getSalaryDetails() != null) {
            updateSalaryDetails(employeeUpdateDto.getSalaryDetails(), id);
        }

        return updatedEmployee;
    }

    @Override
    @Transactional
    public boolean deleteEmployee(Long empId) {
        try {
            Optional<Employee> employeeOpt = employeeRepo.findById(empId);
            if (!employeeOpt.isPresent()) {
                System.out.println("Employee not found with ID: " + empId);
                return false;
            }

            Employee employee = employeeOpt.get();
            System.out.println("Starting deletion process for employee ID: " + empId + " (" + employee.getEmployeeName() + ")");

            // 1. Delete all documents and associated files
            deleteEmployeeDocuments(empId);

            // 2. Delete employee profile photo if exists
            deleteEmployeeProfilePhoto(employee);

            // 3. Delete related records explicitly (in case cascade doesn't work properly)
            deleteEmployeeRelatedRecords(empId);

            // 4. Delete employee status
            deleteEmployeeStatus(empId);

            // 5. Finally delete the employee record (cascade should handle remaining relationships)
            employeeRepo.deleteById(empId);

            System.out.println("Successfully deleted employee ID: " + empId + " and all related records");
            return true;

        } catch (Exception e) {
            System.err.println("Error deleting employee ID " + empId + ": " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to delete employee and related records: " + e.getMessage(), e);
        }
    }

    /**
     * Delete all documents associated with an employee and their files from storage
     */
    private void deleteEmployeeDocuments(Long empId) {
        try {
            List<Document> documents = documentService.getDocumentsByEmployeeIdAsList(empId);
            for (Document document : documents) {
                // Delete document using document service which handles file deletion
                documentService.deleteDocument(document.getId());
            }
            System.out.println("Deleted " + documents.size() + " documents for employee ID: " + empId);
        } catch (Exception e) {
            System.err.println("Error deleting documents for employee ID " + empId + ": " + e.getMessage());
        }
    }

    /**
     * Delete employee profile photo from storage
     */
    private void deleteEmployeeProfilePhoto(Employee employee) {
        try {
            if (employee.getProfilePhoto() != null && !employee.getProfilePhoto().isEmpty()) {
                // Assuming there's a file storage service to delete files
                // fileStorageService.deleteFile(employee.getProfilePhoto());
                System.out.println("Profile photo deletion attempted for employee ID: " + employee.getId());
            }
        } catch (Exception e) {
            System.err.println("Error deleting profile photo for employee ID " + employee.getId() + ": " + e.getMessage());
        }
    }

    /**
     * Delete all related records for an employee
     */
    private void deleteEmployeeRelatedRecords(Long empId) {
        try {
            // Delete service history
            List<ServiceHistory> serviceHistoryList = serviceRepository.findByEmployeeIdOrderByDateDesc(empId);
            if (!serviceHistoryList.isEmpty()) {
                serviceRepository.deleteAll(serviceHistoryList);
                System.out.println("Deleted " + serviceHistoryList.size() + " service history records for employee ID: " + empId);
            }

            // Delete leave records
            List<Leave> leaveList = leaveRepository.findByEmployeeId(empId);
            if (!leaveList.isEmpty()) {
                leaveRepository.deleteAll(leaveList);
                System.out.println("Deleted " + leaveList.size() + " leave records for employee ID: " + empId);
            }

            // Delete education qualifications
            List<EducationQualification> educationList = educationQualificationRepository.findByEmployeeId(empId);
            if (!educationList.isEmpty()) {
                educationQualificationRepository.deleteAll(educationList);
                System.out.println("Deleted " + educationList.size() + " education qualification records for employee ID: " + empId);
            }

            // Delete punishment details
            List<PunishmentDetails> punishmentList = punishmentDetailsRepository.findByEmployeeId(empId);
            if (!punishmentList.isEmpty()) {
                punishmentDetailsRepository.deleteAll(punishmentList);
                System.out.println("Deleted " + punishmentList.size() + " punishment details records for employee ID: " + empId);
            }

            // Delete training details
            List<TrainingDetails> trainingList = trainingDetailsRepository.findByEmployeeId(empId);
            if (!trainingList.isEmpty()) {
                trainingDetailsRepository.deleteAll(trainingList);
                System.out.println("Deleted " + trainingList.size() + " training details records for employee ID: " + empId);
            }

            // Delete nominees
            List<Nominee> nomineeList = nomineeRepository.findByEmployeeId(empId);
            if (!nomineeList.isEmpty()) {
                nomineeRepository.deleteAll(nomineeList);
                System.out.println("Deleted " + nomineeList.size() + " nominee records for employee ID: " + empId);
            }

            // Delete profile
            Optional<Profile> profileOpt = profileRepository.findByEmployeeId(empId);
            if (profileOpt.isPresent()) {
                profileRepository.delete(profileOpt.get());
                System.out.println("Deleted profile record for employee ID: " + empId);
            }

            // Delete account details
            Optional<AccountDetails> accountDetailsOpt = accountDetailsRepository.findByEmployeeId(empId);
            if (accountDetailsOpt.isPresent()) {
                accountDetailsRepository.delete(accountDetailsOpt.get());
                System.out.println("Deleted account details record for employee ID: " + empId);
            }

        } catch (Exception e) {
            System.err.println("Error deleting related records for employee ID " + empId + ": " + e.getMessage());
            throw e;
        }
    }

    /**
     * Delete employee status record
     */
    private void deleteEmployeeStatus(Long empId) {
        try {
            if (employeeStatusService.existsByEmpId(empId)) {
                employeeStatusService.deleteEmployeeStatus(empId);
                System.out.println("Deleted employee status for employee ID: " + empId);
            }
        } catch (Exception e) {
            System.err.println("Error deleting employee status for employee ID " + empId + ": " + e.getMessage());
        }
    }

    @Override
    public List<digitization.digitization.dto.EmployeeDetailDto> getEmployeesByCreatedBy(String createdBy) {
        try {
            // Get employee statuses by createdBy
            List<EmployeeStatus> employeeStatuses = employeeStatusRepository.findByCreatedByOrderByUpdatedAtDesc(createdBy);

            List<digitization.digitization.dto.EmployeeDetailDto> employeeDetails = new ArrayList<>();

            for (EmployeeStatus empStatus : employeeStatuses) {
                try {
                    // Get employee with all details
                    digitization.digitization.dto.EmployeeDetailDto employeeDetail = getEmployeeWithAllDetails(empStatus.getEmpId());
                    employeeDetails.add(employeeDetail);
                } catch (Exception e) {
                    // Log error but continue with other employees
                    System.out.println("Warning: Could not get details for employee ID " + empStatus.getEmpId() + ": " + e.getMessage());
                }
            }

            return employeeDetails;
        } catch (Exception e) {
            throw new RuntimeException("Error getting employees by createdBy: " + e.getMessage(), e);
        }
    }

    @Override
    public List<EmployeeListDto> getEmployeesBasicByCreatedBy(String createdBy) {
        try {
            // Get employee statuses by createdBy
            List<EmployeeStatus> employeeStatuses = employeeStatusRepository.findByCreatedByOrderByUpdatedAtDesc(createdBy);

            List<EmployeeListDto> employeeList = new ArrayList<>();

            for (EmployeeStatus empStatus : employeeStatuses) {
                try {
                    // Get basic employee info
                    Optional<Employee> employeeOpt = employeeRepo.findById(empStatus.getEmpId());
                    if (employeeOpt.isPresent()) {
                        Employee employee = employeeOpt.get();
                        EmployeeListDto employeeListDto = new EmployeeListDto();
                        employeeListDto.setId(employee.getId());
                        employeeListDto.setEmployeeName(employee.getEmployeeName());
                        employeeListDto.setEcpfNumber(employee.getEcpfNumber());
                        employeeListDto.setPanNumber(employee.getPanNumber());
                        employeeListDto.setEmail(employee.getEmail());
                        employeeListDto.setMobileNumber(employee.getMobileNumber());
                        employeeListDto.setCurrentDesignation(employee.getCurrentDesignation());
                        employeeListDto.setSection(employee.getSection());
                        employeeListDto.setDistrict(employee.getDistrict());
                        employeeListDto.setStatus(empStatus.getStatus());
                        employeeListDto.setCreatedBy(empStatus.getCreatedBy());
                        employeeListDto.setCreatedAt(empStatus.getCreatedAt());

                        employeeList.add(employeeListDto);
                    }
                } catch (Exception e) {
                    // Log error but continue with other employees
                    System.out.println("Warning: Could not get basic info for employee ID " + empStatus.getEmpId() + ": " + e.getMessage());
                }
            }

            return employeeList;
        } catch (Exception e) {
            throw new RuntimeException("Error getting basic employees by createdBy: " + e.getMessage(), e);
        }
    }

    @Override
    public List<digitization.digitization.dto.EmployeeDetailDto> getEmployeeDetailsByCreatedBy(String createdBy) {
        try {
            // Get employees directly from employee table by createdBy
            List<Employee> employees = employeeRepo.findByCreatedByOrderByIdDesc(createdBy);

            List<digitization.digitization.dto.EmployeeDetailDto> employeeDetails = new ArrayList<>();

            for (Employee employee : employees) {
                try {
                    // Get complete employee details for each employee
                    digitization.digitization.dto.EmployeeDetailDto employeeDetail = getEmployeeWithAllDetails(employee.getId());
                    employeeDetails.add(employeeDetail);
                } catch (Exception e) {
                    // Log error but continue with other employees
                    System.out.println("Warning: Could not get details for employee ID " + employee.getId() + ": " + e.getMessage());
                }
            }

            return employeeDetails;
        } catch (Exception e) {
            throw new RuntimeException("Error getting employee details by createdBy: " + e.getMessage(), e);
        }
    }

    // Helper methods for creating related records
    private void createProfile(EmployeeCreationDto employeeCreationDto, Long employeeId) {
        Profile profile = new Profile();

        // Set the employee relationship
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            profile.setEmployee(employee);
        }

        if (employeeCreationDto.getProfile() != null) {
            ProfileDto profileDto = employeeCreationDto.getProfile();
            profile.setFatherName(profileDto.getFatherName());
            profile.setMotherName(profileDto.getMotherName());
            profile.setDateOfBirth(profileDto.getDateOfBirth());
            profile.setCommunity(profileDto.getCommunity());
            profile.setCaste(profileDto.getCaste());
            profile.setDistrict(profileDto.getDistrict());
            profile.setNativeplaceandtaluk(profileDto.getNativeplaceandtaluk());

            // Present address fields
            profile.setPresentDoorNo(profileDto.getPresentDoorNo());
            profile.setPresentBuildingName(profileDto.getPresentBuildingName());
            profile.setPresentStreetAddress(profileDto.getPresentStreetAddress());
            profile.setPresentCity(profileDto.getPresentCity());
            profile.setPresentPincode(profileDto.getPresentPincode());

            // Permanent address fields
            profile.setPermanentDoorNo(profileDto.getPermanentDoorNo());
            profile.setPermanentBuildingName(profileDto.getPermanentBuildingName());
            profile.setPermanentStreetAddress(profileDto.getPermanentStreetAddress());
            profile.setPermanentCity(profileDto.getPermanentCity());
            profile.setPermanentPincode(profileDto.getPermanentPincode());

            profile.setProfilephoto(profileDto.getProfilephoto());
            profile.setEmployeeType(profileDto.getEmployeeType());
            profile.setEmail(profileDto.getEmail());
        } else {
            // Use employee data as default - set address fields to null since employee doesn't have detailed address
            profile.setFatherName(employeeCreationDto.getFatherName());
            profile.setMotherName(employeeCreationDto.getMotherName());
            profile.setDateOfBirth(employeeCreationDto.getDateOfBirth());
            profile.setCommunity(employeeCreationDto.getCommunity());
            profile.setCaste(employeeCreationDto.getCaste());
            profile.setDistrict(employeeCreationDto.getDistrict());
            profile.setNativeplaceandtaluk(employeeCreationDto.getNativePlaceAndTaluk());

            // Set address fields to null since employee only has full address text
            profile.setPresentDoorNo(null);
            profile.setPresentBuildingName(null);
            profile.setPresentStreetAddress(null);
            profile.setPresentCity(null);
            profile.setPresentPincode(null);
            profile.setPermanentDoorNo(null);
            profile.setPermanentBuildingName(null);
            profile.setPermanentStreetAddress(null);
            profile.setPermanentCity(null);
            profile.setPermanentPincode(null);

            // Set profile photo and employee type to null as defaults
            profile.setProfilephoto(null);
            profile.setEmployeeType(null);
            profile.setEmail(employeeCreationDto.getEmail());
        }

        profileRepository.save(profile);
    }

    private void createAccountDetails(AccountDetailsDto accountDetailsDto, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            AccountDetails accountDetails = new AccountDetails();
            accountDetails.setBankaccountnumber(accountDetailsDto.getBankaccountnumber());
            accountDetails.setIfsccode(accountDetailsDto.getIfsccode());
            accountDetails.setBankname(accountDetailsDto.getBankname());
            accountDetails.setUannumber(accountDetailsDto.getUannumber());
            accountDetails.setAadharnumber(accountDetailsDto.getAadharnumber());
            accountDetails.setEmployee(employee);

            accountDetailsRepository.save(accountDetails);
        }
    }

    private void createServiceHistory(List<ServiceHistoryDto> serviceHistoryDtos, Employee employee) {
        for (ServiceHistoryDto dto : serviceHistoryDtos) {
            ServiceHistory serviceHistory = new ServiceHistory();
            serviceHistory.setDate(dto.getDate());
            serviceHistory.setType(dto.getType());
            serviceHistory.setStatus(dto.getStatus());
            serviceHistory.setAppointmenttype(dto.getAppointmenttype());
            serviceHistory.setModeofappointment(dto.getModeofappointment());
            serviceHistory.setDateofappointment(dto.getDateofappointment());
            serviceHistory.setProceedingorderdate(dto.getProceedingorderdate());
            serviceHistory.setProceedingorderno(dto.getProceedingorderno());
            serviceHistory.setJoiningdate(dto.getJoiningdate());
            System.out.println("getting value" + dto.getPromoteddate());
            serviceHistory.setPromoteddate(dto.getPromoteddate());
            serviceHistory.setFromdesignation(dto.getFromdesignation());
            serviceHistory.setTopromoted(dto.getTopromoted());
            serviceHistory.setFromdate(dto.getFromdate());
            serviceHistory.setTodate(dto.getTodate());
            serviceHistory.setTypeofincrement(dto.getTypeofincrement());
            serviceHistory.setFromplace(dto.getFromplace());
            serviceHistory.setToplace(dto.getToplace());
            serviceHistory.setDesignation(dto.getDesignation());
            serviceHistory.setOriginaldesignation(dto.getOriginaldesignation());
            serviceHistory.setParentdepartment(dto.getParentdepartment());
            serviceHistory.setPunishmenttype(dto.getPunishmenttype());
            serviceHistory.setCasedetails(dto.getCasedetails());
            serviceHistory.setPunishmentdate(dto.getPunishmentdate());
            serviceHistory.setEmployee(employee);

            serviceRepository.save(serviceHistory);
        }
    }

    private void createLeaveBalances(List<LeaveDto> leaveDtos, Employee employee) {
        for (LeaveDto dto : leaveDtos) {
            Leave leave = new Leave();
            leave.setLeaveType(dto.getLeaveType());
            leave.setOpeningBalance(dto.getOpeningBalance());
            leave.setClosingBalance(dto.getClosingBalance());
            leave.setEntryDate(dto.getEntryDate());
            leave.setEmployee(employee);

            leaveRepository.save(leave);
        }
    }

    private void createEducationQualifications(List<EducationQualificationDto> educationDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            for (EducationQualificationDto dto : educationDtos) {
                EducationQualification education = new EducationQualification();
                education.setQualification(dto.getQualification());
                education.setCoursename(dto.getCoursename());
                education.setSchoolname(dto.getSchoolname());
                education.setCollegename(dto.getCollegename());
                education.setUniversityname(dto.getUniversityname());
                education.setEmployee(employee);

                educationQualificationRepository.save(education);
            }
        }
    }

    private void createPunishmentDetails(List<PunishmentDetailsDto> punishmentDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            for (PunishmentDetailsDto dto : punishmentDtos) {
                PunishmentDetails punishment = new PunishmentDetails();
                punishment.setPunishmenttype(dto.getPunishmenttype());
                punishment.setDate(dto.getDate());
                punishment.setCasedetails(dto.getCasedetails());
                punishment.setEmployee(employee);

                punishmentDetailsRepository.save(punishment);
            }
        }
    }

    private void createTrainingDetails(List<TrainingDetailsDto> trainingDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            for (TrainingDetailsDto dto : trainingDtos) {
                TrainingDetails training = new TrainingDetails();
                training.setTrainingtype(dto.getTrainingtype());
                training.setDate(dto.getDate());
                training.setEmployee(employee);

                trainingDetailsRepository.save(training);
            }
        }
    }

    private void createNominees(List<NomineeDto> nomineeDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            for (NomineeDto dto : nomineeDtos) {
                Nominee nominee = new Nominee();
                nominee.setNomineename(dto.getNomineename());
                nominee.setAddress(dto.getAddress());
                nominee.setRelationship(dto.getRelationship());
                nominee.setAge(dto.getAge());
                nominee.setPercentageofshare(dto.getPercentageofshare());
                nominee.setGender(dto.getGender());

                // Set nominee photo - store just the filename (e.g., "20250619_200151_2d5d377f")
                if (dto.getNomineePhoto() != null && !dto.getNomineePhoto().trim().isEmpty()) {
                    nominee.setNomineePhoto(dto.getNomineePhoto());
                } else {
                    nominee.setNomineePhoto(null);
                }

                nominee.setEmployee(employee);

                nomineeRepository.save(nominee);
            }
        }
    }

    private void createDocuments(List<digitization.digitization.dto.DocumentCreationDto> documentDtos, Long employeeId) {
        for (digitization.digitization.dto.DocumentCreationDto dto : documentDtos) {
            // Create document placeholder - actual file will be uploaded separately
            documentService.createDocumentPlaceholder(
                employeeId,
                dto.getFileName(),
                dto.getFileType() != null ? dto.getFileType() : "application/pdf"
            );
        }
    }

    private void createSalaryDetails(SalaryDetailsDto salaryDetailsDto, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            SalaryDetails salaryDetails = new SalaryDetails();
            salaryDetails.setLastSalaryRevisedDate(salaryDetailsDto.getLastSalaryRevisedDate());
            salaryDetails.setCurrentWithdrawSalary(salaryDetailsDto.getCurrentWithdrawSalary());
            salaryDetails.setEmployee(employee);
            salaryDetails.setCreatedBy(salaryDetailsDto.getCreatedBy());

            // Use the service to create salary details
            SalaryDetailsDto dto = new SalaryDetailsDto();
            dto.setLastSalaryRevisedDate(salaryDetailsDto.getLastSalaryRevisedDate());
            dto.setCurrentWithdrawSalary(salaryDetailsDto.getCurrentWithdrawSalary());
            dto.setEmployeeId(employeeId);
            dto.setCreatedBy(salaryDetailsDto.getCreatedBy());

            salaryDetailsService.createSalaryDetails(dto);
        }
    }

    private void updateSalaryDetails(SalaryDetailsDto salaryDetailsDto, Long employeeId) {
        try {
            // Check if salary details exist for this employee
            if (salaryDetailsService.existsByEmployeeId(employeeId)) {
                // Get existing salary details
                SalaryDetailsDto existingSalaryDetails = salaryDetailsService.getSalaryDetailsByEmployeeId(employeeId);

                // Update the existing record
                salaryDetailsDto.setEmployeeId(employeeId);
                salaryDetailsService.updateSalaryDetails(existingSalaryDetails.getId(), salaryDetailsDto);
            } else {
                // Create new salary details if none exist
                createSalaryDetails(salaryDetailsDto, employeeId);
            }
        } catch (Exception e) {
            // If getting existing salary details fails, try to create new ones
            createSalaryDetails(salaryDetailsDto, employeeId);
        }
    }

    private SalaryDetailsDto getSalaryDetailsByEmployeeId(Long employeeId) {
        try {
            return salaryDetailsService.getSalaryDetailsByEmployeeId(employeeId);
        } catch (Exception e) {
            // Return null if no salary details found
            return null;
        }
    }

    // Update methods for related records
    private void updateProfile(EmployeeCreationDto employeeUpdateDto, Long employeeId) {
        // Find existing profile by employee email or create new one
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null && employee.getEmail() != null) {
            java.util.Optional<Profile> existingProfileOpt = profileRepository.findByEmail(employee.getEmail());
            Profile profile;

            if (existingProfileOpt.isPresent()) {
                profile = existingProfileOpt.get();
            } else {
                profile = new Profile();
            }

            if (employeeUpdateDto.getProfile() != null) {
                ProfileDto profileDto = employeeUpdateDto.getProfile();
                profile.setFatherName(profileDto.getFatherName());
                profile.setMotherName(profileDto.getMotherName());
                profile.setDateOfBirth(profileDto.getDateOfBirth());
                profile.setCommunity(profileDto.getCommunity());
                profile.setCaste(profileDto.getCaste());
                profile.setDistrict(profileDto.getDistrict());
                profile.setNativeplaceandtaluk(profileDto.getNativeplaceandtaluk());
                profile.setEmail(profileDto.getEmail());
            } else {
                // Use employee data as default
                profile.setFatherName(employeeUpdateDto.getFatherName());
                profile.setMotherName(employeeUpdateDto.getMotherName());
                profile.setDateOfBirth(employeeUpdateDto.getDateOfBirth());
                profile.setCommunity(employeeUpdateDto.getCommunity());
                profile.setCaste(employeeUpdateDto.getCaste());
                profile.setDistrict(employeeUpdateDto.getDistrict());
                profile.setNativeplaceandtaluk(employeeUpdateDto.getNativePlaceAndTaluk());

                profile.setEmail(employeeUpdateDto.getEmail());
            }

            profileRepository.save(profile);
        }
    }

    private void updateAccountDetails(AccountDetailsDto accountDetailsDto, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            java.util.Optional<AccountDetails> existingAccountOpt = accountDetailsRepository.findByEmployeeId(employeeId);
            AccountDetails accountDetails;

            if (existingAccountOpt.isPresent()) {
                accountDetails = existingAccountOpt.get();
            } else {
                accountDetails = new AccountDetails();
                accountDetails.setEmployee(employee);
            }

            accountDetails.setBankaccountnumber(accountDetailsDto.getBankaccountnumber());
            accountDetails.setIfsccode(accountDetailsDto.getIfsccode());
            accountDetails.setBankname(accountDetailsDto.getBankname());
            accountDetails.setUannumber(accountDetailsDto.getUannumber());
            accountDetails.setAadharnumber(accountDetailsDto.getAadharnumber());

            accountDetailsRepository.save(accountDetails);
        }
    }

    private void updateServiceHistory(List<ServiceHistoryDto> serviceHistoryDtos, Employee employee) {
        // Delete existing service history records
        List<ServiceHistory> existingRecords = serviceRepository.findByEmployeeIdOrderByDateDesc(employee.getId());
        serviceRepository.deleteAll(existingRecords);

        // Create new service history records
        for (ServiceHistoryDto dto : serviceHistoryDtos) {
            ServiceHistory serviceHistory = new ServiceHistory();
            serviceHistory.setDate(dto.getDate());
            serviceHistory.setType(dto.getType());
            serviceHistory.setStatus(dto.getStatus());
            serviceHistory.setAppointmenttype(dto.getAppointmenttype());
            serviceHistory.setModeofappointment(dto.getModeofappointment());
            serviceHistory.setDateofappointment(dto.getDateofappointment());
            serviceHistory.setProceedingorderdate(dto.getProceedingorderdate());
            serviceHistory.setProceedingorderno(dto.getProceedingorderno());
            serviceHistory.setJoiningdate(dto.getJoiningdate());
            System.out.println("promoted date"+dto.getPromoteddate());
            serviceHistory.setPromoteddate(dto.getPromoteddate());
            serviceHistory.setFromdesignation(dto.getFromdesignation());
            serviceHistory.setTopromoted(dto.getTopromoted());
            serviceHistory.setFromdate(dto.getFromdate());
            serviceHistory.setTodate(dto.getTodate());
            serviceHistory.setTypeofincrement(dto.getTypeofincrement());
            serviceHistory.setFromplace(dto.getFromplace());
            serviceHistory.setToplace(dto.getToplace());
            serviceHistory.setDesignation(dto.getDesignation());
            serviceHistory.setOriginaldesignation(dto.getOriginaldesignation());
            serviceHistory.setParentdepartment(dto.getParentdepartment());
            serviceHistory.setPunishmenttype(dto.getPunishmenttype());
            serviceHistory.setCasedetails(dto.getCasedetails());
            serviceHistory.setPunishmentdate(dto.getPunishmentdate());
            serviceHistory.setEmployee(employee);

            serviceRepository.save(serviceHistory);
        }
    }

    private void updateLeaveBalances(List<LeaveDto> leaveDtos, Employee employee) {
        // Delete existing leave records
        List<Leave> existingRecords = leaveRepository.findByEmployeeId(employee.getId());
        leaveRepository.deleteAll(existingRecords);

        // Create new leave records
        for (LeaveDto dto : leaveDtos) {
            Leave leave = new Leave();
            leave.setLeaveType(dto.getLeaveType());
            leave.setOpeningBalance(dto.getOpeningBalance());
            leave.setClosingBalance(dto.getClosingBalance());
            leave.setEntryDate(dto.getEntryDate());
            leave.setEmployee(employee);

            leaveRepository.save(leave);
        }
    }

    private void updateEducationQualifications(List<EducationQualificationDto> educationDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            // Delete existing education records
            List<EducationQualification> existingRecords = educationQualificationRepository.findByEmployeeId(employeeId);
            educationQualificationRepository.deleteAll(existingRecords);

            // Create new education records
            for (EducationQualificationDto dto : educationDtos) {
                EducationQualification education = new EducationQualification();
                education.setQualification(dto.getQualification());
                education.setCoursename(dto.getCoursename());
                education.setSchoolname(dto.getSchoolname());
                education.setCollegename(dto.getCollegename());
                education.setUniversityname(dto.getUniversityname());
                education.setEmployee(employee);

                educationQualificationRepository.save(education);
            }
        }
    }

    private void updatePunishmentDetails(List<PunishmentDetailsDto> punishmentDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            // Delete existing punishment records
            List<PunishmentDetails> existingRecords = punishmentDetailsRepository.findByEmployeeId(employeeId);
            punishmentDetailsRepository.deleteAll(existingRecords);

            // Create new punishment records
            for (PunishmentDetailsDto dto : punishmentDtos) {
                PunishmentDetails punishment = new PunishmentDetails();
                punishment.setPunishmenttype(dto.getPunishmenttype());
                punishment.setDate(dto.getDate());
                punishment.setCasedetails(dto.getCasedetails());
                punishment.setEmployee(employee);

                punishmentDetailsRepository.save(punishment);
            }
        }
    }

    private void updateTrainingDetails(List<TrainingDetailsDto> trainingDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            // Delete existing training records
            List<TrainingDetails> existingRecords = trainingDetailsRepository.findByEmployeeId(employeeId);
            trainingDetailsRepository.deleteAll(existingRecords);

            // Create new training records
            for (TrainingDetailsDto dto : trainingDtos) {
                TrainingDetails training = new TrainingDetails();
                training.setTrainingtype(dto.getTrainingtype());
                training.setDate(dto.getDate());
                training.setEmployee(employee);

                trainingDetailsRepository.save(training);
            }
        }
    }

    private void updateNominees(List<NomineeDto> nomineeDtos, Long employeeId) {
        Employee employee = employeeRepo.findById(employeeId).orElse(null);
        if (employee != null) {
            // Delete existing nominee records
            List<Nominee> existingRecords = nomineeRepository.findByEmployeeId(employeeId);
            nomineeRepository.deleteAll(existingRecords);

            // Create new nominee records
            for (NomineeDto dto : nomineeDtos) {
                Nominee nominee = new Nominee();
                nominee.setNomineename(dto.getNomineename());
                nominee.setAddress(dto.getAddress());
                nominee.setRelationship(dto.getRelationship());
                nominee.setAge(dto.getAge());
                nominee.setPercentageofshare(dto.getPercentageofshare());
                nominee.setGender(dto.getGender());

                // Set nominee photo - store just the filename (e.g., "20250619_200151_2d5d377f")
                if (dto.getNomineePhoto() != null && !dto.getNomineePhoto().trim().isEmpty()) {
                    nominee.setNomineePhoto(dto.getNomineePhoto());
                } else {
                    nominee.setNomineePhoto(null);
                }

                nominee.setEmployee(employee);

                nomineeRepository.save(nominee);
            }
        }
    }

    // Helper method to convert Employee to EmployeeListDto
    private EmployeeListDto convertToListDto(Employee employee) {
        EmployeeListDto dto = new EmployeeListDto();
        dto.setId(employee.getId());
        dto.setEcpfNumber(employee.getEcpfNumber());
        dto.setPanNumber(employee.getPanNumber());
        dto.setEmployeeName(employee.getEmployeeName());
        dto.setEmail(employee.getEmail());
        dto.setMobileNumber(employee.getMobileNumber());
        dto.setRegisterNumber(employee.getRegisterNumber());
        dto.setSection(employee.getSection());
        dto.setCurrentDesignation(employee.getCurrentDesignation());
        dto.setDateOfEntry(employee.getDateOfEntry());
        dto.setDistrict(employee.getDistrict());
        // Note: For list view, we get createdBy from employee_status table instead
        // dto.setCreatedBy(employee.getCreatedBy());
        return dto;
    }

    // Helper methods to get related data by employee ID
    private digitization.digitization.dto.ProfileDto getProfileByEmployeeId(Long employeeId) {
        try {
            // Use the employee relationship to find profile
            java.util.Optional<digitization.digitization.module.Profile> profileOpt = profileRepository.findByEmployeeId(employeeId);
            if (profileOpt.isPresent()) {
                digitization.digitization.module.Profile profile = profileOpt.get();
                digitization.digitization.dto.ProfileDto dto = new digitization.digitization.dto.ProfileDto();
                dto.setId(profile.getId());
                dto.setFatherName(profile.getFatherName());
                dto.setMotherName(profile.getMotherName());
                dto.setDateOfBirth(profile.getDateOfBirth());
                dto.setCommunity(profile.getCommunity());
                dto.setCaste(profile.getCaste());
                dto.setDistrict(profile.getDistrict());
                dto.setNativeplaceandtaluk(profile.getNativeplaceandtaluk());

                // Present address fields
                dto.setPresentDoorNo(profile.getPresentDoorNo());
                dto.setPresentBuildingName(profile.getPresentBuildingName());
                dto.setPresentStreetAddress(profile.getPresentStreetAddress());
                dto.setPresentCity(profile.getPresentCity());
                dto.setPresentPincode(profile.getPresentPincode());

                // Permanent address fields
                dto.setPermanentDoorNo(profile.getPermanentDoorNo());
                dto.setPermanentBuildingName(profile.getPermanentBuildingName());
                dto.setPermanentStreetAddress(profile.getPermanentStreetAddress());
                dto.setPermanentCity(profile.getPermanentCity());
                dto.setPermanentPincode(profile.getPermanentPincode());

                dto.setProfilephoto(profile.getProfilephoto());
                dto.setEmployeeType(profile.getEmployeeType());
                dto.setEmail(profile.getEmail());
                return dto;
            }
        } catch (Exception e) {
            // Log error but don't fail the whole request
        }
        return null;
    }

    private digitization.digitization.dto.AccountDetailsDto getAccountDetailsByEmployeeId(Long employeeId) {
        try {
            java.util.Optional<digitization.digitization.module.AccountDetails> accountDetailsOpt = accountDetailsRepository.findByEmployeeId(employeeId);
            if (accountDetailsOpt.isPresent()) {
                digitization.digitization.module.AccountDetails accountDetails = accountDetailsOpt.get();
                digitization.digitization.dto.AccountDetailsDto dto = new digitization.digitization.dto.AccountDetailsDto();
                dto.setId(accountDetails.getId());
                dto.setBankaccountnumber(accountDetails.getBankaccountnumber());
                dto.setIfsccode(accountDetails.getIfsccode());
                dto.setBankname(accountDetails.getBankname());
                dto.setUannumber(accountDetails.getUannumber());
                dto.setAadharnumber(accountDetails.getAadharnumber());
                return dto;
            }
        } catch (Exception e) {
            // Log error but don't fail the whole request
        }
        return null;
    }

    private java.util.List<digitization.digitization.dto.ServiceHistoryDto> getServiceHistoryByEmployeeId(Long employeeId) {
        try {
            java.util.List<digitization.digitization.module.ServiceHistory> serviceHistoryList = serviceRepository.findByEmployeeIdOrderByDateDesc(employeeId);
            return serviceHistoryList.stream()
                .map(this::convertServiceHistoryToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            // Log error but don't fail the whole request
            return new java.util.ArrayList<>();
        }
    }

    private java.util.List<digitization.digitization.dto.LeaveDto> getLeaveBalancesByEmployeeId(Long employeeId) {
        try {
            // LeaveRepository has findByEmployeeId method
            java.util.List<digitization.digitization.module.Leave> employeeLeaves = leaveRepository.findByEmployeeId(employeeId);

            return employeeLeaves.stream()
                .map(this::convertLeaveToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            return new java.util.ArrayList<>();
        }
    }

    private java.util.List<digitization.digitization.dto.EducationQualificationDto> getEducationQualificationsByEmployeeId(Long employeeId) {
        try {
            java.util.List<digitization.digitization.module.EducationQualification> educationList = educationQualificationRepository.findByEmployeeId(employeeId);
            return educationList.stream()
                .map(this::convertEducationToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            return new java.util.ArrayList<>();
        }
    }

    private java.util.List<digitization.digitization.dto.PunishmentDetailsDto> getPunishmentDetailsByEmployeeId(Long employeeId) {
        try {
            java.util.List<digitization.digitization.module.PunishmentDetails> punishmentList = punishmentDetailsRepository.findByEmployeeId(employeeId);
            return punishmentList.stream()
                .map(this::convertPunishmentToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            return new java.util.ArrayList<>();
        }
    }

    private java.util.List<digitization.digitization.dto.TrainingDetailsDto> getTrainingDetailsByEmployeeId(Long employeeId) {
        try {
            java.util.List<digitization.digitization.module.TrainingDetails> trainingList = trainingDetailsRepository.findByEmployeeId(employeeId);
            return trainingList.stream()
                .map(this::convertTrainingToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            return new java.util.ArrayList<>();
        }
    }

    private java.util.List<digitization.digitization.dto.NomineeDto> getNomineesByEmployeeId(Long employeeId) {
        try {
            java.util.List<digitization.digitization.module.Nominee> nomineeList = nomineeRepository.findByEmployeeId(employeeId);
            return nomineeList.stream()
                .map(this::convertNomineeToDto)
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            return new java.util.ArrayList<>();
        }
    }

    // Converter methods
    private digitization.digitization.dto.ServiceHistoryDto convertServiceHistoryToDto(digitization.digitization.module.ServiceHistory serviceHistory) {
        digitization.digitization.dto.ServiceHistoryDto dto = new digitization.digitization.dto.ServiceHistoryDto();
        dto.setId(serviceHistory.getId());
        dto.setDate(serviceHistory.getDate());
        dto.setType(serviceHistory.getType());
        dto.setStatus(serviceHistory.getStatus());
        dto.setAppointmenttype(serviceHistory.getAppointmenttype());
        dto.setModeofappointment(serviceHistory.getModeofappointment());
        dto.setDateofappointment(serviceHistory.getDateofappointment());
        dto.setProceedingorderdate(serviceHistory.getProceedingorderdate());
        dto.setProceedingorderno(serviceHistory.getProceedingorderno());
        dto.setJoiningdate(serviceHistory.getJoiningdate());
        dto.setPromoteddate(serviceHistory.getPromoteddate());
        dto.setFromdesignation(serviceHistory.getFromdesignation());
        dto.setTopromoted(serviceHistory.getTopromoted());
        dto.setFromdate(serviceHistory.getFromdate());
        dto.setTodate(serviceHistory.getTodate());
        dto.setTypeofincrement(serviceHistory.getTypeofincrement());
        dto.setFromplace(serviceHistory.getFromplace());
        dto.setToplace(serviceHistory.getToplace());
        dto.setDesignation(serviceHistory.getDesignation());
        dto.setOriginaldesignation(serviceHistory.getOriginaldesignation());
        dto.setParentdepartment(serviceHistory.getParentdepartment());
        dto.setPunishmenttype(serviceHistory.getPunishmenttype());
        dto.setCasedetails(serviceHistory.getCasedetails());
        dto.setPunishmentdate(serviceHistory.getPunishmentdate());
        return dto;
    }

    private digitization.digitization.dto.LeaveDto convertLeaveToDto(digitization.digitization.module.Leave leave) {
        digitization.digitization.dto.LeaveDto dto = new digitization.digitization.dto.LeaveDto();
        dto.setId(leave.getId());
        dto.setLeaveType(leave.getLeaveType());
        dto.setOpeningBalance(leave.getOpeningBalance());
        dto.setClosingBalance(leave.getClosingBalance());
        dto.setEntryDate(leave.getEntryDate());
        return dto;
    }

    private digitization.digitization.dto.EducationQualificationDto convertEducationToDto(digitization.digitization.module.EducationQualification education) {
        digitization.digitization.dto.EducationQualificationDto dto = new digitization.digitization.dto.EducationQualificationDto();
        dto.setId(education.getId());
        dto.setQualification(education.getQualification());
        dto.setCoursename(education.getCoursename());
        dto.setSchoolname(education.getSchoolname());
        dto.setCollegename(education.getCollegename());
        dto.setUniversityname(education.getUniversityname());
        return dto;
    }

    private digitization.digitization.dto.PunishmentDetailsDto convertPunishmentToDto(digitization.digitization.module.PunishmentDetails punishment) {
        digitization.digitization.dto.PunishmentDetailsDto dto = new digitization.digitization.dto.PunishmentDetailsDto();
        dto.setId(punishment.getId());
        dto.setPunishmenttype(punishment.getPunishmenttype());
        dto.setDate(punishment.getDate());
        dto.setCasedetails(punishment.getCasedetails());
        return dto;
    }

    private digitization.digitization.dto.TrainingDetailsDto convertTrainingToDto(digitization.digitization.module.TrainingDetails training) {
        digitization.digitization.dto.TrainingDetailsDto dto = new digitization.digitization.dto.TrainingDetailsDto();
        dto.setId(training.getId());
        dto.setTrainingtype(training.getTrainingtype());
        dto.setDate(training.getDate());
        return dto;
    }

    private digitization.digitization.dto.NomineeDto convertNomineeToDto(digitization.digitization.module.Nominee nominee) {
        digitization.digitization.dto.NomineeDto dto = new digitization.digitization.dto.NomineeDto();
        dto.setId(nominee.getId());
        dto.setNomineename(nominee.getNomineename());
        dto.setAddress(nominee.getAddress());
        dto.setRelationship(nominee.getRelationship());
        dto.setAge(nominee.getAge());
        dto.setPercentageofshare(nominee.getPercentageofshare());
        dto.setGender(nominee.getGender());

        // Convert filename to full URL for frontend usage
        if (nominee.getNomineePhoto() != null && !nominee.getNomineePhoto().trim().isEmpty()) {
            dto.setNomineePhoto("/api/files/" + nominee.getNomineePhoto());
        } else {
            dto.setNomineePhoto(null);
        }

        return dto;
    }

}
