package digitization.digitization.repository;

import digitization.digitization.module.Leave;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LeaveRepository extends JpaRepository<Leave,Long> {
    List<Leave> findByEmployeeId(Long employeeId);
    Optional<Leave> findByEmployeeIdAndLeaveType(Long employeeId, String leaveType);
}
