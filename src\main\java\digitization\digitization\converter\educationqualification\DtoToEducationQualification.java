package digitization.digitization.converter.educationqualification;

import digitization.digitization.dto.EducationQualificationDto;
import digitization.digitization.module.EducationQualification;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class DtoToEducationQualification implements Converter<EducationQualificationDto, EducationQualification> {
    @Override
    public EducationQualification convert(EducationQualificationDto educationQualificationDto) {
        EducationQualification educationQualification = new EducationQualification();
        educationQualification.setId(educationQualificationDto.getId());
        educationQualification.setQualification(educationQualificationDto.getQualification());
        educationQualification.setCoursename(educationQualificationDto.getCoursename());
        educationQualification.setSchoolname(educationQualificationDto.getSchoolname());
        educationQualification.setCollegename(educationQualificationDto.getCollegename());
        educationQualification.setUniversityname(educationQualificationDto.getUniversityname());
        educationQualification.setSpecialization(educationQualificationDto.getSpecialization());
        return educationQualification;
    }
}
