package digitization.digitization.converter.employeeStatus;

import digitization.digitization.dto.EmployeeStatusDto;
import digitization.digitization.module.EmployeeStatus;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class DtoToEmployeeStatus implements Converter<EmployeeStatusDto, EmployeeStatus> {

    @Override
    public EmployeeStatus convert(EmployeeStatusDto dto) {
        EmployeeStatus employeeStatus = new EmployeeStatus();
        employeeStatus.setId(dto.getId());
        employeeStatus.setEmpId(dto.getEmpId());
        employeeStatus.setStatus(dto.getStatus());
        employeeStatus.setApprovedBy(dto.getApprovedBy());
        employeeStatus.setRejectedBy(dto.getRejectedBy());
        employeeStatus.setRemarks(dto.getRemarks());
        employeeStatus.setCreatedAt(dto.getCreatedAt());
        employeeStatus.setUpdatedAt(dto.getUpdatedAt());
        return employeeStatus;
    }
}

