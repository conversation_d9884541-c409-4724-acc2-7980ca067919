package digitization.digitization.services.implementation;

import digitization.digitization.module.Profile;

import java.util.List;

public interface ProfileServ {
    List<Profile> getProfileList();
    Profile getProfileById(Long id);
    Profile createProfile(Profile profile);
    Profile updateProfile(Long id, Profile profileDetails);
    boolean deleteProfile(Long profileId);
    Profile getProfileByEmail(String email);
}
