package digitization.digitization.converter.accountdetails;

import digitization.digitization.dto.AccountDetailsDto;
import digitization.digitization.module.AccountDetails;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class AccountDetailsToDto implements Converter<AccountDetails, AccountDetailsDto> {
    @Override
    public AccountDetailsDto convert(AccountDetails accountDetails) {
        AccountDetailsDto accountDetailsDto = new AccountDetailsDto();
        accountDetailsDto.setId(accountDetails.getId());
        accountDetailsDto.setBankaccountnumber(accountDetails.getBankaccountnumber());
        accountDetailsDto.setIfsccode(accountDetails.getIfsccode());
        accountDetailsDto.setBankname(accountDetails.getBankname());
        accountDetailsDto.setUannumber(accountDetails.getUannumber());
        accountDetailsDto.setAadharnumber(accountDetails.getAadharnumber());
        return accountDetailsDto;
    }
}
