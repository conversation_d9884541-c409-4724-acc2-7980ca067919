package digitization.digitization.controller;

import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import digitization.digitization.dto.EmployeeCreationDto;
import digitization.digitization.dto.EmployeeListDto;
import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.Employee;
import digitization.digitization.services.EmployeeService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.Map;

@RestController
@CrossOrigin(value = "*")
@RequestMapping("employee")
public class EmployeeContro {
    @Autowired
    EmployeeService employeeService;

    @GetMapping("/getList")
    public List<EmployeeListDto> getEmployeeList(){
        return employeeService.getEmployeeListDto();
    }
    @GetMapping("/getList/{createdBy}")
    public List<EmployeeListDto> getEmployeeList(@PathVariable  String createdBy){
        return employeeService.getEmployeeListDtoCreated(createdBy);
    }

    @GetMapping("/getFullList")
    public List<Employee> getFullEmployeeList(){
        return employeeService.getEmployeeList();
    }

    @GetMapping("/getEmp/{id}")
    public ResponseEntity<digitization.digitization.dto.EmployeeDetailDto> getEmployeeById(@PathVariable Long id) throws Exception {
        digitization.digitization.dto.EmployeeDetailDto employeeDetail = employeeService.getEmployeeWithAllDetails(id);
        return ResponseEntity.ok(employeeDetail);
    }

    @GetMapping("/getEmpBasic/{id}")
    public ResponseEntity<Employee> getEmployeeBasicById(@PathVariable Long id) throws Exception {
        Employee employee = employeeService.getEmployeeById(id);

        if (employee == null) {
            throw new ResourceNotFoundException("Employee not found with id: " + id, null, null);
        }

        return ResponseEntity.ok(employee);
    }


    @PostMapping("/createEmp")
    public Employee createEmployee(@RequestBody Employee employee) {
        return employeeService.createEmployee(employee);
    }

    @PostMapping("/createEmployee")
    public ResponseEntity<Employee> createEmployeeWithRelatedRecords(@RequestBody EmployeeCreationDto employeeCreationDto) {
        Employee createdEmployee = employeeService.createEmployeeWithRelatedRecords(employeeCreationDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdEmployee);
    }

    @PostMapping(value = "/createEmployeeWithFiles", consumes = {"multipart/form-data"})
    public ResponseEntity<Employee> createEmployeeWithFiles(
            @RequestPart("employeeData") String employeeDataJson,
            @RequestParam(value = "profilePhoto", required = false) MultipartFile profilePhoto,
            @RequestParam(value = "nomineePhotos", required = false) MultipartFile[] nomineePhotos,
            @RequestParam(value = "documents", required = false) MultipartFile[] documents) {

        try {
            // Parse JSON data
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.findAndRegisterModules(); // For LocalDate support
            EmployeeCreationDto employeeCreationDto = objectMapper.readValue(employeeDataJson, EmployeeCreationDto.class);

            // Create employee with file uploads
            Employee createdEmployee = employeeService.createEmployeeWithFiles(
                employeeCreationDto, profilePhoto, nomineePhotos, documents);

            return ResponseEntity.status(HttpStatus.CREATED).body(createdEmployee);

        } catch (Exception e) {
            System.out.println("Error creating employee with files: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }



    @PutMapping(value = "/updateEmployeeWithFiles/{id}", consumes = {"multipart/form-data"})
    public ResponseEntity<Employee> updateEmployeeWithFiles(
            @PathVariable Long id,
            @RequestPart("employeeData") String employeeDataJson,
            @RequestParam(value = "profilePhoto", required = false) MultipartFile profilePhoto,
            @RequestParam(value = "nomineePhotos", required = false) MultipartFile[] nomineePhotos,
            @RequestParam(value = "documents", required = false) MultipartFile[] documents) {

        try {
            System.out.println("=== DEBUG: updateEmployeeWithFiles endpoint called for employee " + id + " ===");
            System.out.println("Employee data JSON: " + employeeDataJson);
            System.out.println("Profile photo provided: " + (profilePhoto != null && !profilePhoto.isEmpty()));
            System.out.println("Nominee photos provided: " + (nomineePhotos != null ? nomineePhotos.length : 0));
            System.out.println("Documents provided: " + (documents != null ? documents.length : 0));

            // Parse JSON to EmployeeCreationDto
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());
            objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

            EmployeeCreationDto employeeUpdateDto = objectMapper.readValue(employeeDataJson, EmployeeCreationDto.class);

            Employee updatedEmployee = employeeService.updateEmployeeWithFiles(id, employeeUpdateDto, profilePhoto, nomineePhotos, documents);
            return ResponseEntity.ok(updatedEmployee);

        } catch (Exception e) {
            System.err.println("Error in updateEmployeeWithFiles: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @PutMapping("/updateEmp/{id}")
    public ResponseEntity<Employee> updateEmployeeWithRelatedRecords(@PathVariable Long id, @RequestBody EmployeeCreationDto employeeUpdateDto) {
        System.out.println("=== EMPLOYEE UPDATE DEBUG ===");
        System.out.println("Employee ID: " + id);

        if (employeeUpdateDto.getServiceHistory() != null) {
            System.out.println("Service History count: " + employeeUpdateDto.getServiceHistory().size());
            for (int i = 0; i < employeeUpdateDto.getServiceHistory().size(); i++) {
                digitization.digitization.dto.ServiceHistoryDto dto = employeeUpdateDto.getServiceHistory().get(i);
                System.out.println("ServiceHistory[" + i + "] - ID: " + dto.getId());
                System.out.println("ServiceHistory[" + i + "] - Type: " + dto.getType());
                System.out.println("ServiceHistory[" + i + "] - involvedPersons string: '" + dto.getInvolvedPersons() + "'");
                System.out.println("ServiceHistory[" + i + "] - personsInvolved array: " + java.util.Arrays.toString(dto.getPersonsInvolved()));

                // Manual test - try setting the array manually to see if it works
                if (dto.getPersonsInvolved() == null || dto.getPersonsInvolved().length == 0) {
                    System.out.println("MANUAL TEST: Setting personsInvolved to [25, 10] manually");
                    dto.setPersonsInvolved(new Long[]{25L, 10L});
                    System.out.println("MANUAL TEST: After setting - involvedPersons string: '" + dto.getInvolvedPersons() + "'");
                    System.out.println("MANUAL TEST: After setting - personsInvolved array: " + java.util.Arrays.toString(dto.getPersonsInvolved()));
                }
            }
        } else {
            System.out.println("No service history in update request");
        }

        Employee updatedEmployee = employeeService.updateEmployeeWithRelatedRecords(id, employeeUpdateDto);
        return ResponseEntity.ok(updatedEmployee);
    }

    @PostMapping("/test-employee-json")
    public ResponseEntity<String> testEmployeeJsonDeserialization(@RequestBody EmployeeCreationDto employeeDto) {
        try {
            System.out.println("=== EMPLOYEE DTO JSON DESERIALIZATION TEST ===");

            if (employeeDto.getServiceHistory() != null) {
                System.out.println("Service History count: " + employeeDto.getServiceHistory().size());
                for (int i = 0; i < employeeDto.getServiceHistory().size(); i++) {
                    digitization.digitization.dto.ServiceHistoryDto dto = employeeDto.getServiceHistory().get(i);
                    System.out.println("ServiceHistory[" + i + "] - involvedPersons string: '" + dto.getInvolvedPersons() + "'");
                    System.out.println("ServiceHistory[" + i + "] - personsInvolved array: " + java.util.Arrays.toString(dto.getPersonsInvolved()));
                }
            } else {
                System.out.println("No service history in request");
            }

            return ResponseEntity.ok("Employee DTO JSON test completed - check console logs");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.ok("Employee DTO JSON test failed: " + e.getMessage());
        }
    }

    @GetMapping("/debug/nominee-photos/{employeeId}")
    public ResponseEntity<String> debugNomineePhotos(@PathVariable Long employeeId) {
        try {
            StringBuilder debug = new StringBuilder();
            debug.append("=== NOMINEE PHOTOS DEBUG FOR EMPLOYEE ").append(employeeId).append(" ===\n");

            // Get employee details with nominees
            digitization.digitization.dto.EmployeeDetailDto employeeDetail = employeeService.getEmployeeWithAllDetails(employeeId);

            if (employeeDetail != null && employeeDetail.getNominees() != null) {
                debug.append("Found ").append(employeeDetail.getNominees().size()).append(" nominees:\n");

                for (int i = 0; i < employeeDetail.getNominees().size(); i++) {
                    digitization.digitization.dto.NomineeDto nominee = employeeDetail.getNominees().get(i);
                    debug.append("Nominee ").append(i+1).append(":\n");
                    debug.append("  Name: ").append(nominee.getNomineename()).append("\n");
                    debug.append("  Photo: ").append(nominee.getNomineePhoto()).append("\n");
                    debug.append("  Relationship: ").append(nominee.getRelationship()).append("\n");
                    debug.append("\n");
                }
            } else {
                debug.append("No nominees found for employee ").append(employeeId).append("\n");
            }

            return ResponseEntity.ok(debug.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error: " + e.getMessage());
        }
    }

    @GetMapping("/debug/test-placeholder/{value}")
    public ResponseEntity<String> testPlaceholder(@PathVariable String value) {
        try {
            // This is a simple test to verify placeholder detection
            StringBuilder debug = new StringBuilder();
            debug.append("=== PLACEHOLDER TEST ===\n");
            debug.append("Input value: '").append(value).append("'\n");

            // Test various placeholder scenarios
            String[] testValues = {value, "pending_upload", "placeholder", "null", "undefined", "", "no_photo", "actual_photo.jpg"};

            for (String testValue : testValues) {
                boolean isPlaceholder = isPlaceholderValue(testValue);
                debug.append("Value: '").append(testValue).append("' -> Is placeholder: ").append(isPlaceholder).append("\n");
            }

            return ResponseEntity.ok(debug.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error: " + e.getMessage());
        }
    }

    private boolean isPlaceholderValue(String value) {
        if (value == null) return true;
        String cleanValue = value.trim().toLowerCase();
        return cleanValue.equals("pending_upload") ||
               cleanValue.equals("placeholder") ||
               cleanValue.equals("null") ||
               cleanValue.equals("undefined") ||
               cleanValue.equals("") ||
               cleanValue.equals("no_photo");
    }

    @PutMapping("/updateEmpBasic/{id}")
    public ResponseEntity<String> updateEmployeeBasic(@PathVariable Long id, @Valid @RequestBody Employee employeeDetails) {
        Employee updated = employeeService.updateEmployee(id, employeeDetails);

        if (updated != null) {
            return ResponseEntity.ok("Employee updated successfully");
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Employee not found");
        }
    }
    @DeleteMapping("/deleteEmp/{empId}")
    public ResponseEntity<String> deleteEmployee(@PathVariable Long empId) {
        try {
            boolean isDeleted = employeeService.deleteEmployee(empId);
            if (isDeleted) {
                return ResponseEntity.ok("Employee and all related records deleted successfully");
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Employee not found with ID: " + empId);
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error deleting employee: " + e.getMessage());
        }
    }

    @GetMapping("/getByCreatedBy/{createdBy}")
    public ResponseEntity<List<digitization.digitization.dto.EmployeeDetailDto>> getEmployeesByCreatedBy(@PathVariable String createdBy) {
        try {
            List<digitization.digitization.dto.EmployeeDetailDto> employees = employeeService.getEmployeesByCreatedBy(createdBy);
            return ResponseEntity.ok(employees);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/getByCreatedBy/{createdBy}/basic")
    public ResponseEntity<List<EmployeeListDto>> getEmployeesBasicByCreatedBy(@PathVariable String createdBy) {
        try {
            List<EmployeeListDto> employees = employeeService.getEmployeesBasicByCreatedBy(createdBy);
            return ResponseEntity.ok(employees);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }










}
