package digitization.digitization.controller;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.AccountDetails;
import digitization.digitization.services.AccountDetailsService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("accountdetails")
public class AccountDetailsController {
    
    @Autowired
    AccountDetailsService accountDetailsService;

    @GetMapping("/getlist")
    public List<AccountDetails> getAccountDetailsList() {
        return accountDetailsService.getAccountDetailsList();
    }

    @GetMapping("/getAccountDetails/{id}")
    public ResponseEntity<AccountDetails> getAccountDetailsById(@PathVariable Long id) throws Exception {
        AccountDetails accountDetails = accountDetailsService.getAccountDetailsById(id);

//        if (accountDetails == null) {
//            throw new ResourceNotFoundException("Account details not found with id: " + id);
//        }

        return ResponseEntity.ok(accountDetails);
    }

    @GetMapping("/getByBankAccount/{bankaccountnumber}")
    public ResponseEntity<AccountDetails> getAccountDetailsByBankAccountNumber(@PathVariable String bankaccountnumber) throws Exception {
        AccountDetails accountDetails = accountDetailsService.getAccountDetailsByBankAccountNumber(bankaccountnumber);

//        if (accountDetails == null) {
//            throw new ResourceNotFoundException("Account details not found with bank account number: " + bankaccountnumber);
//        }

        return ResponseEntity.ok(accountDetails);
    }

    @GetMapping("/getByAadhar/{aadharnumber}")
    public ResponseEntity<AccountDetails> getAccountDetailsByAadharNumber(@PathVariable String aadharnumber) throws Exception {
        AccountDetails accountDetails = accountDetailsService.getAccountDetailsByAadharNumber(aadharnumber);

//        if (accountDetails == null) {
//            throw new ResourceNotFoundException("Account details not found with Aadhar number: " + aadharnumber);
//        }

        return ResponseEntity.ok(accountDetails);
    }

    @GetMapping("/getByUan/{uannumber}")
    public ResponseEntity<AccountDetails> getAccountDetailsByUanNumber(@PathVariable String uannumber) throws Exception {
        AccountDetails accountDetails = accountDetailsService.getAccountDetailsByUanNumber(uannumber);

//        if (accountDetails == null) {
//            throw new ResourceNotFoundException("Account details not found with UAN number: " + uannumber);
//        }

        return ResponseEntity.ok(accountDetails);
    }

    @PostMapping("/createAccountDetails")
    public AccountDetails createAccountDetails(@Valid @RequestBody AccountDetails accountDetails) {
        return accountDetailsService.createAccountDetails(accountDetails);
    }

    @PutMapping("/updateAccountDetails/{id}")
    public AccountDetails updateAccountDetails(@PathVariable Long id, @Valid @RequestBody AccountDetails accountDetailsDetails) {
        return accountDetailsService.updateAccountDetails(id, accountDetailsDetails);
    }

    @DeleteMapping("/deleteAccountDetails/{id}")
    public ResponseEntity<?> deleteAccountDetails(@PathVariable Long id) {
        boolean deleted = accountDetailsService.deleteAccountDetails(id);
        if (deleted) {
            return ResponseEntity.ok().build();
        } else {
//            throw new ResourceNotFoundException("Account details not found with id: " + id);

            return ResponseEntity.badRequest().build();

        }
    }
}
