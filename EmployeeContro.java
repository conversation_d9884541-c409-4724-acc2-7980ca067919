package digitization.digitization.controller;

import digitization.digitization.dto.EmployeeCreationDto;
import digitization.digitization.dto.EmployeeListDto;
import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.Employee;
import digitization.digitization.services.EmployeeService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@CrossOrigin(value = "*")
@RequestMapping("employee")
public class EmployeeContro {
    @Autowired
    EmployeeService employeeService;

    @GetMapping("/getList")
    public List<EmployeeListDto> getEmployeeList(){
        return employeeService.getEmployeeListDto();
    }

    @GetMapping("/getFullList")
    public List<Employee> getFullEmployeeList(){
        return employeeService.getEmployeeList();
    }

    @GetMapping("/getEmp/{id}")
    public ResponseEntity<digitization.digitization.dto.EmployeeDetailDto> getEmployeeById(@PathVariable Long id) throws Exception {
        digitization.digitization.dto.EmployeeDetailDto employeeDetail = employeeService.getEmployeeWithAllDetails(id);
        return ResponseEntity.ok(employeeDetail);
    }

    @GetMapping("/getEmpBasic/{id}")
    public ResponseEntity<Employee> getEmployeeBasicById(@PathVariable Long id) throws Exception {
        Employee employee = employeeService.getEmployeeById(id);

        if (employee == null) {
            throw new ResourceNotFoundException("Employee not found with id: " + id, null, null);
        }

        return ResponseEntity.ok(employee);
    }


    @PostMapping("/createEmp")
    public Employee createEmployee(@RequestBody Employee employee) {
        return employeeService.createEmployee(employee);
    }

    @PostMapping("/createEmployee")
    public ResponseEntity<Employee> createEmployeeWithRelatedRecords(@RequestBody EmployeeCreationDto employeeCreationDto) {
        Employee createdEmployee = employeeService.createEmployeeWithRelatedRecords(employeeCreationDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdEmployee);
    }



    @PutMapping("/updateEmp/{id}")
    public ResponseEntity<Employee> updateEmployeeWithRelatedRecords(@PathVariable Long id, @RequestBody EmployeeCreationDto employeeUpdateDto) {
        Employee updatedEmployee = employeeService.updateEmployeeWithRelatedRecords(id, employeeUpdateDto);
        return ResponseEntity.ok(updatedEmployee);
    }

    @PutMapping("/updateEmpBasic/{id}")
    public ResponseEntity<String> updateEmployeeBasic(@PathVariable Long id, @Valid @RequestBody Employee employeeDetails) {
        Employee updated = employeeService.updateEmployee(id, employeeDetails);

        if (updated != null) {
            return ResponseEntity.ok("Employee updated successfully");
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Employee not found");
        }
    }
    @DeleteMapping("/deleteEmp/{empId}")
    public ResponseEntity<String> deleteEmployee(@PathVariable Long empId) {
        try {
            boolean isDeleted = employeeService.deleteEmployee(empId);
            if (isDeleted) {
                return ResponseEntity.ok("Employee and all related records deleted successfully");
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Employee not found with ID: " + empId);
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error deleting employee: " + e.getMessage());
        }
    }

    @GetMapping("/getByCreatedBy/{createdBy}")
    public ResponseEntity<List<digitization.digitization.dto.EmployeeDetailDto>> getEmployeesByCreatedBy(@PathVariable String createdBy) {
        try {
            List<digitization.digitization.dto.EmployeeDetailDto> employees = employeeService.getEmployeesByCreatedBy(createdBy);
            return ResponseEntity.ok(employees);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/getByCreatedBy/{createdBy}/basic")
    public ResponseEntity<List<EmployeeListDto>> getEmployeesBasicByCreatedBy(@PathVariable String createdBy) {
        try {
            List<EmployeeListDto> employees = employeeService.getEmployeesBasicByCreatedBy(createdBy);
            return ResponseEntity.ok(employees);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/getDetailsByCreatedBy/{createdBy}")
    public ResponseEntity<List<digitization.digitization.dto.EmployeeDetailDto>> getEmployeeDetailsByCreatedBy(@PathVariable String createdBy) {
        try {
            List<digitization.digitization.dto.EmployeeDetailDto> employees = employeeService.getEmployeeDetailsByCreatedBy(createdBy);
            return ResponseEntity.ok(employees);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }










}
