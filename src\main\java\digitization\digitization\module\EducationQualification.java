package digitization.digitization.module;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

@Entity
@Table(name = "education_qualification")
public class EducationQualification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

//    @NotBlank(message = "Qualification is required")
    private String qualification;

    private String coursename;

    private String schoolname;

    private String collegename;

    private String universityname;

    private String specialization;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "employee_id")
    private Employee employee;

    public EducationQualification() {
    }

    public EducationQualification(Long id, String qualification, String coursename, String schoolname, String collegename, String universityname, String specialization) {
        this.id = id;
        this.qualification = qualification;
        this.coursename = coursename;
        this.schoolname = schoolname;
        this.collegename = collegename;
        this.universityname = universityname;
        this.specialization = specialization;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQualification() {
        return qualification;
    }

    public void setQualification(String qualification) {
        this.qualification = qualification;
    }

    public String getCoursename() {
        return coursename;
    }

    public void setCoursename(String coursename) {
        this.coursename = coursename;
    }

    public String getSchoolname() {
        return schoolname;
    }

    public void setSchoolname(String schoolname) {
        this.schoolname = schoolname;
    }

    public String getCollegename() {
        return collegename;
    }

    public void setCollegename(String collegename) {
        this.collegename = collegename;
    }

    public String getUniversityname() {
        return universityname;
    }

    public void setUniversityname(String universityname) {
        this.universityname = universityname;
    }

    public String getSpecialization() {
        return specialization;
    }

    public void setSpecialization(String specialization) {
        this.specialization = specialization;
    }

    public Employee getEmployee() {
        return employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }
}
