package digitization.digitization.repository;

import digitization.digitization.module.TrainingDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface TrainingDetailsRepository extends JpaRepository<TrainingDetails, Long> {
    Optional<TrainingDetails> findById(Long id);
    List<TrainingDetails> findByTrainingtype(String trainingtype);
    List<TrainingDetails> findByDate(LocalDate date);
    List<TrainingDetails> findByDateBetween(LocalDate startDate, LocalDate endDate);
    List<TrainingDetails> findByDateAfter(LocalDate date);
    List<TrainingDetails> findByDateBefore(LocalDate date);

    @Query("SELECT t FROM TrainingDetails t WHERE t.trainingtype LIKE %:keyword%")
    List<TrainingDetails> findByTrainingTypeContaining(@Param("keyword") String keyword);

    List<TrainingDetails> findByOrderByDateDesc();
    List<TrainingDetails> findByOrderByDateAsc();
    List<TrainingDetails> findByTrainingtypeOrderByDateDesc(String trainingtype);
    List<TrainingDetails> findByTrainingtypeOrderByDateAsc(String trainingtype);

    @Query("SELECT DISTINCT t.trainingtype FROM TrainingDetails t ORDER BY t.trainingtype")
    List<String> findDistinctTrainingTypes();

    List<TrainingDetails> findByEmployeeId(Long employeeId);
}
