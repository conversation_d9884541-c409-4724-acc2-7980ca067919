package digitization.digitization.security;

import digitization.digitization.module.User;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Component
public class UserDetailsImpl implements UserDetails {

    private static final Long serialVersionUID = 6L;

    private Long id;
    private String username;
    private String password;
    private String email;
    private Boolean isActive;
    private boolean isAccountLocked;
    private LocalDateTime forgottenPasswordTime;
    private  String forgottenPasswordCode;
    private Collection<? extends GrantedAuthority> authorities;
    private String userType;
    private String userId;

    public UserDetailsImpl(String username, String password, String email, boolean isActive, List<GrantedAuthority> authorities, String userId, boolean isAccountLocked) {
        this.username = username;
        this.password = password;
        this.email = email;
        this.isActive = isActive;
        this.isAccountLocked = isAccountLocked;

        this.authorities = authorities;
        this.userId = userId;
    }

    public UserDetailsImpl() {
    }

    //    public static UserDetailsImpl build(User user) {
//        List<GrantedAuthority> authorities = Collections.singletonList(
//                new SimpleGrantedAuthority("ROLE_USER")
//        );
//
//        return new UserDetailsImpl(
//                user.getUsername(),
//                user.getPassword(),
//                user.getEmail(),
//                user.isActive(),
//                authorities,
//                user.getUserId()
//        );
//    }

    public  static UserDetailsImpl build(User user){
        List<GrantedAuthority> authorities;

        if (user.getRole() != null && !user.getRole().isEmpty()) {
            // Convert list of roles to authorities
            authorities = user.getRole().stream()
                    .map(role -> new SimpleGrantedAuthority("ROLE_" + role.getRole().name()))
                    .collect(java.util.stream.Collectors.toList());
        } else {
            // Default authority if no roles
            authorities = Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"));
        }

        return new UserDetailsImpl(user.getUsername(), user.getPassword(),user.getEmail(),user.isActive(),authorities,user.getUserId(),user.isAccountLocked());
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return this.authorities;
    }

    @Override
    public String getPassword() {
        return this.password;
    }

    @Override
    public String getUsername() {
        return this.username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return this.isActive;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    @Override
    public boolean isAccountNonLocked() {
        return !this.isAccountLocked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    public LocalDateTime getForgottenPasswordTime() {
        return forgottenPasswordTime;
    }

    public void setForgottenPasswordTime(LocalDateTime forgottenPasswordTime) {
        this.forgottenPasswordTime = forgottenPasswordTime;
    }

    public String getForgottenPasswordCode() {
        return forgottenPasswordCode;
    }

    public void setForgottenPasswordCode(String forgottenPasswordCode) {
        this.forgottenPasswordCode = forgottenPasswordCode;
    }
}
