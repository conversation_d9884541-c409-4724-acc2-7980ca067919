package digitization.digitization.dto;

public class FileStorageResult {
    
    private String relativePath;
    private String fileUrl;
    private String absolutePath;

    public FileStorageResult() {
    }

    public FileStorageResult(String relativePath, String fileUrl, String absolutePath) {
        this.relativePath = relativePath;
        this.fileUrl = fileUrl;
        this.absolutePath = absolutePath;
    }

    public String getRelativePath() {
        return relativePath;
    }

    public void setRelativePath(String relativePath) {
        this.relativePath = relativePath;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getAbsolutePath() {
        return absolutePath;
    }

    public void setAbsolutePath(String absolutePath) {
        this.absolutePath = absolutePath;
    }
}
