package digitization.digitization.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class LeaveDto {
    private Long id;
    private String leaveType;
    private Integer openingBalance;
    private Integer closingBalance;
    private LocalDate entryDate;
    private LocalDateTime createdAt;

    public LeaveDto() {
    }

    public LeaveDto(Long id, String leaveType, Integer openingBalance, Integer closingBalance, LocalDate entryDate, LocalDateTime createdAt) {
        this.id = id;
        this.leaveType = leaveType;
        this.openingBalance = openingBalance;
        this.closingBalance = closingBalance;
        this.entryDate = entryDate;
        this.createdAt = createdAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(String leaveType) {
        this.leaveType = leaveType;
    }

    public Integer getOpeningBalance() {
        return openingBalance;
    }

    public void setOpeningBalance(Integer openingBalance) {
        this.openingBalance = openingBalance;
    }

    public Integer getClosingBalance() {
        return closingBalance;
    }

    public void setClosingBalance(Integer closingBalance) {
        this.closingBalance = closingBalance;
    }

    public LocalDate getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(LocalDate entryDate) {
        this.entryDate = entryDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}
