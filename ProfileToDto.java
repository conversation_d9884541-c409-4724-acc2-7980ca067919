package digitization.digitization.converter.profile;

import digitization.digitization.dto.ProfileDto;
import digitization.digitization.module.Profile;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class ProfileToDto implements Converter<Profile, ProfileDto> {
    @Override
    public ProfileDto convert(Profile profile) {
        ProfileDto profileDto = new ProfileDto();
        profileDto.setId(profile.getId());
        profileDto.setFatherName(profile.getFatherName());
        profileDto.setMotherName(profile.getMotherName());
        profileDto.setDateOfBirth(profile.getDateOfBirth());
        profileDto.setCommunity(profile.getCommunity());
        profileDto.setCaste(profile.getCaste());
        profileDto.setDistrict(profile.getDistrict());
        profileDto.setNativeplaceandtaluk(profile.getNativeplaceandtaluk());
        profileDto.setEmail(profile.getEmail());
        return profileDto;
    }
}
