package digitization.digitization.services;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.EducationQualification;
import digitization.digitization.repository.EducationQualificationRepository;
import digitization.digitization.services.implementation.EducationQualificationServ;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class EducationQualificationService implements EducationQualificationServ {
    
    @Autowired
    EducationQualificationRepository educationQualificationRepository;

    @Override
    public List<EducationQualification> getEducationQualificationList() {
        List<EducationQualification> list = educationQualificationRepository.findAll();
        return list;
    }

    @Override
    public EducationQualification getEducationQualificationById(Long educationQualificationId) {
        Optional<EducationQualification> optionalEducationQualification = this.educationQualificationRepository.findById(educationQualificationId);
        if (!optionalEducationQualification.isPresent()) {
            throw new ResourceNotFoundException("Education qualification not found with id: " + educationQualificationId, null, null);
        }
        return optionalEducationQualification.get();
    }

    @Override
    public EducationQualification createEducationQualification(EducationQualification educationQualification) {
        EducationQualification eduQual = new EducationQualification();
        eduQual.setId(educationQualification.getId());
        eduQual.setQualification(educationQualification.getQualification());
        eduQual.setCoursename(educationQualification.getCoursename());
        eduQual.setSchoolname(educationQualification.getSchoolname());
        eduQual.setCollegename(educationQualification.getCollegename());
        eduQual.setUniversityname(educationQualification.getUniversityname());

        educationQualificationRepository.save(eduQual);
        return eduQual;
    }

    @Override
    public EducationQualification updateEducationQualification(Long id, EducationQualification educationQualificationDetails) {
        EducationQualification educationQualification = educationQualificationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Education qualification not found with id: " + id, null, null));

        educationQualification.setQualification(educationQualificationDetails.getQualification());
        educationQualification.setCoursename(educationQualificationDetails.getCoursename());
        educationQualification.setSchoolname(educationQualificationDetails.getSchoolname());
        educationQualification.setCollegename(educationQualificationDetails.getCollegename());
        educationQualification.setUniversityname(educationQualificationDetails.getUniversityname());

        return educationQualificationRepository.save(educationQualification);
    }

    @Override
    public boolean deleteEducationQualification(Long educationQualificationId) {
        try {
            Optional<EducationQualification> educationQualification = educationQualificationRepository.findById(educationQualificationId);
            if (educationQualification.isPresent()) {
                educationQualificationRepository.deleteById(educationQualificationId);
                return true;
            } else {
                System.out.println("Education qualification not found with ID: " + educationQualificationId);
                return false;
            }
        } catch (Exception e) {
            System.out.println("Error deleting education qualification: " + e.getMessage());
            return false;
        }
    }

    @Override
    public List<EducationQualification> getEducationQualificationsByQualification(String qualification) {
        return educationQualificationRepository.findByQualification(qualification);
    }

    @Override
    public List<EducationQualification> getEducationQualificationsByCourseName(String coursename) {
        return educationQualificationRepository.findByCoursename(coursename);
    }

    @Override
    public List<EducationQualification> getEducationQualificationsBySchoolName(String schoolname) {
        return educationQualificationRepository.findBySchoolname(schoolname);
    }

    @Override
    public List<EducationQualification> getEducationQualificationsByCollegeName(String collegename) {
        return educationQualificationRepository.findByCollegename(collegename);
    }

    @Override
    public List<EducationQualification> getEducationQualificationsByUniversityName(String universityname) {
        return educationQualificationRepository.findByUniversityname(universityname);
    }
}
