package digitization.digitization.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class UserBlockedException extends RuntimeException {

    private String message;


    /**
     * @param  msg       exception message
     * */
    public UserBlockedException(String msg){
        message = msg;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}