package digitization.digitization.controller;

import digitization.digitization.exception.ResourceNotFoundException;
import digitization.digitization.module.Nominee;
import digitization.digitization.services.NomineeService;
import digitization.digitization.services.DocumentService;
import digitization.digitization.services.FileStorageService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("nominee")
public class NomineeController {

    @Autowired
    NomineeService nomineeService;

    @Autowired
    DocumentService documentService;

    @Autowired
    FileStorageService fileStorageService;

    @GetMapping("/getlist")
    public List<Nominee> getNomineeList() {
        return nomineeService.getNomineeList();
    }

    @GetMapping("/getNominee/{id}")
    public ResponseEntity<Nominee> getNomineeById(@PathVariable Long id) throws Exception {
        Nominee nominee = nomineeService.getNomineeById(id);

        if (nominee == null) {
            throw new ResourceNotFoundException("Nominee not found with id: " + id, null, null);
        }

        return ResponseEntity.ok(nominee);
    }

    @GetMapping("/getByName/{nomineename}")
    public List<Nominee> getNomineesByName(@PathVariable String nomineename) {
        return nomineeService.getNomineesByName(nomineename);
    }

    @GetMapping("/getByRelationship/{relationship}")
    public List<Nominee> getNomineesByRelationship(@PathVariable String relationship) {
        return nomineeService.getNomineesByRelationship(relationship);
    }

    @GetMapping("/getByGender/{gender}")
    public List<Nominee> getNomineesByGender(@PathVariable String gender) {
        return nomineeService.getNomineesByGender(gender);
    }

    @GetMapping("/getByAge/{age}")
    public List<Nominee> getNomineesByAge(@PathVariable Integer age) {
        return nomineeService.getNomineesByAge(age);
    }

    @GetMapping("/getByAgeRange")
    public List<Nominee> getNomineesByAgeRange(@RequestParam Integer minAge, @RequestParam Integer maxAge) {
        return nomineeService.getNomineesByAgeRange(minAge, maxAge);
    }

    @GetMapping("/getByPercentageShare/{percentage}")
    public List<Nominee> getNomineesByPercentageShare(@PathVariable Double percentage) {
        return nomineeService.getNomineesByPercentageShare(percentage);
    }

    @GetMapping("/getWithShareGreaterThan/{percentage}")
    public List<Nominee> getNomineesWithShareGreaterThan(@PathVariable Double percentage) {
        return nomineeService.getNomineesWithShareGreaterThan(percentage);
    }

    @GetMapping("/searchByName/{name}")
    public List<Nominee> searchNomineesByName(@PathVariable String name) {
        return nomineeService.searchNomineesByName(name);
    }

    @GetMapping("/searchByAddress/{keyword}")
    public List<Nominee> searchNomineesByAddress(@PathVariable String keyword) {
        return nomineeService.searchNomineesByAddress(keyword);
    }

    @GetMapping("/getByRelationshipAndGender")
    public List<Nominee> getNomineesByRelationshipAndGender(@RequestParam String relationship, @RequestParam String gender) {
        return nomineeService.getNomineesByRelationshipAndGender(relationship, gender);
    }

    @GetMapping("/getAllOrderByShare")
    public List<Nominee> getAllNomineesOrderByShare() {
        return nomineeService.getAllNomineesOrderByShare();
    }

    @GetMapping("/getAllOrderByName")
    public List<Nominee> getAllNomineesOrderByName() {
        return nomineeService.getAllNomineesOrderByName();
    }

    @PostMapping("/createNominee")
    public Nominee createNominee(@Valid @RequestBody Nominee nominee) {
        return nomineeService.createNominee(nominee);
    }

    @PutMapping("/updateNominee/{id}")
    public Nominee updateNominee(@PathVariable Long id, @Valid @RequestBody Nominee nomineeDetails) {
        return nomineeService.updateNominee(id, nomineeDetails);
    }

    @DeleteMapping("/deleteNominee/{id}")
    public ResponseEntity<?> deleteNominee(@PathVariable Long id) {
        boolean deleted = nomineeService.deleteNominee(id);
        if (deleted) {
            return ResponseEntity.ok().build();
        } else {
            throw new ResourceNotFoundException("Nominee not found with id: " + id, null, null);
        }
    }

    @PostMapping("/uploadNomineePhoto/{nomineeId}")
    public ResponseEntity<String> uploadNomineePhoto(@PathVariable Long nomineeId, @RequestParam("file") MultipartFile file) {
        try {
            // Get nominee to validate it exists and get employee ID
            Nominee nominee = nomineeService.getNomineeById(nomineeId);
            if (nominee == null) {
                throw new ResourceNotFoundException("Nominee not found with id: " + nomineeId, null, null);
            }

            Long employeeId = nominee.getEmployee().getId();

            // Use the same logic as profile photos - store file using FileStorageService
            digitization.digitization.dto.FileStorageResult storageResult = fileStorageService.storeFile(file, employeeId);

            // Store file using DocumentService and create document record
            digitization.digitization.dto.DocumentDto documentDto = documentService.uploadDocument(file, employeeId);

            // Update nominee photo with the relative path (same format as profile photos)
            nominee.setNomineePhoto(storageResult.getRelativePath());
            nomineeService.updateNominee(nomineeId, nominee);

            // Return the full URL for frontend usage (same format as profile photos)
            return ResponseEntity.ok(storageResult.getFileUrl());
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error uploading nominee photo: " + e.getMessage());
        }
    }
}
