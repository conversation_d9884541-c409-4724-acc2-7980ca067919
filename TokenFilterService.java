package digitization.digitization.security;

import digitization.digitization.exception.UnAuthException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Optional;

@Component
public class TokenFilterService extends OncePerRequestFilter {

    @Autowired
    private TokenUtilityService tokenUtil;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Value("${jwt.header}")
    private String tokenHeader;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String path = request.getRequestURI();
        String method = request.getMethod();

//        logger.info("Processing request: {} {}", method, path);

        // Skip token validation for public endpoints
        if (isPublicPath(path)) {
//            logger.info("Skipping authentication for public path: {}", path);
            filterChain.doFilter(request, response);
            return;
        }

//        logger.info("Applying authentication for protected path: {}", path);

        try {
            Optional<String> tokenBody = getAuthTokenBody(request);

            if (tokenBody.isPresent() && tokenUtil.validateAuthToken(tokenBody.get())) {
                if (tokenUtil.isTokenExpired(tokenBody.get())) {
                    throw new UnAuthException(false, "Token Not valid with this browser", "Cannot get user authentication");
                }
                String authToken = tokenBody.get();
                String userName = tokenUtil.getUserNameFromAuthToken(authToken);
                Date tokenDate = tokenUtil.getCreatedDateFromAuthToken(authToken);
                String userAgent = request.getHeader("user-agent");
                String tokenAgent = tokenUtil.getUseragentFormToken(authToken);
                if (!userAgent.equalsIgnoreCase(tokenAgent)) {
                    throw new UnAuthException(false, "Token Not valid with this browser", "Cannot get user authentication");
                }
                LocalDateTime tokenLocalDate = LocalDateTime.ofInstant(tokenDate.toInstant(), ZoneId.systemDefault());
                UserDetails userDetails = userDetailsService.loadUserByUsername(userName);
                if (userDetails instanceof UserDetailsImpl) {
                    UserDetailsImpl userDetailsImpl = (UserDetailsImpl) userDetails;
                    if (userDetailsImpl.getForgottenPasswordCode() != null) {
                        if (userDetailsImpl.getForgottenPasswordTime() != null && !tokenLocalDate.isAfter(userDetailsImpl.getForgottenPasswordTime())) {
                            throw new UnAuthException(false, "UserName Not found", "Cannot get user authentication");
                        }
                    }
                }
                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(
                        userDetails, null, userDetails.getAuthorities());
                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            }
        } catch (Exception e) {
            logger.error("Cannot get user authentication: ", e);
            // Don't throw exception, just continue without authentication
        }

        filterChain.doFilter(request, response);
    }

    private boolean isPublicPath(String path) {
        return path.startsWith("/auth/") ||
               path.startsWith("/user/") ||
               path.startsWith("/role/") ||
               path.startsWith("/dashboard/") ||
               path.startsWith("/api/files/") ||
               path.equals("/error") ||
               path.equals("/favicon.ico");
    }

    private Optional<String> getAuthTokenBody(HttpServletRequest request) {
        String tokenHeaderString = request.getHeader(this.tokenHeader);
        if (tokenHeaderString != null && tokenHeaderString.startsWith("Bearer ")) {
            return Optional.of(tokenHeaderString.substring(7));
        }
        return Optional.empty();
    }
}
