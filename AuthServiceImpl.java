package digitization.digitization.auth;

import digitization.digitization.common.ApiResponseBean;
import digitization.digitization.common.HttpRequestExtractor;
import digitization.digitization.dto.authDto.AuthDto;
import digitization.digitization.dto.authDto.EmployeeAuthDto;
import digitization.digitization.enums.Roles;
import digitization.digitization.module.Employee;
import digitization.digitization.module.Role;
import digitization.digitization.module.User;
import digitization.digitization.repository.EmployeeRepo;
import digitization.digitization.repository.UserRepository;
import digitization.digitization.security.TokenUtilityService;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class AuthServiceImpl implements AuthService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmployeeRepo employeeRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private HttpRequestExtractor httpRequestExtractor;
    @Autowired
    private TokenUtilityService tokenUtilityService;



    @Override
    public ApiResponseBean authenticate(AuthDto authDto, HttpServletRequest request) {

        ApiResponseBean  apiResponseBean = new ApiResponseBean();
        Optional<User> userOptional = userRepository.findByUsername(authDto.getUsername());

        if (!userOptional.isPresent()) {
            apiResponseBean.setResponseType("INVALID_USERNAME");
            return apiResponseBean;
        }
        User user = userOptional.get();

        if (!passwordEncoder.matches(authDto.getPassword(), user.getPassword())) {

            apiResponseBean.setResponseType("INVALID_PASSWORD");
            String ip = httpRequestExtractor.getClientIp();
            User updatedUser =   userRepository.save(user);
            return apiResponseBean;
        }

        List<Roles> roleNames = new ArrayList<>();
        List<String> roleIdList = new ArrayList<>();


        Boolean isAdmin =Boolean.FALSE;
        Boolean isSalesManager = Boolean.FALSE;

        int count = 0;
        for (Role role : user.getRole()) {
            roleNames.add(role.getRole());
            roleIdList.add(role.getId().toString());
            if (role.getRole().equals(0)) {
                isAdmin = Boolean.TRUE;
            }
            if (role.getRole().equals(1)) {
                isSalesManager = Boolean.TRUE;
            }

            count++;
        }

//    if(isAdmin == null && isSalesManager == null) {
//        Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(authDto.getUsername(), authDto.getPassword()));
//        SecurityContextHolder.getContext().setAuthentication(authentication);
//        String userAgent = request.getHeader("user-agent");
//        String jwtToken = tokenUtilityService.genarateAuthToken(authentication, userAgent);
//        String ip = httpRequestExtractor.getClientIp();
//        LocalDateTime lastLogin = LocalDateTime.now();
//
//        User updatedUser = userRepository.save(user);
//        apiResponseBean.setResponseType("SUCCESS");
//        apiResponseBean.setResponseData(jwtToken);
//            apiResponseBean.setUserData(updatedUser.getUserId());
//        apiResponseBean.setUserData(updatedUser);
//            apiResponseBean.setRole(updatedUser.getRole().get(0).getRole());
//
//    }

        Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(authDto.getUsername(), authDto.getPassword()));
        SecurityContextHolder.getContext().setAuthentication(authentication);
        String userAgent = request.getHeader("user-agent");
        String jwtToken = tokenUtilityService.genarateAuthToken(authentication, userAgent);
        String ip = httpRequestExtractor.getClientIp();
        LocalDateTime lastLogin = LocalDateTime.now();
//            loginHandler.success(ip);

        User updatedUser = userRepository.save(user);
        apiResponseBean.setResponseType("SUCCESS");
        apiResponseBean.setResponseData(jwtToken);
        apiResponseBean.setUserData(updatedUser.getUserId());
        apiResponseBean.setRole(updatedUser.getRole().get(0).getRole());
        System.out.println(updatedUser.getRole().get(0).getRole());



//        if(isSalesManager){
//            Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(authDto.getUsername(), authDto.getPassword()));
//            SecurityContextHolder.getContext().setAuthentication(authentication);
//            String userAgent = request.getHeader("user-agent");
//            String jwtToken = tokenUtilityService.genarateAuthToken(authentication, userAgent);
//            String ip = httpRequestExtractor.getClientIp();
//            LocalDateTime lastLogin = LocalDateTime.now();
////            loginHandler.success(ip);
//
//            User updatedUser = userRepository.save(user);
//            apiResponseBean.setResponseType("SUCCESS");
//            apiResponseBean.setResponseData(jwtToken);
//            apiResponseBean.setUserData(updatedUser.getUserId());
//            apiResponseBean.setRole(updatedUser.getRole().get(0).getRole());
//        }

        return apiResponseBean;
    }

    @Override
    public ApiResponseBean employeeLogin(EmployeeAuthDto token) {

        ApiResponseBean apiResponseBean = new ApiResponseBean();
        Optional<Employee> request = employeeRepository.findByEmpIdAndPanNumber(
                token.getEmpId(),
                token.getPanNumber()
        );

        if (request.isPresent()) {
            apiResponseBean.setResponseType("LOGIN SUCCESS");
            apiResponseBean.setResponseData(request.get()); // return actual Employee data
        } else {
            apiResponseBean.setResponseType("LOGIN FAILED");
            apiResponseBean.setResponseData(null);
        }

        return apiResponseBean;
    }



}
