-- Fix for employee_status table emp_id column type mismatch
-- Run this SQL script in your PostgreSQL database

-- Step 1: Check current data type of emp_id column
SELECT column_name, data_type, character_maximum_length 
FROM information_schema.columns 
WHERE table_name = 'employee_status' AND column_name = 'emp_id';

-- Step 2: Check if there are any existing records
SELECT COUNT(*) FROM employee_status;

-- Step 3: If there are existing records, backup the data first
CREATE TABLE employee_status_backup AS SELECT * FROM employee_status;

-- Step 4: Fix the emp_id column data type
-- Option A: If the column contains numeric data that can be converted
ALTER TABLE employee_status ALTER COLUMN emp_id TYPE bigint USING emp_id::bigint;

-- Option B: If the above fails and you have string data, try this approach
-- First, add a new column
-- ALTER TABLE employee_status ADD COLUMN emp_id_new bigint;

-- Update the new column with converted values (only if data is numeric)
-- UPDATE employee_status SET emp_id_new = emp_id::bigint WHERE emp_id ~ '^[0-9]+$';

-- Drop the old column and rename the new one
-- ALTER TABLE employee_status DROP COLUMN emp_id;
-- ALTER TABLE employee_status RENAME COLUMN emp_id_new TO emp_id;

-- Step 5: Add the created_by column if it doesn't exist
ALTER TABLE employee_status ADD COLUMN IF NOT EXISTS created_by VARCHAR(255);

-- Step 6: Verify the changes
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'employee_status' 
ORDER BY ordinal_position;

-- Step 7: If everything looks good, you can drop the backup table
-- DROP TABLE employee_status_backup;
