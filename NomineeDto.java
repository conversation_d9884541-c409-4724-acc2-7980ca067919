package digitization.digitization.dto;

public class NomineeDto {

    private Long id;
    private String nomineename;
    private String address;
    private String relationship;
    private Integer age;
    private Double percentageofshare;
    private String gender;
    private String nomineePhoto;

    public NomineeDto() {
    }

    public NomineeDto(Long id, String nomineename, String address, String relationship, Integer age, Double percentageofshare, String gender) {
        this.id = id;
        this.nomineename = nomineename;
        this.address = address;
        this.relationship = relationship;
        this.age = age;
        this.percentageofshare = percentageofshare;
        this.gender = gender;
    }

    public NomineeDto(Long id, String nomineename, String address, String relationship, Integer age, Double percentageofshare, String gender, String nomineePhoto) {
        this.id = id;
        this.nomineename = nomineename;
        this.address = address;
        this.relationship = relationship;
        this.age = age;
        this.percentageofshare = percentageofshare;
        this.gender = gender;
        this.nomineePhoto = nomineePhoto;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNomineename() {
        return nomineename;
    }

    public void setNomineename(String nomineename) {
        this.nomineename = nomineename;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Double getPercentageofshare() {
        return percentageofshare;
    }

    public void setPercentageofshare(Double percentageofshare) {
        this.percentageofshare = percentageofshare;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getNomineePhoto() {
        return nomineePhoto;
    }

    public void setNomineePhoto(String nomineePhoto) {
        this.nomineePhoto = nomineePhoto;
    }
}
