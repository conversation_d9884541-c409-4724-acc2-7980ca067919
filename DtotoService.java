package digitization.digitization.converter.service;

import digitization.digitization.dto.ServiceHistoryDto;
import digitization.digitization.module.ServiceHistory;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class DtotoService implements Converter<ServiceHistoryDto, ServiceHistory> {
    @Override
    public ServiceHistory convert(ServiceHistoryDto serv) {
        ServiceHistory serviceHistory=new ServiceHistory();
        serviceHistory.setId(serv.getId());
        serviceHistory.setDate(serv.getDate());
        serviceHistory.setType(serv.getType());
        serviceHistory.setStatus(serv.getStatus());
        serviceHistory.setAppointmenttype(serv.getAppointmenttype());
        serviceHistory.setModeofappointment(serv.getModeofappointment());
        serviceHistory.setDateofappointment(serv.getDateofappointment());
        serviceHistory.setProceedingorderdate(serv.getProceedingorderdate());
        serviceHistory.setJoiningdate(serv.getJoiningdate());
        serviceHistory.setPromoteddate(serv.getPromoteddate());
        serviceHistory.setFromdesignation(serv.getFromdesignation());
        serviceHistory.setTopromoted(serv.getTopromoted());
        serviceHistory.setFromdate(serv.getFromdate());
        serviceHistory.setTodate(serv.getTodate());
        serviceHistory.setTypeofincrement(serv.getTypeofincrement());
        serviceHistory.setFromplace(serv.getFromplace());
        serviceHistory.setToplace(serv.getToplace());
        return serviceHistory;
    }
}
