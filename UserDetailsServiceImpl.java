package digitization.digitization.security;

import digitization.digitization.common.HttpRequestExtractor;
import digitization.digitization.module.User;
import digitization.digitization.repository.UserRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;


@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    @Autowired
    private UserRepository userRepo;

    @Autowired
    private HttpRequestExtractor httpRequestExtractor;
    @Override
    @Transactional
    public UserDetailsImpl loadUserByUsername(String userName) {//
        User user = userRepo.findByUsername(userName).orElseThrow(() -> new UsernameNotFoundException("UserName or Password wrong "));
        return UserDetailsImpl.build(user);
    }
}